import {
  ErrorBoundary,
  ProConfigProvider,
  ProProvider,
  compareVersions,
  compatibleBorder,
  coverToNewToken,
  isBrowser,
  isImg,
  isNeedOpenHash,
  isUrl,
  merge,
  omitUndefined,
  openVisibleCompatible,
  operationUnit,
  resetComponent,
  setAlpha,
  useBreakpoint,
  useDebounceFn,
  useDocumentTitle,
  useSWR,
  useSWRConfig,
  useStyle,
  useToken,
  useUrlSearchParams
} from "./chunk-JF54ERNK.js";
import {
  Keyframes_default,
  _asyncToGenerator,
  _classCallCheck,
  _createClass,
  _regeneratorRuntime,
  _toConsumableArray,
  affix_default,
  alert_default,
  avatar_default,
  breadcrumb_default,
  button_default,
  card_default,
  config_provider_default,
  divider_default,
  drawer_default,
  es_default,
  image_default,
  layout_default,
  list_default,
  menu_default,
  message_default,
  modal_default,
  omit,
  popover_default,
  select_default,
  skeleton_default,
  space_default,
  spin_default,
  switch_default,
  tabs_default,
  tooltip_default,
  typography_default,
  useMergedState,
  version_default
} from "./chunk-26W7M2TV.js";
import {
  require_jsx_runtime
} from "./chunk-YUEMZWDV.js";
import {
  CheckOutlined_default,
  CloseOutlined_default,
  CopyOutlined_default,
  CopyrightOutlined_default,
  MenuOutlined_default,
  NotificationOutlined_default,
  ProfileOutlined_default,
  SearchOutlined_default,
  SettingOutlined_default,
  _defineProperty,
  _objectSpread2,
  _objectWithoutProperties,
  _slicedToArray,
  _typeof,
  create,
  es_exports,
  init_defineProperty,
  init_es2 as init_es,
  init_typeof,
  noteOnce,
  require_classnames,
  warning_default
} from "./chunk-V74HSD5Q.js";
import {
  require_react_dom
} from "./chunk-TLVXLPKY.js";
import {
  __commonJS,
  __toCommonJS,
  __toESM,
  require_react
} from "./chunk-5A2KPB3J.js";

// node_modules/@babel/runtime/helpers/typeof.js
var require_typeof = __commonJS({
  "node_modules/@babel/runtime/helpers/typeof.js"(exports, module) {
    function _typeof3(o) {
      "@babel/helpers - typeof";
      return module.exports = _typeof3 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
        return typeof o2;
      } : function(o2) {
        return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof3(o);
    }
    module.exports = _typeof3, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/interopRequireWildcard.js
var require_interopRequireWildcard = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireWildcard.js"(exports, module) {
    var _typeof3 = require_typeof()["default"];
    function _interopRequireWildcard(e, t) {
      if ("function" == typeof WeakMap)
        var r = /* @__PURE__ */ new WeakMap(), n = /* @__PURE__ */ new WeakMap();
      return (module.exports = _interopRequireWildcard = function _interopRequireWildcard2(e2, t2) {
        if (!t2 && e2 && e2.__esModule)
          return e2;
        var o, i, f = {
          __proto__: null,
          "default": e2
        };
        if (null === e2 || "object" != _typeof3(e2) && "function" != typeof e2)
          return f;
        if (o = t2 ? n : r) {
          if (o.has(e2))
            return o.get(e2);
          o.set(e2, f);
        }
        for (var _t in e2)
          "default" !== _t && {}.hasOwnProperty.call(e2, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e2, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e2[_t]);
        return f;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports)(e, t);
    }
    module.exports = _interopRequireWildcard, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/extends.js
var require_extends = __commonJS({
  "node_modules/@babel/runtime/helpers/extends.js"(exports, module) {
    function _extends() {
      return module.exports = _extends = Object.assign ? Object.assign.bind() : function(n) {
        for (var e = 1; e < arguments.length; e++) {
          var t = arguments[e];
          for (var r in t)
            ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
        }
        return n;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _extends.apply(null, arguments);
    }
    module.exports = _extends, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@ant-design/icons-svg/lib/asn/ArrowLeftOutlined.js
var require_ArrowLeftOutlined = __commonJS({
  "node_modules/@ant-design/icons-svg/lib/asn/ArrowLeftOutlined.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var ArrowLeftOutlined2 = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z" } }] }, "name": "arrow-left", "theme": "outlined" };
    exports.default = ArrowLeftOutlined2;
  }
});

// node_modules/@babel/runtime/helpers/arrayWithHoles.js
var require_arrayWithHoles = __commonJS({
  "node_modules/@babel/runtime/helpers/arrayWithHoles.js"(exports, module) {
    function _arrayWithHoles2(r) {
      if (Array.isArray(r))
        return r;
    }
    module.exports = _arrayWithHoles2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/iterableToArrayLimit.js
var require_iterableToArrayLimit = __commonJS({
  "node_modules/@babel/runtime/helpers/iterableToArrayLimit.js"(exports, module) {
    function _iterableToArrayLimit2(r, l) {
      var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
      if (null != t) {
        var e, n, i, u, a = [], f = true, o = false;
        try {
          if (i = (t = t.call(r)).next, 0 === l) {
            if (Object(t) !== t)
              return;
            f = false;
          } else
            for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = true)
              ;
        } catch (r2) {
          o = true, n = r2;
        } finally {
          try {
            if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u))
              return;
          } finally {
            if (o)
              throw n;
          }
        }
        return a;
      }
    }
    module.exports = _iterableToArrayLimit2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/arrayLikeToArray.js
var require_arrayLikeToArray = __commonJS({
  "node_modules/@babel/runtime/helpers/arrayLikeToArray.js"(exports, module) {
    function _arrayLikeToArray2(r, a) {
      (null == a || a > r.length) && (a = r.length);
      for (var e = 0, n = Array(a); e < a; e++)
        n[e] = r[e];
      return n;
    }
    module.exports = _arrayLikeToArray2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js
var require_unsupportedIterableToArray = __commonJS({
  "node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js"(exports, module) {
    var arrayLikeToArray = require_arrayLikeToArray();
    function _unsupportedIterableToArray2(r, a) {
      if (r) {
        if ("string" == typeof r)
          return arrayLikeToArray(r, a);
        var t = {}.toString.call(r).slice(8, -1);
        return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;
      }
    }
    module.exports = _unsupportedIterableToArray2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/nonIterableRest.js
var require_nonIterableRest = __commonJS({
  "node_modules/@babel/runtime/helpers/nonIterableRest.js"(exports, module) {
    function _nonIterableRest2() {
      throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
    }
    module.exports = _nonIterableRest2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/slicedToArray.js
var require_slicedToArray = __commonJS({
  "node_modules/@babel/runtime/helpers/slicedToArray.js"(exports, module) {
    var arrayWithHoles = require_arrayWithHoles();
    var iterableToArrayLimit = require_iterableToArrayLimit();
    var unsupportedIterableToArray = require_unsupportedIterableToArray();
    var nonIterableRest = require_nonIterableRest();
    function _slicedToArray3(r, e) {
      return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();
    }
    module.exports = _slicedToArray3, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPrimitive.js
var require_toPrimitive = __commonJS({
  "node_modules/@babel/runtime/helpers/toPrimitive.js"(exports, module) {
    var _typeof3 = require_typeof()["default"];
    function toPrimitive(t, r) {
      if ("object" != _typeof3(t) || !t)
        return t;
      var e = t[Symbol.toPrimitive];
      if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof3(i))
          return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return ("string" === r ? String : Number)(t);
    }
    module.exports = toPrimitive, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPropertyKey.js
var require_toPropertyKey = __commonJS({
  "node_modules/@babel/runtime/helpers/toPropertyKey.js"(exports, module) {
    var _typeof3 = require_typeof()["default"];
    var toPrimitive = require_toPrimitive();
    function toPropertyKey(t) {
      var i = toPrimitive(t, "string");
      return "symbol" == _typeof3(i) ? i : i + "";
    }
    module.exports = toPropertyKey, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/defineProperty.js
var require_defineProperty = __commonJS({
  "node_modules/@babel/runtime/helpers/defineProperty.js"(exports, module) {
    var toPropertyKey = require_toPropertyKey();
    function _defineProperty4(e, r, t) {
      return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: true,
        configurable: true,
        writable: true
      }) : e[r] = t, e;
    }
    module.exports = _defineProperty4, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js
var require_objectWithoutPropertiesLoose = __commonJS({
  "node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js"(exports, module) {
    function _objectWithoutPropertiesLoose2(r, e) {
      if (null == r)
        return {};
      var t = {};
      for (var n in r)
        if ({}.hasOwnProperty.call(r, n)) {
          if (-1 !== e.indexOf(n))
            continue;
          t[n] = r[n];
        }
      return t;
    }
    module.exports = _objectWithoutPropertiesLoose2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/objectWithoutProperties.js
var require_objectWithoutProperties = __commonJS({
  "node_modules/@babel/runtime/helpers/objectWithoutProperties.js"(exports, module) {
    var objectWithoutPropertiesLoose = require_objectWithoutPropertiesLoose();
    function _objectWithoutProperties3(e, t) {
      if (null == e)
        return {};
      var o, r, i = objectWithoutPropertiesLoose(e, t);
      if (Object.getOwnPropertySymbols) {
        var n = Object.getOwnPropertySymbols(e);
        for (r = 0; r < n.length; r++)
          o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
      }
      return i;
    }
    module.exports = _objectWithoutProperties3, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@ant-design/icons/lib/components/Context.js
var require_Context = __commonJS({
  "node_modules/@ant-design/icons/lib/components/Context.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _react = require_react();
    var IconContext = (0, _react.createContext)({});
    var _default = exports.default = IconContext;
  }
});

// node_modules/@babel/runtime/helpers/objectSpread2.js
var require_objectSpread2 = __commonJS({
  "node_modules/@babel/runtime/helpers/objectSpread2.js"(exports, module) {
    var defineProperty = require_defineProperty();
    function ownKeys3(e, r) {
      var t = Object.keys(e);
      if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r2) {
          return Object.getOwnPropertyDescriptor(e, r2).enumerable;
        })), t.push.apply(t, o);
      }
      return t;
    }
    function _objectSpread22(e) {
      for (var r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys3(Object(t), true).forEach(function(r2) {
          defineProperty(e, r2, t[r2]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys3(Object(t)).forEach(function(r2) {
          Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
        });
      }
      return e;
    }
    module.exports = _objectSpread22, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-util/lib/Dom/canUseDom.js
var require_canUseDom = __commonJS({
  "node_modules/rc-util/lib/Dom/canUseDom.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = canUseDom;
    function canUseDom() {
      return !!(typeof window !== "undefined" && window.document && window.document.createElement);
    }
  }
});

// node_modules/rc-util/lib/Dom/contains.js
var require_contains = __commonJS({
  "node_modules/rc-util/lib/Dom/contains.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = contains;
    function contains(root, n) {
      if (!root) {
        return false;
      }
      if (root.contains) {
        return root.contains(n);
      }
      var node = n;
      while (node) {
        if (node === root) {
          return true;
        }
        node = node.parentNode;
      }
      return false;
    }
  }
});

// node_modules/rc-util/lib/Dom/dynamicCSS.js
var require_dynamicCSS = __commonJS({
  "node_modules/rc-util/lib/Dom/dynamicCSS.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.clearContainerCache = clearContainerCache;
    exports.injectCSS = injectCSS;
    exports.removeCSS = removeCSS;
    exports.updateCSS = updateCSS;
    var _objectSpread22 = _interopRequireDefault(require_objectSpread2());
    var _canUseDom = _interopRequireDefault(require_canUseDom());
    var _contains = _interopRequireDefault(require_contains());
    var APPEND_ORDER = "data-rc-order";
    var APPEND_PRIORITY = "data-rc-priority";
    var MARK_KEY = "rc-util-key";
    var containerCache = /* @__PURE__ */ new Map();
    function getMark() {
      var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, mark = _ref.mark;
      if (mark) {
        return mark.startsWith("data-") ? mark : "data-".concat(mark);
      }
      return MARK_KEY;
    }
    function getContainer(option) {
      if (option.attachTo) {
        return option.attachTo;
      }
      var head = document.querySelector("head");
      return head || document.body;
    }
    function getOrder(prepend) {
      if (prepend === "queue") {
        return "prependQueue";
      }
      return prepend ? "prepend" : "append";
    }
    function findStyles(container) {
      return Array.from((containerCache.get(container) || container).children).filter(function(node) {
        return node.tagName === "STYLE";
      });
    }
    function injectCSS(css) {
      var option = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
      if (!(0, _canUseDom.default)()) {
        return null;
      }
      var csp = option.csp, prepend = option.prepend, _option$priority = option.priority, priority = _option$priority === void 0 ? 0 : _option$priority;
      var mergedOrder = getOrder(prepend);
      var isPrependQueue = mergedOrder === "prependQueue";
      var styleNode = document.createElement("style");
      styleNode.setAttribute(APPEND_ORDER, mergedOrder);
      if (isPrependQueue && priority) {
        styleNode.setAttribute(APPEND_PRIORITY, "".concat(priority));
      }
      if (csp !== null && csp !== void 0 && csp.nonce) {
        styleNode.nonce = csp === null || csp === void 0 ? void 0 : csp.nonce;
      }
      styleNode.innerHTML = css;
      var container = getContainer(option);
      var firstChild = container.firstChild;
      if (prepend) {
        if (isPrependQueue) {
          var existStyle = (option.styles || findStyles(container)).filter(function(node) {
            if (!["prepend", "prependQueue"].includes(node.getAttribute(APPEND_ORDER))) {
              return false;
            }
            var nodePriority = Number(node.getAttribute(APPEND_PRIORITY) || 0);
            return priority >= nodePriority;
          });
          if (existStyle.length) {
            container.insertBefore(styleNode, existStyle[existStyle.length - 1].nextSibling);
            return styleNode;
          }
        }
        container.insertBefore(styleNode, firstChild);
      } else {
        container.appendChild(styleNode);
      }
      return styleNode;
    }
    function findExistNode(key) {
      var option = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
      var container = getContainer(option);
      return (option.styles || findStyles(container)).find(function(node) {
        return node.getAttribute(getMark(option)) === key;
      });
    }
    function removeCSS(key) {
      var option = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
      var existNode = findExistNode(key, option);
      if (existNode) {
        var container = getContainer(option);
        container.removeChild(existNode);
      }
    }
    function syncRealContainer(container, option) {
      var cachedRealContainer = containerCache.get(container);
      if (!cachedRealContainer || !(0, _contains.default)(document, cachedRealContainer)) {
        var placeholderStyle = injectCSS("", option);
        var parentNode = placeholderStyle.parentNode;
        containerCache.set(container, parentNode);
        container.removeChild(placeholderStyle);
      }
    }
    function clearContainerCache() {
      containerCache.clear();
    }
    function updateCSS(css, key) {
      var originOption = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
      var container = getContainer(originOption);
      var styles = findStyles(container);
      var option = (0, _objectSpread22.default)((0, _objectSpread22.default)({}, originOption), {}, {
        styles
      });
      syncRealContainer(container, option);
      var existNode = findExistNode(key, option);
      if (existNode) {
        var _option$csp, _option$csp2;
        if ((_option$csp = option.csp) !== null && _option$csp !== void 0 && _option$csp.nonce && existNode.nonce !== ((_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce)) {
          var _option$csp3;
          existNode.nonce = (_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce;
        }
        if (existNode.innerHTML !== css) {
          existNode.innerHTML = css;
        }
        return existNode;
      }
      var newNode = injectCSS(css, option);
      newNode.setAttribute(getMark(option), key);
      return newNode;
    }
  }
});

// node_modules/rc-util/lib/Dom/shadow.js
var require_shadow = __commonJS({
  "node_modules/rc-util/lib/Dom/shadow.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.getShadowRoot = getShadowRoot;
    exports.inShadow = inShadow;
    function getRoot(ele) {
      var _ele$getRootNode;
      return ele === null || ele === void 0 || (_ele$getRootNode = ele.getRootNode) === null || _ele$getRootNode === void 0 ? void 0 : _ele$getRootNode.call(ele);
    }
    function inShadow(ele) {
      return getRoot(ele) instanceof ShadowRoot;
    }
    function getShadowRoot(ele) {
      return inShadow(ele) ? getRoot(ele) : null;
    }
  }
});

// node_modules/rc-util/lib/warning.js
var require_warning = __commonJS({
  "node_modules/rc-util/lib/warning.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.call = call;
    exports.default = void 0;
    exports.note = note;
    exports.noteOnce = noteOnce2;
    exports.preMessage = void 0;
    exports.resetWarned = resetWarned;
    exports.warning = warning;
    exports.warningOnce = warningOnce;
    var warned = {};
    var preWarningFns = [];
    var preMessage = exports.preMessage = function preMessage2(fn) {
      preWarningFns.push(fn);
    };
    function warning(valid, message) {
      if (!valid && console !== void 0) {
        var finalMessage = preWarningFns.reduce(function(msg, preMessageFn) {
          return preMessageFn(msg !== null && msg !== void 0 ? msg : "", "warning");
        }, message);
        if (finalMessage) {
          console.error("Warning: ".concat(finalMessage));
        }
      }
    }
    function note(valid, message) {
      if (!valid && console !== void 0) {
        var finalMessage = preWarningFns.reduce(function(msg, preMessageFn) {
          return preMessageFn(msg !== null && msg !== void 0 ? msg : "", "note");
        }, message);
        if (finalMessage) {
          console.warn("Note: ".concat(finalMessage));
        }
      }
    }
    function resetWarned() {
      warned = {};
    }
    function call(method, valid, message) {
      if (!valid && !warned[message]) {
        method(false, message);
        warned[message] = true;
      }
    }
    function warningOnce(valid, message) {
      call(warning, valid, message);
    }
    function noteOnce2(valid, message) {
      call(note, valid, message);
    }
    warningOnce.preMessage = preMessage;
    warningOnce.resetWarned = resetWarned;
    warningOnce.noteOnce = noteOnce2;
    var _default = exports.default = warningOnce;
  }
});

// node_modules/@ant-design/icons/lib/utils.js
var require_utils = __commonJS({
  "node_modules/@ant-design/icons/lib/utils.js"(exports) {
    "use strict";
    var _interopRequireWildcard = require_interopRequireWildcard().default;
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.generate = generate;
    exports.getSecondaryColor = getSecondaryColor;
    exports.iconStyles = void 0;
    exports.isIconDefinition = isIconDefinition;
    exports.normalizeAttrs = normalizeAttrs;
    exports.normalizeTwoToneColors = normalizeTwoToneColors;
    exports.useInsertStyles = exports.svgBaseProps = void 0;
    exports.warning = warning;
    var _objectSpread22 = _interopRequireDefault(require_objectSpread2());
    var _typeof22 = _interopRequireDefault(require_typeof());
    var _colors = (init_es(), __toCommonJS(es_exports));
    var _dynamicCSS = require_dynamicCSS();
    var _shadow = require_shadow();
    var _warning = _interopRequireDefault(require_warning());
    var _react = _interopRequireWildcard(require_react());
    var _Context = _interopRequireDefault(require_Context());
    function camelCase(input) {
      return input.replace(/-(.)/g, function(match3, g) {
        return g.toUpperCase();
      });
    }
    function warning(valid, message) {
      (0, _warning.default)(valid, "[@ant-design/icons] ".concat(message));
    }
    function isIconDefinition(target) {
      return (0, _typeof22.default)(target) === "object" && typeof target.name === "string" && typeof target.theme === "string" && ((0, _typeof22.default)(target.icon) === "object" || typeof target.icon === "function");
    }
    function normalizeAttrs() {
      var attrs = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
      return Object.keys(attrs).reduce(function(acc, key) {
        var val = attrs[key];
        switch (key) {
          case "class":
            acc.className = val;
            delete acc.class;
            break;
          default:
            delete acc[key];
            acc[camelCase(key)] = val;
        }
        return acc;
      }, {});
    }
    function generate(node, key, rootProps) {
      if (!rootProps) {
        return _react.default.createElement(node.tag, (0, _objectSpread22.default)({
          key
        }, normalizeAttrs(node.attrs)), (node.children || []).map(function(child, index) {
          return generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
        }));
      }
      return _react.default.createElement(node.tag, (0, _objectSpread22.default)((0, _objectSpread22.default)({
        key
      }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function(child, index) {
        return generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
      }));
    }
    function getSecondaryColor(primaryColor) {
      return (0, _colors.generate)(primaryColor)[0];
    }
    function normalizeTwoToneColors(twoToneColor) {
      if (!twoToneColor) {
        return [];
      }
      return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];
    }
    var svgBaseProps = exports.svgBaseProps = {
      width: "1em",
      height: "1em",
      fill: "currentColor",
      "aria-hidden": "true",
      focusable: "false"
    };
    var iconStyles = exports.iconStyles = "\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";
    var useInsertStyles = exports.useInsertStyles = function useInsertStyles2(eleRef) {
      var _useContext = (0, _react.useContext)(_Context.default), csp = _useContext.csp, prefixCls = _useContext.prefixCls, layer = _useContext.layer;
      var mergedStyleStr = iconStyles;
      if (prefixCls) {
        mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);
      }
      if (layer) {
        mergedStyleStr = "@layer ".concat(layer, " {\n").concat(mergedStyleStr, "\n}");
      }
      (0, _react.useEffect)(function() {
        var ele = eleRef.current;
        var shadowRoot = (0, _shadow.getShadowRoot)(ele);
        (0, _dynamicCSS.updateCSS)(mergedStyleStr, "@ant-design-icons", {
          prepend: !layer,
          csp,
          attachTo: shadowRoot
        });
      }, []);
    };
  }
});

// node_modules/@ant-design/icons/lib/components/IconBase.js
var require_IconBase = __commonJS({
  "node_modules/@ant-design/icons/lib/components/IconBase.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    var _interopRequireWildcard = require_interopRequireWildcard().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _objectWithoutProperties22 = _interopRequireDefault(require_objectWithoutProperties());
    var _objectSpread22 = _interopRequireDefault(require_objectSpread2());
    var React32 = _interopRequireWildcard(require_react());
    var _utils = require_utils();
    var _excluded15 = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"];
    var twoToneColorPalette = {
      primaryColor: "#333",
      secondaryColor: "#E6E6E6",
      calculated: false
    };
    function setTwoToneColors(_ref) {
      var primaryColor = _ref.primaryColor, secondaryColor = _ref.secondaryColor;
      twoToneColorPalette.primaryColor = primaryColor;
      twoToneColorPalette.secondaryColor = secondaryColor || (0, _utils.getSecondaryColor)(primaryColor);
      twoToneColorPalette.calculated = !!secondaryColor;
    }
    function getTwoToneColors() {
      return (0, _objectSpread22.default)({}, twoToneColorPalette);
    }
    var IconBase = function IconBase2(props) {
      var icon = props.icon, className = props.className, onClick = props.onClick, style = props.style, primaryColor = props.primaryColor, secondaryColor = props.secondaryColor, restProps = (0, _objectWithoutProperties22.default)(props, _excluded15);
      var svgRef = React32.useRef();
      var colors = twoToneColorPalette;
      if (primaryColor) {
        colors = {
          primaryColor,
          secondaryColor: secondaryColor || (0, _utils.getSecondaryColor)(primaryColor)
        };
      }
      (0, _utils.useInsertStyles)(svgRef);
      (0, _utils.warning)((0, _utils.isIconDefinition)(icon), "icon should be icon definiton, but got ".concat(icon));
      if (!(0, _utils.isIconDefinition)(icon)) {
        return null;
      }
      var target = icon;
      if (target && typeof target.icon === "function") {
        target = (0, _objectSpread22.default)((0, _objectSpread22.default)({}, target), {}, {
          icon: target.icon(colors.primaryColor, colors.secondaryColor)
        });
      }
      return (0, _utils.generate)(target.icon, "svg-".concat(target.name), (0, _objectSpread22.default)((0, _objectSpread22.default)({
        className,
        onClick,
        style,
        "data-icon": target.name,
        width: "1em",
        height: "1em",
        fill: "currentColor",
        "aria-hidden": "true"
      }, restProps), {}, {
        ref: svgRef
      }));
    };
    IconBase.displayName = "IconReact";
    IconBase.getTwoToneColors = getTwoToneColors;
    IconBase.setTwoToneColors = setTwoToneColors;
    var _default = exports.default = IconBase;
  }
});

// node_modules/@ant-design/icons/lib/components/twoTonePrimaryColor.js
var require_twoTonePrimaryColor = __commonJS({
  "node_modules/@ant-design/icons/lib/components/twoTonePrimaryColor.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.getTwoToneColor = getTwoToneColor;
    exports.setTwoToneColor = setTwoToneColor;
    var _slicedToArray22 = _interopRequireDefault(require_slicedToArray());
    var _IconBase = _interopRequireDefault(require_IconBase());
    var _utils = require_utils();
    function setTwoToneColor(twoToneColor) {
      var _normalizeTwoToneColo = (0, _utils.normalizeTwoToneColors)(twoToneColor), _normalizeTwoToneColo2 = (0, _slicedToArray22.default)(_normalizeTwoToneColo, 2), primaryColor = _normalizeTwoToneColo2[0], secondaryColor = _normalizeTwoToneColo2[1];
      return _IconBase.default.setTwoToneColors({
        primaryColor,
        secondaryColor
      });
    }
    function getTwoToneColor() {
      var colors = _IconBase.default.getTwoToneColors();
      if (!colors.calculated) {
        return colors.primaryColor;
      }
      return [colors.primaryColor, colors.secondaryColor];
    }
  }
});

// node_modules/@ant-design/icons/lib/components/AntdIcon.js
var require_AntdIcon = __commonJS({
  "node_modules/@ant-design/icons/lib/components/AntdIcon.js"(exports) {
    "use strict";
    "use client";
    var _interopRequireDefault = require_interopRequireDefault().default;
    var _interopRequireWildcard = require_interopRequireWildcard().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _extends2 = _interopRequireDefault(require_extends());
    var _slicedToArray22 = _interopRequireDefault(require_slicedToArray());
    var _defineProperty22 = _interopRequireDefault(require_defineProperty());
    var _objectWithoutProperties22 = _interopRequireDefault(require_objectWithoutProperties());
    var React32 = _interopRequireWildcard(require_react());
    var _classnames = _interopRequireDefault(require_classnames());
    var _colors = (init_es(), __toCommonJS(es_exports));
    var _Context = _interopRequireDefault(require_Context());
    var _IconBase = _interopRequireDefault(require_IconBase());
    var _twoTonePrimaryColor = require_twoTonePrimaryColor();
    var _utils = require_utils();
    var _excluded15 = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];
    (0, _twoTonePrimaryColor.setTwoToneColor)(_colors.blue.primary);
    var Icon = React32.forwardRef(function(props, ref) {
      var className = props.className, icon = props.icon, spin = props.spin, rotate = props.rotate, tabIndex = props.tabIndex, onClick = props.onClick, twoToneColor = props.twoToneColor, restProps = (0, _objectWithoutProperties22.default)(props, _excluded15);
      var _React$useContext = React32.useContext(_Context.default), _React$useContext$pre = _React$useContext.prefixCls, prefixCls = _React$useContext$pre === void 0 ? "anticon" : _React$useContext$pre, rootClassName = _React$useContext.rootClassName;
      var classString = (0, _classnames.default)(rootClassName, prefixCls, (0, _defineProperty22.default)((0, _defineProperty22.default)({}, "".concat(prefixCls, "-").concat(icon.name), !!icon.name), "".concat(prefixCls, "-spin"), !!spin || icon.name === "loading"), className);
      var iconTabIndex = tabIndex;
      if (iconTabIndex === void 0 && onClick) {
        iconTabIndex = -1;
      }
      var svgStyle = rotate ? {
        msTransform: "rotate(".concat(rotate, "deg)"),
        transform: "rotate(".concat(rotate, "deg)")
      } : void 0;
      var _normalizeTwoToneColo = (0, _utils.normalizeTwoToneColors)(twoToneColor), _normalizeTwoToneColo2 = (0, _slicedToArray22.default)(_normalizeTwoToneColo, 2), primaryColor = _normalizeTwoToneColo2[0], secondaryColor = _normalizeTwoToneColo2[1];
      return React32.createElement("span", (0, _extends2.default)({
        role: "img",
        "aria-label": icon.name
      }, restProps, {
        ref,
        tabIndex: iconTabIndex,
        onClick,
        className: classString
      }), React32.createElement(_IconBase.default, {
        icon,
        primaryColor,
        secondaryColor,
        style: svgStyle
      }));
    });
    Icon.displayName = "AntdIcon";
    Icon.getTwoToneColor = _twoTonePrimaryColor.getTwoToneColor;
    Icon.setTwoToneColor = _twoTonePrimaryColor.setTwoToneColor;
    var _default = exports.default = Icon;
  }
});

// node_modules/@ant-design/icons/lib/icons/ArrowLeftOutlined.js
var require_ArrowLeftOutlined2 = __commonJS({
  "node_modules/@ant-design/icons/lib/icons/ArrowLeftOutlined.js"(exports) {
    "use strict";
    var _interopRequireWildcard = require_interopRequireWildcard().default;
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _extends2 = _interopRequireDefault(require_extends());
    var React32 = _interopRequireWildcard(require_react());
    var _ArrowLeftOutlined = _interopRequireDefault(require_ArrowLeftOutlined());
    var _AntdIcon = _interopRequireDefault(require_AntdIcon());
    var ArrowLeftOutlined2 = function ArrowLeftOutlined3(props, ref) {
      return React32.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
        ref,
        icon: _ArrowLeftOutlined.default
      }));
    };
    var RefIcon = React32.forwardRef(ArrowLeftOutlined2);
    if (true) {
      RefIcon.displayName = "ArrowLeftOutlined";
    }
    var _default = exports.default = RefIcon;
  }
});

// node_modules/@ant-design/icons/ArrowLeftOutlined.js
var require_ArrowLeftOutlined3 = __commonJS({
  "node_modules/@ant-design/icons/ArrowLeftOutlined.js"(exports, module) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _ArrowLeftOutlined = _interopRequireDefault(require_ArrowLeftOutlined2());
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    var _default = _ArrowLeftOutlined;
    exports.default = _default;
    module.exports = _default;
  }
});

// node_modules/@ant-design/icons-svg/lib/asn/ArrowRightOutlined.js
var require_ArrowRightOutlined = __commonJS({
  "node_modules/@ant-design/icons-svg/lib/asn/ArrowRightOutlined.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var ArrowRightOutlined2 = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z" } }] }, "name": "arrow-right", "theme": "outlined" };
    exports.default = ArrowRightOutlined2;
  }
});

// node_modules/@ant-design/icons/lib/icons/ArrowRightOutlined.js
var require_ArrowRightOutlined2 = __commonJS({
  "node_modules/@ant-design/icons/lib/icons/ArrowRightOutlined.js"(exports) {
    "use strict";
    var _interopRequireWildcard = require_interopRequireWildcard().default;
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _extends2 = _interopRequireDefault(require_extends());
    var React32 = _interopRequireWildcard(require_react());
    var _ArrowRightOutlined = _interopRequireDefault(require_ArrowRightOutlined());
    var _AntdIcon = _interopRequireDefault(require_AntdIcon());
    var ArrowRightOutlined2 = function ArrowRightOutlined3(props, ref) {
      return React32.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
        ref,
        icon: _ArrowRightOutlined.default
      }));
    };
    var RefIcon = React32.forwardRef(ArrowRightOutlined2);
    if (true) {
      RefIcon.displayName = "ArrowRightOutlined";
    }
    var _default = exports.default = RefIcon;
  }
});

// node_modules/@ant-design/icons/ArrowRightOutlined.js
var require_ArrowRightOutlined3 = __commonJS({
  "node_modules/@ant-design/icons/ArrowRightOutlined.js"(exports, module) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _ArrowRightOutlined = _interopRequireDefault(require_ArrowRightOutlined2());
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    var _default = _ArrowRightOutlined;
    exports.default = _default;
    module.exports = _default;
  }
});

// node_modules/path-to-regexp/dist/index.js
var require_dist = __commonJS({
  "node_modules/path-to-regexp/dist/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.TokenData = void 0;
    exports.parse = parse;
    exports.compile = compile;
    exports.match = match3;
    exports.pathToRegexp = pathToRegexp3;
    exports.stringify = stringify;
    var DEFAULT_DELIMITER = "/";
    var NOOP_VALUE = (value) => value;
    var ID_START = /^[$_\p{ID_Start}]$/u;
    var ID_CONTINUE = /^[$\u200c\u200d\p{ID_Continue}]$/u;
    var DEBUG_URL = "https://git.new/pathToRegexpError";
    var SIMPLE_TOKENS = {
      // Groups.
      "{": "{",
      "}": "}",
      // Reserved.
      "(": "(",
      ")": ")",
      "[": "[",
      "]": "]",
      "+": "+",
      "?": "?",
      "!": "!"
    };
    function escapeText(str) {
      return str.replace(/[{}()\[\]+?!:*]/g, "\\$&");
    }
    function escape(str) {
      return str.replace(/[.+*?^${}()[\]|/\\]/g, "\\$&");
    }
    function* lexer(str) {
      const chars = [...str];
      let i = 0;
      function name() {
        let value = "";
        if (ID_START.test(chars[++i])) {
          value += chars[i];
          while (ID_CONTINUE.test(chars[++i])) {
            value += chars[i];
          }
        } else if (chars[i] === '"') {
          let pos = i;
          while (i < chars.length) {
            if (chars[++i] === '"') {
              i++;
              pos = 0;
              break;
            }
            if (chars[i] === "\\") {
              value += chars[++i];
            } else {
              value += chars[i];
            }
          }
          if (pos) {
            throw new TypeError(`Unterminated quote at ${pos}: ${DEBUG_URL}`);
          }
        }
        if (!value) {
          throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);
        }
        return value;
      }
      while (i < chars.length) {
        const value = chars[i];
        const type = SIMPLE_TOKENS[value];
        if (type) {
          yield { type, index: i++, value };
        } else if (value === "\\") {
          yield { type: "ESCAPED", index: i++, value: chars[i++] };
        } else if (value === ":") {
          const value2 = name();
          yield { type: "PARAM", index: i, value: value2 };
        } else if (value === "*") {
          const value2 = name();
          yield { type: "WILDCARD", index: i, value: value2 };
        } else {
          yield { type: "CHAR", index: i, value: chars[i++] };
        }
      }
      return { type: "END", index: i, value: "" };
    }
    var Iter = class {
      constructor(tokens) {
        this.tokens = tokens;
      }
      peek() {
        if (!this._peek) {
          const next = this.tokens.next();
          this._peek = next.value;
        }
        return this._peek;
      }
      tryConsume(type) {
        const token = this.peek();
        if (token.type !== type)
          return;
        this._peek = void 0;
        return token.value;
      }
      consume(type) {
        const value = this.tryConsume(type);
        if (value !== void 0)
          return value;
        const { type: nextType, index } = this.peek();
        throw new TypeError(`Unexpected ${nextType} at ${index}, expected ${type}: ${DEBUG_URL}`);
      }
      text() {
        let result = "";
        let value;
        while (value = this.tryConsume("CHAR") || this.tryConsume("ESCAPED")) {
          result += value;
        }
        return result;
      }
    };
    var TokenData = class {
      constructor(tokens) {
        this.tokens = tokens;
      }
    };
    exports.TokenData = TokenData;
    function parse(str, options = {}) {
      const { encodePath = NOOP_VALUE } = options;
      const it = new Iter(lexer(str));
      function consume(endType) {
        const tokens2 = [];
        while (true) {
          const path = it.text();
          if (path)
            tokens2.push({ type: "text", value: encodePath(path) });
          const param = it.tryConsume("PARAM");
          if (param) {
            tokens2.push({
              type: "param",
              name: param
            });
            continue;
          }
          const wildcard = it.tryConsume("WILDCARD");
          if (wildcard) {
            tokens2.push({
              type: "wildcard",
              name: wildcard
            });
            continue;
          }
          const open = it.tryConsume("{");
          if (open) {
            tokens2.push({
              type: "group",
              tokens: consume("}")
            });
            continue;
          }
          it.consume(endType);
          return tokens2;
        }
      }
      const tokens = consume("END");
      return new TokenData(tokens);
    }
    function compile(path, options = {}) {
      const { encode = encodeURIComponent, delimiter = DEFAULT_DELIMITER } = options;
      const data = path instanceof TokenData ? path : parse(path, options);
      const fn = tokensToFunction(data.tokens, delimiter, encode);
      return function path2(data2 = {}) {
        const [path3, ...missing] = fn(data2);
        if (missing.length) {
          throw new TypeError(`Missing parameters: ${missing.join(", ")}`);
        }
        return path3;
      };
    }
    function tokensToFunction(tokens, delimiter, encode) {
      const encoders = tokens.map((token) => tokenToFunction(token, delimiter, encode));
      return (data) => {
        const result = [""];
        for (const encoder of encoders) {
          const [value, ...extras] = encoder(data);
          result[0] += value;
          result.push(...extras);
        }
        return result;
      };
    }
    function tokenToFunction(token, delimiter, encode) {
      if (token.type === "text")
        return () => [token.value];
      if (token.type === "group") {
        const fn = tokensToFunction(token.tokens, delimiter, encode);
        return (data) => {
          const [value, ...missing] = fn(data);
          if (!missing.length)
            return [value];
          return [""];
        };
      }
      const encodeValue = encode || NOOP_VALUE;
      if (token.type === "wildcard" && encode !== false) {
        return (data) => {
          const value = data[token.name];
          if (value == null)
            return ["", token.name];
          if (!Array.isArray(value) || value.length === 0) {
            throw new TypeError(`Expected "${token.name}" to be a non-empty array`);
          }
          return [
            value.map((value2, index) => {
              if (typeof value2 !== "string") {
                throw new TypeError(`Expected "${token.name}/${index}" to be a string`);
              }
              return encodeValue(value2);
            }).join(delimiter)
          ];
        };
      }
      return (data) => {
        const value = data[token.name];
        if (value == null)
          return ["", token.name];
        if (typeof value !== "string") {
          throw new TypeError(`Expected "${token.name}" to be a string`);
        }
        return [encodeValue(value)];
      };
    }
    function match3(path, options = {}) {
      const { decode = decodeURIComponent, delimiter = DEFAULT_DELIMITER } = options;
      const { regexp, keys } = pathToRegexp3(path, options);
      const decoders = keys.map((key) => {
        if (decode === false)
          return NOOP_VALUE;
        if (key.type === "param")
          return decode;
        return (value) => value.split(delimiter).map(decode);
      });
      return function match4(input) {
        const m = regexp.exec(input);
        if (!m)
          return false;
        const path2 = m[0];
        const params = /* @__PURE__ */ Object.create(null);
        for (let i = 1; i < m.length; i++) {
          if (m[i] === void 0)
            continue;
          const key = keys[i - 1];
          const decoder = decoders[i - 1];
          params[key.name] = decoder(m[i]);
        }
        return { path: path2, params };
      };
    }
    function pathToRegexp3(path, options = {}) {
      const { delimiter = DEFAULT_DELIMITER, end = true, sensitive = false, trailing = true } = options;
      const keys = [];
      const sources = [];
      const flags = sensitive ? "" : "i";
      const paths = Array.isArray(path) ? path : [path];
      const items = paths.map((path2) => path2 instanceof TokenData ? path2 : parse(path2, options));
      for (const { tokens } of items) {
        for (const seq of flatten(tokens, 0, [])) {
          const regexp2 = sequenceToRegExp(seq, delimiter, keys);
          sources.push(regexp2);
        }
      }
      let pattern = `^(?:${sources.join("|")})`;
      if (trailing)
        pattern += `(?:${escape(delimiter)}$)?`;
      pattern += end ? "$" : `(?=${escape(delimiter)}|$)`;
      const regexp = new RegExp(pattern, flags);
      return { regexp, keys };
    }
    function* flatten(tokens, index, init) {
      if (index === tokens.length) {
        return yield init;
      }
      const token = tokens[index];
      if (token.type === "group") {
        const fork = init.slice();
        for (const seq of flatten(token.tokens, 0, fork)) {
          yield* flatten(tokens, index + 1, seq);
        }
      } else {
        init.push(token);
      }
      yield* flatten(tokens, index + 1, init);
    }
    function sequenceToRegExp(tokens, delimiter, keys) {
      let result = "";
      let backtrack = "";
      let isSafeSegmentParam = true;
      for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        if (token.type === "text") {
          result += escape(token.value);
          backtrack += token.value;
          isSafeSegmentParam || (isSafeSegmentParam = token.value.includes(delimiter));
          continue;
        }
        if (token.type === "param" || token.type === "wildcard") {
          if (!isSafeSegmentParam && !backtrack) {
            throw new TypeError(`Missing text after "${token.name}": ${DEBUG_URL}`);
          }
          if (token.type === "param") {
            result += `(${negate(delimiter, isSafeSegmentParam ? "" : backtrack)}+)`;
          } else {
            result += `([\\s\\S]+)`;
          }
          keys.push(token);
          backtrack = "";
          isSafeSegmentParam = false;
          continue;
        }
      }
      return result;
    }
    function negate(delimiter, backtrack) {
      if (backtrack.length < 2) {
        if (delimiter.length < 2)
          return `[^${escape(delimiter + backtrack)}]`;
        return `(?:(?!${escape(delimiter)})[^${escape(backtrack)}])`;
      }
      if (delimiter.length < 2) {
        return `(?:(?!${escape(backtrack)})[^${escape(delimiter)}])`;
      }
      return `(?:(?!${escape(backtrack)}|${escape(delimiter)})[\\s\\S])`;
    }
    function stringify(data) {
      return data.tokens.map(function stringifyToken(token, index, tokens) {
        if (token.type === "text")
          return escapeText(token.value);
        if (token.type === "group") {
          return `{${token.tokens.map(stringifyToken).join("")}}`;
        }
        const isSafe = isNameSafe(token.name) && isNextNameSafe(tokens[index + 1]);
        const key = isSafe ? token.name : JSON.stringify(token.name);
        if (token.type === "param")
          return `:${key}`;
        if (token.type === "wildcard")
          return `*${key}`;
        throw new TypeError(`Unexpected token: ${token}`);
      }).join("");
    }
    function isNameSafe(name) {
      const [first, ...rest] = name;
      if (!ID_START.test(first))
        return false;
      return rest.every((char) => ID_CONTINUE.test(char));
    }
    function isNextNameSafe(token) {
      if ((token === null || token === void 0 ? void 0 : token.type) !== "text")
        return true;
      return !ID_CONTINUE.test(token.value[0]);
    }
  }
});

// node_modules/@umijs/route-utils/es/path-to-regexp.js
var require_path_to_regexp = __commonJS({
  "node_modules/@umijs/route-utils/es/path-to-regexp.js"(exports) {
    function _typeof3(obj) {
      "@babel/helpers - typeof";
      return _typeof3 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj2) {
        return typeof obj2;
      } : function(obj2) {
        return obj2 && "function" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
      }, _typeof3(obj);
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.pathToRegexp = exports.tokensToRegexp = exports.regexpToFunction = exports.match = exports.tokensToFunction = exports.compile = exports.parse = void 0;
    function lexer(str) {
      var tokens = [];
      var i = 0;
      while (i < str.length) {
        var char = str[i];
        if (char === "*" || char === "+" || char === "?") {
          tokens.push({
            type: "MODIFIER",
            index: i,
            value: str[i++]
          });
          continue;
        }
        if (char === "\\") {
          tokens.push({
            type: "ESCAPED_CHAR",
            index: i++,
            value: str[i++]
          });
          continue;
        }
        if (char === "{") {
          tokens.push({
            type: "OPEN",
            index: i,
            value: str[i++]
          });
          continue;
        }
        if (char === "}") {
          tokens.push({
            type: "CLOSE",
            index: i,
            value: str[i++]
          });
          continue;
        }
        if (char === ":") {
          var name = "";
          var j = i + 1;
          while (j < str.length) {
            var code = str.charCodeAt(j);
            if (
              // `0-9`
              code >= 48 && code <= 57 || // `A-Z`
              code >= 65 && code <= 90 || // `a-z`
              code >= 97 && code <= 122 || // `_`
              code === 95
            ) {
              name += str[j++];
              continue;
            }
            break;
          }
          if (!name)
            throw new TypeError("Missing parameter name at " + i);
          tokens.push({
            type: "NAME",
            index: i,
            value: name
          });
          i = j;
          continue;
        }
        if (char === "(") {
          var count2 = 1;
          var pattern = "";
          var j = i + 1;
          if (str[j] === "?") {
            throw new TypeError('Pattern cannot start with "?" at ' + j);
          }
          while (j < str.length) {
            if (str[j] === "\\") {
              pattern += str[j++] + str[j++];
              continue;
            }
            if (str[j] === ")") {
              count2--;
              if (count2 === 0) {
                j++;
                break;
              }
            } else if (str[j] === "(") {
              count2++;
              if (str[j + 1] !== "?") {
                throw new TypeError("Capturing groups are not allowed at " + j);
              }
            }
            pattern += str[j++];
          }
          if (count2)
            throw new TypeError("Unbalanced pattern at " + i);
          if (!pattern)
            throw new TypeError("Missing pattern at " + i);
          tokens.push({
            type: "PATTERN",
            index: i,
            value: pattern
          });
          i = j;
          continue;
        }
        tokens.push({
          type: "CHAR",
          index: i,
          value: str[i++]
        });
      }
      tokens.push({
        type: "END",
        index: i,
        value: ""
      });
      return tokens;
    }
    function parse(str, options) {
      if (options === void 0) {
        options = {};
      }
      var tokens = lexer(str);
      var _a = options.prefixes, prefixes = _a === void 0 ? "./" : _a;
      var defaultPattern = "[^" + escapeString(options.delimiter || "/#?") + "]+?";
      var result = [];
      var key = 0;
      var i = 0;
      var path = "";
      var tryConsume = function tryConsume2(type) {
        if (i < tokens.length && tokens[i].type === type)
          return tokens[i++].value;
      };
      var mustConsume = function mustConsume2(type) {
        var value2 = tryConsume(type);
        if (value2 !== void 0)
          return value2;
        var _a2 = tokens[i], nextType = _a2.type, index = _a2.index;
        throw new TypeError("Unexpected " + nextType + " at " + index + ", expected " + type);
      };
      var consumeText = function consumeText2() {
        var result2 = "";
        var value2;
        while (value2 = tryConsume("CHAR") || tryConsume("ESCAPED_CHAR")) {
          result2 += value2;
        }
        return result2;
      };
      while (i < tokens.length) {
        var char = tryConsume("CHAR");
        var name = tryConsume("NAME");
        var pattern = tryConsume("PATTERN");
        if (name || pattern) {
          var prefix = char || "";
          if (prefixes.indexOf(prefix) === -1) {
            path += prefix;
            prefix = "";
          }
          if (path) {
            result.push(path);
            path = "";
          }
          result.push({
            name: name || key++,
            prefix,
            suffix: "",
            pattern: pattern || defaultPattern,
            modifier: tryConsume("MODIFIER") || ""
          });
          continue;
        }
        var value = char || tryConsume("ESCAPED_CHAR");
        if (value) {
          path += value;
          continue;
        }
        if (path) {
          result.push(path);
          path = "";
        }
        var open = tryConsume("OPEN");
        if (open) {
          var prefix = consumeText();
          var name_1 = tryConsume("NAME") || "";
          var pattern_1 = tryConsume("PATTERN") || "";
          var suffix = consumeText();
          mustConsume("CLOSE");
          result.push({
            name: name_1 || (pattern_1 ? key++ : ""),
            pattern: name_1 && !pattern_1 ? defaultPattern : pattern_1,
            prefix,
            suffix,
            modifier: tryConsume("MODIFIER") || ""
          });
          continue;
        }
        mustConsume("END");
      }
      return result;
    }
    exports.parse = parse;
    function compile(str, options) {
      return tokensToFunction(parse(str, options), options);
    }
    exports.compile = compile;
    function tokensToFunction(tokens, options) {
      if (options === void 0) {
        options = {};
      }
      var reFlags = flags(options);
      var _a = options.encode, encode = _a === void 0 ? function(x) {
        return x;
      } : _a, _b = options.validate, validate = _b === void 0 ? true : _b;
      var matches = tokens.map(function(token) {
        if (_typeof3(token) === "object") {
          return new RegExp("^(?:" + token.pattern + ")$", reFlags);
        }
      });
      return function(data) {
        var path = "";
        for (var i = 0; i < tokens.length; i++) {
          var token = tokens[i];
          if (typeof token === "string") {
            path += token;
            continue;
          }
          var value = data ? data[token.name] : void 0;
          var optional = token.modifier === "?" || token.modifier === "*";
          var repeat = token.modifier === "*" || token.modifier === "+";
          if (Array.isArray(value)) {
            if (!repeat) {
              throw new TypeError('Expected "' + token.name + '" to not repeat, but got an array');
            }
            if (value.length === 0) {
              if (optional)
                continue;
              throw new TypeError('Expected "' + token.name + '" to not be empty');
            }
            for (var j = 0; j < value.length; j++) {
              var segment = encode(value[j], token);
              if (validate && !matches[i].test(segment)) {
                throw new TypeError('Expected all "' + token.name + '" to match "' + token.pattern + '", but got "' + segment + '"');
              }
              path += token.prefix + segment + token.suffix;
            }
            continue;
          }
          if (typeof value === "string" || typeof value === "number") {
            var segment = encode(String(value), token);
            if (validate && !matches[i].test(segment)) {
              throw new TypeError('Expected "' + token.name + '" to match "' + token.pattern + '", but got "' + segment + '"');
            }
            path += token.prefix + segment + token.suffix;
            continue;
          }
          if (optional)
            continue;
          var typeOfMessage = repeat ? "an array" : "a string";
          throw new TypeError('Expected "' + token.name + '" to be ' + typeOfMessage);
        }
        return path;
      };
    }
    exports.tokensToFunction = tokensToFunction;
    function match3(str, options) {
      var keys = [];
      var re = pathToRegexp3(str, keys, options);
      return regexpToFunction(re, keys, options);
    }
    exports.match = match3;
    function regexpToFunction(re, keys, options) {
      if (options === void 0) {
        options = {};
      }
      var _a = options.decode, decode = _a === void 0 ? function(x) {
        return x;
      } : _a;
      return function(pathname) {
        var m = re.exec(pathname);
        if (!m)
          return false;
        var path = m[0], index = m.index;
        var params = /* @__PURE__ */ Object.create(null);
        var _loop_1 = function _loop_12(i2) {
          if (m[i2] === void 0)
            return "continue";
          var key = keys[i2 - 1];
          if (key.modifier === "*" || key.modifier === "+") {
            params[key.name] = m[i2].split(key.prefix + key.suffix).map(function(value) {
              return decode(value, key);
            });
          } else {
            params[key.name] = decode(m[i2], key);
          }
        };
        for (var i = 1; i < m.length; i++) {
          _loop_1(i);
        }
        return {
          path,
          index,
          params
        };
      };
    }
    exports.regexpToFunction = regexpToFunction;
    function escapeString(str) {
      return str.replace(/([.+*?=^!:${}()[\]|/\\])/g, "\\$1");
    }
    function flags(options) {
      return options && options.sensitive ? "" : "i";
    }
    function regexpToRegexp(path, keys) {
      if (!keys)
        return path;
      var groups = path.source.match(/\((?!\?)/g);
      if (groups) {
        for (var i = 0; i < groups.length; i++) {
          keys.push({
            name: i,
            prefix: "",
            suffix: "",
            modifier: "",
            pattern: ""
          });
        }
      }
      return path;
    }
    function arrayToRegexp(paths, keys, options) {
      var parts = paths.map(function(path) {
        return pathToRegexp3(path, keys, options).source;
      });
      return new RegExp("(?:" + parts.join("|") + ")", flags(options));
    }
    function stringToRegexp(path, keys, options) {
      return tokensToRegexp(parse(path, options), keys, options);
    }
    function tokensToRegexp(tokens, keys, options) {
      if (options === void 0) {
        options = {};
      }
      var _a = options.strict, strict = _a === void 0 ? false : _a, _b = options.start, start = _b === void 0 ? true : _b, _c = options.end, end = _c === void 0 ? true : _c, _d = options.encode, encode = _d === void 0 ? function(x) {
        return x;
      } : _d;
      var endsWith = "[" + escapeString(options.endsWith || "") + "]|$";
      var delimiter = "[" + escapeString(options.delimiter || "/#?") + "]";
      var route = start ? "^" : "";
      for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {
        var token = tokens_1[_i];
        if (typeof token === "string") {
          route += escapeString(encode(token));
        } else {
          var prefix = escapeString(encode(token.prefix));
          var suffix = escapeString(encode(token.suffix));
          if (token.pattern) {
            if (keys)
              keys.push(token);
            if (prefix || suffix) {
              if (token.modifier === "+" || token.modifier === "*") {
                var mod = token.modifier === "*" ? "?" : "";
                route += "(?:" + prefix + "((?:" + token.pattern + ")(?:" + suffix + prefix + "(?:" + token.pattern + "))*)" + suffix + ")" + mod;
              } else {
                route += "(?:" + prefix + "(" + token.pattern + ")" + suffix + ")" + token.modifier;
              }
            } else {
              route += "(" + token.pattern + ")" + token.modifier;
            }
          } else {
            route += "(?:" + prefix + suffix + ")" + token.modifier;
          }
        }
      }
      if (end) {
        if (!strict)
          route += delimiter + "?";
        route += !options.endsWith ? "$" : "(?=" + endsWith + ")";
      } else {
        var endToken = tokens[tokens.length - 1];
        var isEndDelimited = typeof endToken === "string" ? delimiter.indexOf(endToken[endToken.length - 1]) > -1 : (
          // tslint:disable-next-line
          endToken === void 0
        );
        if (!strict) {
          route += "(?:" + delimiter + "(?=" + endsWith + "))?";
        }
        if (!isEndDelimited) {
          route += "(?=" + delimiter + "|" + endsWith + ")";
        }
      }
      return new RegExp(route, flags(options));
    }
    exports.tokensToRegexp = tokensToRegexp;
    function pathToRegexp3(path, keys, options) {
      if (path instanceof RegExp)
        return regexpToRegexp(path, keys);
      if (Array.isArray(path))
        return arrayToRegexp(path, keys, options);
      return stringToRegexp(path, keys, options);
    }
    exports.pathToRegexp = pathToRegexp3;
  }
});

// node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js
init_defineProperty();
var import_classnames = __toESM(require_classnames());
var import_react = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());

// node_modules/@ant-design/pro-layout/es/components/FooterToolbar/style/index.js
init_defineProperty();
var genFooterToolBarStyle = function genFooterToolBarStyle2(token) {
  return _defineProperty({}, token.componentCls, {
    position: "fixed",
    insetInlineEnd: 0,
    bottom: 0,
    zIndex: 99,
    display: "flex",
    alignItems: "center",
    width: "100%",
    paddingInline: 24,
    paddingBlock: 0,
    boxSizing: "border-box",
    lineHeight: "64px",
    /* A way to reset the style of the component. */
    backgroundColor: setAlpha(token.colorBgElevated, 0.6),
    borderBlockStart: "1px solid ".concat(token.colorSplit),
    "-webkit-backdrop-filter": "blur(8px)",
    backdropFilter: "blur(8px)",
    color: token.colorText,
    transition: "all 0.2s ease 0s",
    "&-left": {
      flex: 1,
      color: token.colorText
    },
    "&-right": {
      color: token.colorText,
      "> *": {
        marginInlineEnd: 8,
        "&:last-child": {
          marginBlock: 0,
          marginInline: 0
        }
      }
    }
  });
};
function useStyle2(prefixCls) {
  return useStyle("ProLayoutFooterToolbar", function(token) {
    var proCardToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genFooterToolBarStyle(proCardToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/FooterToolbar/style/stylish.js
init_defineProperty();
function useStylish(prefixCls, _ref) {
  var stylish = _ref.stylish;
  return useStyle("ProLayoutFooterToolbarStylish", function(token) {
    var stylishToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    if (!stylish)
      return [];
    return [_defineProperty({}, "".concat(stylishToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(stylishToken))];
  });
}

// node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var _excluded = ["children", "className", "extra", "portalDom", "style", "renderContent"];
var FooterToolbar = function FooterToolbar2(props) {
  var children = props.children, className = props.className, extra = props.extra, _props$portalDom = props.portalDom, portalDom = _props$portalDom === void 0 ? true : _props$portalDom, style = props.style, renderContent = props.renderContent, restProps = _objectWithoutProperties(props, _excluded);
  var _useContext = (0, import_react.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls, getTargetContainer = _useContext.getTargetContainer;
  var prefixCls = props.prefixCls || getPrefixCls("pro");
  var baseClassName = "".concat(prefixCls, "-footer-bar");
  var _useStyle = useStyle2(baseClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var value = (0, import_react.useContext)(RouteContext);
  var width = (0, import_react.useMemo)(function() {
    var hasSiderMenu = value.hasSiderMenu, isMobile = value.isMobile, siderWidth = value.siderWidth;
    if (!hasSiderMenu) {
      return void 0;
    }
    if (!siderWidth) {
      return "100%";
    }
    return isMobile ? "100%" : "calc(100% - ".concat(siderWidth, "px)");
  }, [value.collapsed, value.hasSiderMenu, value.isMobile, value.siderWidth]);
  var containerDom = (0, import_react.useMemo)(function() {
    if (typeof window === "undefined" || typeof document === "undefined")
      return null;
    return (getTargetContainer === null || getTargetContainer === void 0 ? void 0 : getTargetContainer()) || document.body;
  }, []);
  var stylish = useStylish("".concat(baseClassName, ".").concat(baseClassName, "-stylish"), {
    stylish: props.stylish
  });
  var dom = (0, import_jsx_runtime3.jsxs)(import_jsx_runtime2.Fragment, {
    children: [(0, import_jsx_runtime.jsx)("div", {
      className: "".concat(baseClassName, "-left ").concat(hashId).trim(),
      children: extra
    }), (0, import_jsx_runtime.jsx)("div", {
      className: "".concat(baseClassName, "-right ").concat(hashId).trim(),
      children
    })]
  });
  (0, import_react.useEffect)(function() {
    if (!value || !(value !== null && value !== void 0 && value.setHasFooterToolbar)) {
      return function() {
      };
    }
    value === null || value === void 0 || value.setHasFooterToolbar(true);
    return function() {
      var _value$setHasFooterTo;
      value === null || value === void 0 || (_value$setHasFooterTo = value.setHasFooterToolbar) === null || _value$setHasFooterTo === void 0 || _value$setHasFooterTo.call(value, false);
    };
  }, []);
  var renderDom = (0, import_jsx_runtime.jsx)("div", _objectSpread2(_objectSpread2({
    className: (0, import_classnames.default)(className, hashId, baseClassName, _defineProperty({}, "".concat(baseClassName, "-stylish"), !!props.stylish)),
    style: _objectSpread2({
      width
    }, style)
  }, omit(restProps, ["prefixCls"])), {}, {
    children: renderContent ? renderContent(_objectSpread2(_objectSpread2(_objectSpread2({}, props), value), {}, {
      leftWidth: width
    }), dom) : dom
  }));
  var ssrDom = !isBrowser() || !portalDom || !containerDom ? renderDom : (0, import_react_dom.createPortal)(renderDom, containerDom, baseClassName);
  return stylish.wrapSSR(wrapSSR((0, import_jsx_runtime.jsx)(import_react.default.Fragment, {
    children: ssrDom
  }, baseClassName)));
};

// node_modules/@ant-design/pro-layout/es/components/GridContent/index.js
init_defineProperty();
var import_classnames2 = __toESM(require_classnames());
var import_react3 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/context/RouteContext.js
var import_react2 = __toESM(require_react());
var RouteContext = (0, import_react2.createContext)({});

// node_modules/@ant-design/pro-layout/es/components/GridContent/style.js
init_defineProperty();
var genGridContentStyle = function genGridContentStyle2(token) {
  return _defineProperty({}, token.componentCls, {
    width: "100%",
    "&-wide": {
      maxWidth: 1152,
      margin: "0 auto"
    }
  });
};
function useStyle3(prefixCls) {
  return useStyle("ProLayoutGridContent", function(token) {
    var GridContentToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genGridContentStyle(GridContentToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/GridContent/index.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var GridContent = function GridContent2(props) {
  var value = (0, import_react3.useContext)(RouteContext);
  var children = props.children, propsContentWidth = props.contentWidth, propsClassName = props.className, style = props.style;
  var _useContext = (0, import_react3.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = props.prefixCls || getPrefixCls("pro");
  var contentWidth = propsContentWidth || value.contentWidth;
  var className = "".concat(prefixCls, "-grid-content");
  var _useStyle = useStyle3(className), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var isWide = contentWidth === "Fixed" && value.layout === "top";
  return wrapSSR((0, import_jsx_runtime4.jsx)("div", {
    className: (0, import_classnames2.default)(className, hashId, propsClassName, _defineProperty({}, "".concat(className, "-wide"), isWide)),
    style,
    children: (0, import_jsx_runtime4.jsx)("div", {
      className: "".concat(prefixCls, "-grid-content-children ").concat(hashId).trim(),
      children
    })
  }));
};

// node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js
init_defineProperty();
init_typeof();
var import_classnames5 = __toESM(require_classnames());
var import_react6 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/PageHeader/index.js
init_defineProperty();
var import_ArrowLeftOutlined = __toESM(require_ArrowLeftOutlined3());
var import_ArrowRightOutlined = __toESM(require_ArrowRightOutlined3());
var import_classnames3 = __toESM(require_classnames());
var React3 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/PageHeader/style/index.js
init_defineProperty();
var textOverflowEllipsis = function textOverflowEllipsis2() {
  return {
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis"
  };
};
var genPageHeaderStyle = function genPageHeaderStyle2(token) {
  var _token$layout;
  return _defineProperty({}, token.componentCls, _objectSpread2(_objectSpread2({}, resetComponent === null || resetComponent === void 0 ? void 0 : resetComponent(token)), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    position: "relative",
    backgroundColor: token.colorWhite,
    paddingBlock: token.pageHeaderPaddingVertical + 2,
    paddingInline: token.pageHeaderPadding,
    "&&-ghost": {
      backgroundColor: token.pageHeaderBgGhost
    },
    "&-no-children": {
      height: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.pageContainer) === null || _token$layout === void 0 ? void 0 : _token$layout.paddingBlockPageContainerContent
    },
    "&&-has-breadcrumb": {
      paddingBlockStart: token.pageHeaderPaddingBreadCrumb
    },
    "&&-has-footer": {
      paddingBlockEnd: 0
    },
    "& &-back": _defineProperty({
      marginInlineEnd: token.margin,
      fontSize: 16,
      lineHeight: 1,
      "&-button": _objectSpread2(_objectSpread2({
        fontSize: 16
      }, operationUnit === null || operationUnit === void 0 ? void 0 : operationUnit(token)), {}, {
        color: token.pageHeaderColorBack,
        cursor: "pointer"
      })
    }, "".concat(token.componentCls, "-rlt &"), {
      float: "right",
      marginInlineEnd: 0,
      marginInlineStart: 0
    })
  }, "& ".concat("ant", "-divider-vertical"), {
    height: 14,
    marginBlock: 0,
    marginInline: token.marginSM,
    verticalAlign: "middle"
  }), "& &-breadcrumb + &-heading", {
    marginBlockStart: token.marginXS
  }), "& &-heading", {
    display: "flex",
    justifyContent: "space-between",
    "&-left": {
      display: "flex",
      alignItems: "center",
      marginBlock: token.marginXS / 2,
      marginInlineEnd: 0,
      marginInlineStart: 0,
      overflow: "hidden"
    },
    "&-title": _objectSpread2(_objectSpread2({
      marginInlineEnd: token.marginSM,
      marginBlockEnd: 0,
      color: token.colorTextHeading,
      fontWeight: 600,
      fontSize: token.pageHeaderFontSizeHeaderTitle,
      lineHeight: token.controlHeight + "px"
    }, textOverflowEllipsis()), {}, _defineProperty({}, "".concat(token.componentCls, "-rlt &"), {
      marginInlineEnd: 0,
      marginInlineStart: token.marginSM
    })),
    "&-avatar": _defineProperty({
      marginInlineEnd: token.marginSM
    }, "".concat(token.componentCls, "-rlt &"), {
      float: "right",
      marginInlineEnd: 0,
      marginInlineStart: token.marginSM
    }),
    "&-tags": _defineProperty({}, "".concat(token.componentCls, "-rlt &"), {
      float: "right"
    }),
    "&-sub-title": _objectSpread2(_objectSpread2({
      marginInlineEnd: token.marginSM,
      color: token.colorTextSecondary,
      fontSize: token.pageHeaderFontSizeHeaderSubTitle,
      lineHeight: token.lineHeight
    }, textOverflowEllipsis()), {}, _defineProperty({}, "".concat(token.componentCls, "-rlt &"), {
      float: "right",
      marginInlineEnd: 0,
      marginInlineStart: 12
    })),
    "&-extra": _defineProperty(_defineProperty({
      marginBlock: token.marginXS / 2,
      marginInlineEnd: 0,
      marginInlineStart: 0,
      whiteSpace: "nowrap",
      "> *": _defineProperty({
        "white-space": "unset"
      }, "".concat(token.componentCls, "-rlt &"), {
        marginInlineEnd: token.marginSM,
        marginInlineStart: 0
      })
    }, "".concat(token.componentCls, "-rlt &"), {
      float: "left"
    }), "*:first-child", _defineProperty({}, "".concat(token.componentCls, "-rlt &"), {
      marginInlineEnd: 0
    }))
  }), "&-content", {
    paddingBlockStart: token.pageHeaderPaddingContentPadding
  }), "&-footer", {
    marginBlockStart: token.margin
  }), "&-compact &-heading", {
    flexWrap: "wrap"
  }), "&-wide", {
    maxWidth: 1152,
    margin: "0 auto"
  }), "&-rtl", {
    direction: "rtl"
  })));
};
function useStyle4(prefixCls) {
  return useStyle("ProLayoutPageHeader", function(token) {
    var proCardToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls),
      pageHeaderBgGhost: "transparent",
      pageHeaderPadding: 16,
      pageHeaderPaddingVertical: 4,
      pageHeaderPaddingBreadCrumb: token.paddingSM,
      pageHeaderColorBack: token.colorTextHeading,
      pageHeaderFontSizeHeaderTitle: token.fontSizeHeading4,
      pageHeaderFontSizeHeaderSubTitle: 14,
      pageHeaderPaddingContentPadding: token.paddingSM
    });
    return [genPageHeaderStyle(proCardToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/PageHeader/index.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var renderBack = function renderBack2(prefixCls, hashId, backIcon, onBack) {
  if (!backIcon || !onBack) {
    return null;
  }
  return (0, import_jsx_runtime5.jsx)("div", {
    className: "".concat(prefixCls, "-back ").concat(hashId).trim(),
    children: (0, import_jsx_runtime5.jsx)("div", {
      role: "button",
      onClick: function onClick(e) {
        onBack === null || onBack === void 0 || onBack(e);
      },
      className: "".concat(prefixCls, "-back-button ").concat(hashId).trim(),
      "aria-label": "back",
      children: backIcon
    })
  });
};
var renderBreadcrumb = function renderBreadcrumb2(breadcrumb, prefixCls) {
  var _breadcrumb$items;
  if (!((_breadcrumb$items = breadcrumb.items) !== null && _breadcrumb$items !== void 0 && _breadcrumb$items.length))
    return null;
  return (0, import_jsx_runtime5.jsx)(breadcrumb_default, _objectSpread2(_objectSpread2({}, breadcrumb), {}, {
    className: (0, import_classnames3.default)("".concat(prefixCls, "-breadcrumb"), breadcrumb.className)
  }));
};
var getBackIcon = function getBackIcon2(props) {
  var direction = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "ltr";
  if (props.backIcon !== void 0) {
    return props.backIcon;
  }
  return direction === "rtl" ? (0, import_jsx_runtime5.jsx)(import_ArrowRightOutlined.default, {}) : (0, import_jsx_runtime5.jsx)(import_ArrowLeftOutlined.default, {});
};
var renderTitle = function renderTitle2(prefixCls, props) {
  var direction = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "ltr";
  var hashId = arguments.length > 3 ? arguments[3] : void 0;
  var title = props.title, avatar = props.avatar, subTitle = props.subTitle, tags = props.tags, extra = props.extra, onBack = props.onBack;
  var headingPrefixCls = "".concat(prefixCls, "-heading");
  var hasHeading = title || subTitle || tags || extra;
  if (!hasHeading) {
    return null;
  }
  var backIcon = getBackIcon(props, direction);
  var backIconDom = renderBack(prefixCls, hashId, backIcon, onBack);
  var hasTitle = backIconDom || avatar || hasHeading;
  return (0, import_jsx_runtime6.jsxs)("div", {
    className: headingPrefixCls + " " + hashId,
    children: [hasTitle && (0, import_jsx_runtime6.jsxs)("div", {
      className: "".concat(headingPrefixCls, "-left ").concat(hashId).trim(),
      children: [backIconDom, avatar && (0, import_jsx_runtime5.jsx)(avatar_default, _objectSpread2({
        className: (0, import_classnames3.default)("".concat(headingPrefixCls, "-avatar"), hashId, avatar.className)
      }, avatar)), title && (0, import_jsx_runtime5.jsx)("span", {
        className: "".concat(headingPrefixCls, "-title ").concat(hashId).trim(),
        title: typeof title === "string" ? title : void 0,
        children: title
      }), subTitle && (0, import_jsx_runtime5.jsx)("span", {
        className: "".concat(headingPrefixCls, "-sub-title ").concat(hashId).trim(),
        title: typeof subTitle === "string" ? subTitle : void 0,
        children: subTitle
      }), tags && (0, import_jsx_runtime5.jsx)("span", {
        className: "".concat(headingPrefixCls, "-tags ").concat(hashId).trim(),
        children: tags
      })]
    }), extra && (0, import_jsx_runtime5.jsx)("span", {
      className: "".concat(headingPrefixCls, "-extra ").concat(hashId).trim(),
      children: (0, import_jsx_runtime5.jsx)(space_default, {
        children: extra
      })
    })]
  });
};
var renderFooter = function renderFooter2(prefixCls, footer, hashId) {
  if (footer) {
    return (0, import_jsx_runtime5.jsx)("div", {
      className: "".concat(prefixCls, "-footer ").concat(hashId).trim(),
      children: footer
    });
  }
  return null;
};
var renderChildren = function renderChildren2(prefixCls, children, hashId) {
  return (0, import_jsx_runtime5.jsx)("div", {
    className: "".concat(prefixCls, "-content ").concat(hashId).trim(),
    children
  });
};
var transformBreadcrumbRoutesToItems = function transformBreadcrumbRoutesToItems2(routes) {
  return routes === null || routes === void 0 ? void 0 : routes.map(function(route) {
    var _route$children;
    noteOnce(!!route.breadcrumbName, "Route.breadcrumbName is deprecated, please use Route.title instead.");
    return _objectSpread2(_objectSpread2({}, route), {}, {
      breadcrumbName: void 0,
      children: void 0,
      title: route.title || route.breadcrumbName
    }, (_route$children = route.children) !== null && _route$children !== void 0 && _route$children.length ? {
      menu: {
        items: transformBreadcrumbRoutesToItems2(route.children)
      }
    } : {});
  });
};
var PageHeader = function PageHeader2(props) {
  var _breadcrumbRender;
  var _React$useState = React3.useState(false), _React$useState2 = _slicedToArray(_React$useState, 2), compact = _React$useState2[0], updateCompact = _React$useState2[1];
  var onResize = function onResize2(_ref) {
    var width = _ref.width;
    return updateCompact(width < 768);
  };
  var _React$useContext = React3.useContext(config_provider_default.ConfigContext), getPrefixCls = _React$useContext.getPrefixCls, direction = _React$useContext.direction;
  var customizePrefixCls = props.prefixCls, style = props.style, footer = props.footer, children = props.children, breadcrumb = props.breadcrumb, breadcrumbRender = props.breadcrumbRender, customizeClassName = props.className, contentWidth = props.contentWidth, layout = props.layout, _props$ghost = props.ghost, ghost = _props$ghost === void 0 ? true : _props$ghost;
  var prefixCls = getPrefixCls("page-header", customizePrefixCls);
  var _useStyle = useStyle4(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var getDefaultBreadcrumbDom = function getDefaultBreadcrumbDom2() {
    if (breadcrumb && !(breadcrumb !== null && breadcrumb !== void 0 && breadcrumb.items) && breadcrumb !== null && breadcrumb !== void 0 && breadcrumb.routes) {
      noteOnce(false, "The routes of Breadcrumb is deprecated, please use items instead.");
      breadcrumb.items = transformBreadcrumbRoutesToItems(breadcrumb.routes);
    }
    if (breadcrumb !== null && breadcrumb !== void 0 && breadcrumb.items) {
      return renderBreadcrumb(breadcrumb, prefixCls);
    }
    return null;
  };
  var defaultBreadcrumbDom = getDefaultBreadcrumbDom();
  var isBreadcrumbComponent = breadcrumb && "props" in breadcrumb;
  var breadcrumbRenderDomFromProps = (_breadcrumbRender = breadcrumbRender === null || breadcrumbRender === void 0 ? void 0 : breadcrumbRender(_objectSpread2(_objectSpread2({}, props), {}, {
    prefixCls
  }), defaultBreadcrumbDom)) !== null && _breadcrumbRender !== void 0 ? _breadcrumbRender : defaultBreadcrumbDom;
  var breadcrumbDom = isBreadcrumbComponent ? breadcrumb : breadcrumbRenderDomFromProps;
  var className = (0, import_classnames3.default)(prefixCls, hashId, customizeClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(prefixCls, "-has-breadcrumb"), !!breadcrumbDom), "".concat(prefixCls, "-has-footer"), !!footer), "".concat(prefixCls, "-rtl"), direction === "rtl"), "".concat(prefixCls, "-compact"), compact), "".concat(prefixCls, "-wide"), contentWidth === "Fixed" && layout == "top"), "".concat(prefixCls, "-ghost"), ghost));
  var title = renderTitle(prefixCls, props, direction, hashId);
  var childDom = children && renderChildren(prefixCls, children, hashId);
  var footerDom = renderFooter(prefixCls, footer, hashId);
  if (!breadcrumbDom && !title && !footerDom && !childDom) {
    return (0, import_jsx_runtime5.jsx)("div", {
      className: (0, import_classnames3.default)(hashId, ["".concat(prefixCls, "-no-children")])
    });
  }
  return wrapSSR((0, import_jsx_runtime5.jsx)(es_default, {
    onResize,
    children: (0, import_jsx_runtime6.jsxs)("div", {
      className,
      style,
      children: [breadcrumbDom, title, childDom, footerDom]
    })
  }));
};

// node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js
var import_react4 = __toESM(require_react());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var _excluded2 = ["isLoading", "pastDelay", "timedOut", "error", "retry"];
var PageLoading = function PageLoading2(_ref) {
  var isLoading = _ref.isLoading, pastDelay = _ref.pastDelay, timedOut = _ref.timedOut, error = _ref.error, retry = _ref.retry, reset = _objectWithoutProperties(_ref, _excluded2);
  return (0, import_jsx_runtime7.jsx)("div", {
    style: {
      paddingBlockStart: 100,
      textAlign: "center"
    },
    children: (0, import_jsx_runtime7.jsx)(spin_default, _objectSpread2({
      size: "large"
    }, reset))
  });
};

// node_modules/@ant-design/pro-layout/es/components/WaterMark/index.js
var import_classnames4 = __toESM(require_classnames());
var import_react5 = __toESM(require_react());
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var getPixelRatio = function getPixelRatio2(context) {
  if (!context) {
    return 1;
  }
  var backingStore = context.backingStorePixelRatio || context.webkitBackingStorePixelRatio || context.mozBackingStorePixelRatio || context.msBackingStorePixelRatio || context.oBackingStorePixelRatio || 1;
  return (window.devicePixelRatio || 1) / backingStore;
};
var WaterMark = function WaterMark2(props) {
  var _useToken = useToken(), token = _useToken.token;
  var children = props.children, style = props.style, className = props.className, markStyle = props.markStyle, markClassName = props.markClassName, _props$zIndex = props.zIndex, zIndex = _props$zIndex === void 0 ? 9 : _props$zIndex, _props$gapX = props.gapX, gapX = _props$gapX === void 0 ? 212 : _props$gapX, _props$gapY = props.gapY, gapY = _props$gapY === void 0 ? 222 : _props$gapY, _props$width = props.width, width = _props$width === void 0 ? 120 : _props$width, _props$height = props.height, height = _props$height === void 0 ? 64 : _props$height, _props$rotate = props.rotate, rotate = _props$rotate === void 0 ? -22 : _props$rotate, image = props.image, offsetLeft = props.offsetLeft, outOffsetTop = props.offsetTop, _props$fontStyle = props.fontStyle, fontStyle = _props$fontStyle === void 0 ? "normal" : _props$fontStyle, _props$fontWeight = props.fontWeight, fontWeight = _props$fontWeight === void 0 ? "normal" : _props$fontWeight, _props$fontColor = props.fontColor, fontColor = _props$fontColor === void 0 ? token.colorFill : _props$fontColor, _props$fontSize = props.fontSize, fontSize = _props$fontSize === void 0 ? 16 : _props$fontSize, _props$fontFamily = props.fontFamily, fontFamily = _props$fontFamily === void 0 ? "sans-serif" : _props$fontFamily, customizePrefixCls = props.prefixCls;
  var _useContext = (0, import_react5.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls("pro-layout-watermark", customizePrefixCls);
  var wrapperCls = (0, import_classnames4.default)("".concat(prefixCls, "-wrapper"), className);
  var waterMarkCls = (0, import_classnames4.default)(prefixCls, markClassName);
  var _useState = (0, import_react5.useState)(""), _useState2 = _slicedToArray(_useState, 2), base64Url = _useState2[0], setBase64Url = _useState2[1];
  (0, import_react5.useEffect)(function() {
    var canvas = document.createElement("canvas");
    var ctx = canvas.getContext("2d");
    var ratio = getPixelRatio(ctx);
    var canvasWidth = "".concat((gapX + width) * ratio, "px");
    var canvasHeight = "".concat((gapY + height) * ratio, "px");
    var canvasOffsetLeft = offsetLeft || gapX / 2;
    var canvasOffsetTop = outOffsetTop || gapY / 2;
    canvas.setAttribute("width", canvasWidth);
    canvas.setAttribute("height", canvasHeight);
    if (!ctx) {
      console.error("当前环境不支持Canvas");
      return;
    }
    ctx.translate(canvasOffsetLeft * ratio, canvasOffsetTop * ratio);
    ctx.rotate(Math.PI / 180 * Number(rotate));
    var markWidth = width * ratio;
    var markHeight = height * ratio;
    var writeContent = function writeContent2(contentText) {
      var offsetTop = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      var markSize = Number(fontSize) * ratio;
      ctx.font = "".concat(fontStyle, " normal ").concat(fontWeight, " ").concat(markSize, "px/").concat(markHeight, "px ").concat(fontFamily);
      ctx.fillStyle = fontColor;
      if (Array.isArray(contentText)) {
        contentText === null || contentText === void 0 || contentText.forEach(function(item, index) {
          return ctx.fillText(item, 0, index * markSize + offsetTop);
        });
      } else {
        ctx.fillText(contentText, 0, offsetTop ? offsetTop + markSize : 0);
      }
      setBase64Url(canvas.toDataURL());
    };
    if (image) {
      var img = new Image();
      img.crossOrigin = "anonymous";
      img.referrerPolicy = "no-referrer";
      img.src = image;
      img.onload = function() {
        ctx.drawImage(img, 0, 0, markWidth, markHeight);
        setBase64Url(canvas.toDataURL());
        if (props.content) {
          writeContent(props.content, img.height + 8);
          return;
        }
      };
      return;
    }
    if (props.content) {
      writeContent(props.content);
      return;
    }
  }, [gapX, gapY, offsetLeft, outOffsetTop, rotate, fontStyle, fontWeight, width, height, fontFamily, fontColor, image, props.content, fontSize]);
  return (0, import_jsx_runtime9.jsxs)("div", {
    style: _objectSpread2({
      position: "relative"
    }, style),
    className: wrapperCls,
    children: [children, (0, import_jsx_runtime8.jsx)("div", {
      className: waterMarkCls,
      style: _objectSpread2(_objectSpread2({
        zIndex,
        position: "absolute",
        left: 0,
        top: 0,
        width: "100%",
        height: "100%",
        backgroundSize: "".concat(gapX + width, "px"),
        pointerEvents: "none",
        backgroundRepeat: "repeat"
      }, base64Url ? {
        backgroundImage: "url('".concat(base64Url, "')")
      } : {}), markStyle)
    })]
  });
};

// node_modules/@ant-design/pro-layout/es/components/PageContainer/style/index.js
init_defineProperty();
var _map = [576, 768, 992, 1200].map(function(bp) {
  return "@media (max-width: ".concat(bp, "px)");
});
var _map2 = _slicedToArray(_map, 4);
var sm = _map2[0];
var md = _map2[1];
var lg = _map2[2];
var xl = _map2[3];
var genPageContainerStyle = function genPageContainerStyle2(token) {
  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout$pageCon, _token$layout5, _token$layout$pageCon2, _token$layout6, _token$layout7, _token$layout8, _token$layout$pageCon3, _token$layout9, _token$layout$pageCon4, _token$layout10, _token$layout$pageCon5, _token$layout11, _token$layout$pageCon6, _token$layout12;
  return _defineProperty({}, token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    position: "relative",
    "&-children-container": {
      paddingBlockStart: 0,
      paddingBlockEnd: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.pageContainer) === null || _token$layout === void 0 ? void 0 : _token$layout.paddingBlockPageContainerContent,
      paddingInline: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.pageContainer) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.paddingInlinePageContainerContent
    },
    "&-children-container-no-header": {
      paddingBlockStart: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.pageContainer) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.paddingBlockPageContainerContent
    },
    "&-affix": _defineProperty({}, "".concat(token.antCls, "-affix"), _defineProperty({}, "".concat(token.componentCls, "-warp"), {
      backgroundColor: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.pageContainer) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgPageContainerFixed,
      transition: "background-color 0.3s",
      boxShadow: "0 2px 8px #f0f1f2"
    }))
  }, "& &-warp-page-header", _defineProperty(_defineProperty(_defineProperty(_defineProperty({
    paddingBlockStart: ((_token$layout$pageCon = (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.pageContainer) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.paddingBlockPageContainerContent) !== null && _token$layout$pageCon !== void 0 ? _token$layout$pageCon : 40) / 4,
    paddingBlockEnd: ((_token$layout$pageCon2 = (_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.pageContainer) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.paddingBlockPageContainerContent) !== null && _token$layout$pageCon2 !== void 0 ? _token$layout$pageCon2 : 40) / 2,
    paddingInlineStart: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.pageContainer) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.paddingInlinePageContainerContent,
    paddingInlineEnd: (_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.pageContainer) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.paddingInlinePageContainerContent
  }, "& ~ ".concat(token.proComponentsCls, "-grid-content"), _defineProperty({}, "".concat(token.proComponentsCls, "-page-container-children-content"), {
    paddingBlock: ((_token$layout$pageCon3 = (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.pageContainer) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.paddingBlockPageContainerContent) !== null && _token$layout$pageCon3 !== void 0 ? _token$layout$pageCon3 : 24) / 3
  })), "".concat(token.antCls, "-page-header-breadcrumb"), {
    paddingBlockStart: ((_token$layout$pageCon4 = (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.pageContainer) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.paddingBlockPageContainerContent) !== null && _token$layout$pageCon4 !== void 0 ? _token$layout$pageCon4 : 40) / 4 + 10
  }), "".concat(token.antCls, "-page-header-heading"), {
    paddingBlockStart: ((_token$layout$pageCon5 = (_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.pageContainer) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.paddingBlockPageContainerContent) !== null && _token$layout$pageCon5 !== void 0 ? _token$layout$pageCon5 : 40) / 4
  }), "".concat(token.antCls, "-page-header-footer"), {
    marginBlockStart: ((_token$layout$pageCon6 = (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.pageContainer) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.paddingBlockPageContainerContent) !== null && _token$layout$pageCon6 !== void 0 ? _token$layout$pageCon6 : 40) / 4
  })), "&-detail", _defineProperty({
    display: "flex"
  }, sm, {
    display: "block"
  })), "&-main", {
    width: "100%"
  }), "&-row", _defineProperty({
    display: "flex",
    width: "100%"
  }, md, {
    display: "block"
  })), "&-content", {
    flex: "auto",
    width: "100%"
  }), "&-extraContent", _defineProperty(_defineProperty(_defineProperty(_defineProperty({
    flex: "0 1 auto",
    minWidth: "242px",
    marginInlineStart: 88,
    textAlign: "end"
  }, xl, {
    marginInlineStart: 44
  }), lg, {
    marginInlineStart: 20
  }), md, {
    marginInlineStart: 0,
    textAlign: "start"
  }), sm, {
    marginInlineStart: 0
  })));
};
function useStyle5(prefixCls, componentsToken) {
  return useStyle("ProLayoutPageContainer", function(token) {
    var _token$layout13;
    var proCardToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls),
      layout: _objectSpread2(_objectSpread2({}, token === null || token === void 0 ? void 0 : token.layout), {}, {
        pageContainer: _objectSpread2(_objectSpread2({}, token === null || token === void 0 || (_token$layout13 = token.layout) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.pageContainer), componentsToken)
      })
    });
    return [genPageContainerStyle(proCardToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/PageContainer/style/stylish.js
init_defineProperty();
function useStylish2(prefixCls, _ref) {
  var stylish = _ref.stylish;
  return useStyle("ProLayoutPageContainerStylish", function(token) {
    var stylishToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    if (!stylish)
      return [];
    return [_defineProperty({}, "div".concat(stylishToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(stylishToken))];
  });
}

// node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var _excluded3 = ["title", "content", "pageHeaderRender", "header", "prefixedClassName", "extraContent", "childrenContentStyle", "style", "prefixCls", "hashId", "value", "breadcrumbRender"];
var _excluded22 = ["children", "loading", "className", "style", "footer", "affixProps", "token", "fixedHeader", "breadcrumbRender", "footerToolBarProps", "childrenContentStyle"];
function genLoading(spinProps) {
  if (_typeof(spinProps) === "object") {
    return spinProps;
  }
  return {
    spinning: spinProps
  };
}
var renderFooter3 = function renderFooter4(_ref) {
  var tabList = _ref.tabList, tabActiveKey = _ref.tabActiveKey, onTabChange = _ref.onTabChange, hashId = _ref.hashId, tabBarExtraContent = _ref.tabBarExtraContent, tabProps = _ref.tabProps, prefixedClassName = _ref.prefixedClassName;
  if (Array.isArray(tabList) || tabBarExtraContent) {
    return (0, import_jsx_runtime10.jsx)(tabs_default, _objectSpread2(_objectSpread2({
      className: "".concat(prefixedClassName, "-tabs ").concat(hashId).trim(),
      activeKey: tabActiveKey,
      onChange: function onChange(key) {
        if (onTabChange) {
          onTabChange(key);
        }
      },
      tabBarExtraContent,
      items: tabList === null || tabList === void 0 ? void 0 : tabList.map(function(item, index) {
        var _item$key;
        return _objectSpread2(_objectSpread2({
          label: item.tab
        }, item), {}, {
          key: ((_item$key = item.key) === null || _item$key === void 0 ? void 0 : _item$key.toString()) || (index === null || index === void 0 ? void 0 : index.toString())
        });
      })
    }, tabProps), {}, {
      children: compareVersions(version_default, "4.23.0") < 0 ? tabList === null || tabList === void 0 ? void 0 : tabList.map(function(item, index) {
        return (0, import_jsx_runtime10.jsx)(tabs_default.TabPane, _objectSpread2({
          tab: item.tab
        }, item), item.key || index);
      }) : null
    }));
  }
  return null;
};
var renderPageHeader = function renderPageHeader2(content, extraContent, prefixedClassName, hashId) {
  if (!content && !extraContent) {
    return null;
  }
  return (0, import_jsx_runtime10.jsx)("div", {
    className: "".concat(prefixedClassName, "-detail ").concat(hashId).trim(),
    children: (0, import_jsx_runtime10.jsx)("div", {
      className: "".concat(prefixedClassName, "-main ").concat(hashId).trim(),
      children: (0, import_jsx_runtime11.jsxs)("div", {
        className: "".concat(prefixedClassName, "-row ").concat(hashId).trim(),
        children: [content && (0, import_jsx_runtime10.jsx)("div", {
          className: "".concat(prefixedClassName, "-content ").concat(hashId).trim(),
          children: content
        }), extraContent && (0, import_jsx_runtime10.jsx)("div", {
          className: "".concat(prefixedClassName, "-extraContent ").concat(hashId).trim(),
          children: extraContent
        })]
      })
    })
  });
};
var ProBreadcrumb = function ProBreadcrumb2(props) {
  var value = (0, import_react6.useContext)(RouteContext);
  return (0, import_jsx_runtime10.jsx)("div", {
    style: {
      height: "100%",
      display: "flex",
      alignItems: "center"
    },
    children: (0, import_jsx_runtime10.jsx)(breadcrumb_default, _objectSpread2(_objectSpread2(_objectSpread2({}, value === null || value === void 0 ? void 0 : value.breadcrumb), value === null || value === void 0 ? void 0 : value.breadcrumbProps), props))
  });
};
var memoRenderPageHeader = function memoRenderPageHeader2(props) {
  var _breadcrumb$items;
  var title = props.title, content = props.content, pageHeaderRender = props.pageHeaderRender, header = props.header, prefixedClassName = props.prefixedClassName, extraContent = props.extraContent, childrenContentStyle = props.childrenContentStyle, style = props.style, prefixCls = props.prefixCls, hashId = props.hashId, value = props.value, breadcrumbRender = props.breadcrumbRender, restProps = _objectWithoutProperties(props, _excluded3);
  var getBreadcrumbRender = function getBreadcrumbRender2() {
    if (!breadcrumbRender) {
      return void 0;
    }
    return breadcrumbRender;
  };
  if (pageHeaderRender === false) {
    return null;
  }
  if (pageHeaderRender) {
    return (0, import_jsx_runtime11.jsxs)(import_jsx_runtime12.Fragment, {
      children: [" ", pageHeaderRender(_objectSpread2(_objectSpread2({}, props), value))]
    });
  }
  var pageHeaderTitle = title;
  if (!title && title !== false) {
    pageHeaderTitle = value.title;
  }
  var pageHeaderProps = _objectSpread2(_objectSpread2(_objectSpread2({}, value), {}, {
    title: pageHeaderTitle
  }, restProps), {}, {
    footer: renderFooter3(_objectSpread2(_objectSpread2({}, restProps), {}, {
      hashId,
      breadcrumbRender,
      prefixedClassName
    }))
  }, header);
  var _ref2 = pageHeaderProps, breadcrumb = _ref2.breadcrumb;
  var noHasBreadCrumb = (!breadcrumb || !(breadcrumb !== null && breadcrumb !== void 0 && breadcrumb.itemRender) && !(breadcrumb !== null && breadcrumb !== void 0 && (_breadcrumb$items = breadcrumb.items) !== null && _breadcrumb$items !== void 0 && _breadcrumb$items.length)) && !breadcrumbRender;
  if (["title", "subTitle", "extra", "tags", "footer", "avatar", "backIcon"].every(function(item) {
    return !pageHeaderProps[item];
  }) && noHasBreadCrumb && !content && !extraContent) {
    return null;
  }
  return (0, import_jsx_runtime10.jsx)(PageHeader, _objectSpread2(_objectSpread2({}, pageHeaderProps), {}, {
    className: "".concat(prefixedClassName, "-warp-page-header ").concat(hashId).trim(),
    breadcrumb: breadcrumbRender === false ? void 0 : _objectSpread2(_objectSpread2({}, pageHeaderProps.breadcrumb), value.breadcrumbProps),
    breadcrumbRender: getBreadcrumbRender(),
    prefixCls,
    children: (header === null || header === void 0 ? void 0 : header.children) || renderPageHeader(content, extraContent, prefixedClassName, hashId)
  }));
};
var PageContainerBase = function PageContainerBase2(props) {
  var _restProps$header2, _token$layout;
  var children = props.children, _props$loading = props.loading, loading = _props$loading === void 0 ? false : _props$loading, className = props.className, style = props.style, footer = props.footer, affixProps = props.affixProps, propsToken = props.token, fixedHeader = props.fixedHeader, breadcrumbRender = props.breadcrumbRender, footerToolBarProps = props.footerToolBarProps, childrenContentStyle = props.childrenContentStyle, restProps = _objectWithoutProperties(props, _excluded22);
  var value = (0, import_react6.useContext)(RouteContext);
  (0, import_react6.useEffect)(function() {
    var _value$setHasPageCont;
    if (!value || !(value !== null && value !== void 0 && value.setHasPageContainer)) {
      return function() {
      };
    }
    value === null || value === void 0 || (_value$setHasPageCont = value.setHasPageContainer) === null || _value$setHasPageCont === void 0 || _value$setHasPageCont.call(value, function(num) {
      return num + 1;
    });
    return function() {
      var _value$setHasPageCont2;
      value === null || value === void 0 || (_value$setHasPageCont2 = value.setHasPageContainer) === null || _value$setHasPageCont2 === void 0 || _value$setHasPageCont2.call(value, function(num) {
        return num - 1;
      });
    };
  }, []);
  var _useContext = (0, import_react6.useContext)(ProProvider), token = _useContext.token;
  var _useContext2 = (0, import_react6.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext2.getPrefixCls;
  var prefixCls = props.prefixCls || getPrefixCls("pro");
  var basePageContainer = "".concat(prefixCls, "-page-container");
  var _useStyle = useStyle5(basePageContainer, propsToken), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var stylish = useStylish2("".concat(basePageContainer, ".").concat(basePageContainer, "-stylish"), {
    stylish: props.stylish
  });
  var memoBreadcrumbRender = (0, import_react6.useMemo)(function() {
    var _restProps$header;
    if (breadcrumbRender == false)
      return false;
    return breadcrumbRender || (restProps === null || restProps === void 0 || (_restProps$header = restProps.header) === null || _restProps$header === void 0 ? void 0 : _restProps$header.breadcrumbRender);
  }, [breadcrumbRender, restProps === null || restProps === void 0 || (_restProps$header2 = restProps.header) === null || _restProps$header2 === void 0 ? void 0 : _restProps$header2.breadcrumbRender]);
  var pageHeaderDom = memoRenderPageHeader(_objectSpread2(_objectSpread2({}, restProps), {}, {
    breadcrumbRender: memoBreadcrumbRender,
    ghost: true,
    hashId,
    prefixCls: void 0,
    prefixedClassName: basePageContainer,
    value
  }));
  var loadingDom = (0, import_react6.useMemo)(function() {
    if (import_react6.default.isValidElement(loading)) {
      return loading;
    }
    if (typeof loading === "boolean" && !loading) {
      return null;
    }
    var spinProps = genLoading(loading);
    return spinProps.spinning ? (0, import_jsx_runtime10.jsx)(PageLoading, _objectSpread2({}, spinProps)) : null;
  }, [loading]);
  var content = (0, import_react6.useMemo)(function() {
    return children ? (0, import_jsx_runtime10.jsx)(import_jsx_runtime12.Fragment, {
      children: (0, import_jsx_runtime10.jsx)("div", {
        className: (0, import_classnames5.default)(hashId, "".concat(basePageContainer, "-children-container"), _defineProperty({}, "".concat(basePageContainer, "-children-container-no-header"), !pageHeaderDom)),
        style: childrenContentStyle,
        children
      })
    }) : null;
  }, [children, basePageContainer, childrenContentStyle, hashId]);
  var renderContentDom = (0, import_react6.useMemo)(function() {
    var dom = loadingDom || content;
    if (props.waterMarkProps || value.waterMarkProps) {
      var waterMarkProps = _objectSpread2(_objectSpread2({}, value.waterMarkProps), props.waterMarkProps);
      return (0, import_jsx_runtime10.jsx)(WaterMark, _objectSpread2(_objectSpread2({}, waterMarkProps), {}, {
        children: dom
      }));
    }
    return dom;
  }, [props.waterMarkProps, value.waterMarkProps, loadingDom, content]);
  var containerClassName = (0, import_classnames5.default)(basePageContainer, hashId, className, _defineProperty(_defineProperty(_defineProperty({}, "".concat(basePageContainer, "-with-footer"), footer), "".concat(basePageContainer, "-with-affix"), fixedHeader && pageHeaderDom), "".concat(basePageContainer, "-stylish"), !!restProps.stylish));
  return wrapSSR(stylish.wrapSSR((0, import_jsx_runtime11.jsxs)(import_jsx_runtime12.Fragment, {
    children: [(0, import_jsx_runtime11.jsxs)("div", {
      style,
      className: containerClassName,
      children: [fixedHeader && pageHeaderDom ? (
        // 在 hasHeader 且 fixedHeader 的情况下，才需要设置高度
        (0, import_jsx_runtime10.jsx)(affix_default, _objectSpread2(_objectSpread2({
          offsetTop: value.hasHeader && value.fixedHeader ? (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader : 1
        }, affixProps), {}, {
          className: "".concat(basePageContainer, "-affix ").concat(hashId).trim(),
          children: (0, import_jsx_runtime10.jsx)("div", {
            className: "".concat(basePageContainer, "-warp ").concat(hashId).trim(),
            children: pageHeaderDom
          })
        }))
      ) : pageHeaderDom, renderContentDom && (0, import_jsx_runtime10.jsx)(GridContent, {
        children: renderContentDom
      })]
    }), footer && (0, import_jsx_runtime10.jsx)(FooterToolbar, _objectSpread2(_objectSpread2({
      stylish: restProps.footerStylish,
      prefixCls
    }, footerToolBarProps), {}, {
      children: footer
    }))]
  })));
};
var PageContainer = function PageContainer2(props) {
  return (0, import_jsx_runtime10.jsx)(ProConfigProvider, {
    needDeps: true,
    children: (0, import_jsx_runtime10.jsx)(PageContainerBase, _objectSpread2({}, props))
  });
};
var ProPageHeader = function ProPageHeader2(props) {
  var value = (0, import_react6.useContext)(RouteContext);
  return memoRenderPageHeader(_objectSpread2(_objectSpread2({}, props), {}, {
    hashId: "",
    value
  }));
};

// node_modules/@ant-design/pro-layout/es/components/Footer.js
var import_react8 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.js
var import_classnames6 = __toESM(require_classnames());
var import_react7 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/GlobalFooter/style.js
init_defineProperty();
var genFooterToolBarStyle3 = function genFooterToolBarStyle4(token) {
  return _defineProperty({}, token.componentCls, {
    marginBlock: 0,
    marginBlockStart: 48,
    marginBlockEnd: 24,
    marginInline: 0,
    paddingBlock: 0,
    paddingInline: 16,
    textAlign: "center",
    "&-list": {
      marginBlockEnd: 8,
      color: token.colorTextSecondary,
      "&-link": {
        color: token.colorTextSecondary,
        textDecoration: token.linkDecoration
      },
      "*:not(:last-child)": {
        marginInlineEnd: 8
      },
      "&:hover": {
        color: token.colorPrimary
      }
    },
    "&-copyright": {
      fontSize: "14px",
      color: token.colorText
    }
  });
};
function useStyle6(prefixCls) {
  return useStyle("ProLayoutFooter", function(token) {
    var proCardToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genFooterToolBarStyle3(proCardToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var GlobalFooter = function GlobalFooter2(_ref) {
  var className = _ref.className, prefixCls = _ref.prefixCls, links = _ref.links, copyright = _ref.copyright, style = _ref.style;
  var context = (0, import_react7.useContext)(config_provider_default.ConfigContext);
  var baseClassName = context.getPrefixCls(prefixCls || "pro-global-footer");
  var _useStyle = useStyle6(baseClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  if ((links == null || links === false || Array.isArray(links) && links.length === 0) && (copyright == null || copyright === false)) {
    return null;
  }
  return wrapSSR((0, import_jsx_runtime14.jsxs)("div", {
    className: (0, import_classnames6.default)(baseClassName, hashId, className),
    style,
    children: [links && (0, import_jsx_runtime13.jsx)("div", {
      className: "".concat(baseClassName, "-list ").concat(hashId).trim(),
      children: links.map(function(link) {
        return (0, import_jsx_runtime13.jsx)("a", {
          className: "".concat(baseClassName, "-list-link ").concat(hashId).trim(),
          title: link.key,
          target: link.blankTarget ? "_blank" : "_self",
          href: link.href,
          rel: "noreferrer",
          children: link.title
        }, link.key);
      })
    }), copyright && (0, import_jsx_runtime13.jsx)("div", {
      className: "".concat(baseClassName, "-copyright ").concat(hashId).trim(),
      children: copyright
    })]
  }));
};

// node_modules/@ant-design/pro-layout/es/components/Footer.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var Footer = layout_default.Footer;
var DefaultFooter = function DefaultFooter2(_ref) {
  var links = _ref.links, copyright = _ref.copyright, style = _ref.style, className = _ref.className, prefixCls = _ref.prefixCls;
  return (0, import_jsx_runtime15.jsx)(Footer, {
    className,
    style: _objectSpread2({
      padding: 0
    }, style),
    children: (0, import_jsx_runtime15.jsx)(GlobalFooter, {
      links,
      prefixCls,
      copyright: copyright === false ? null : (0, import_jsx_runtime16.jsxs)(import_react8.Fragment, {
        children: [(0, import_jsx_runtime15.jsx)(CopyrightOutlined_default, {}), " ", copyright]
      })
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/Header/index.js
init_defineProperty();
var import_classnames14 = __toESM(require_classnames());
var import_react20 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/utils/utils.js
var getOpenKeysFromMenuData = function getOpenKeysFromMenuData2(menuData) {
  return (menuData || []).reduce(function(pre, item) {
    if (item.key) {
      pre.push(item.key);
    }
    if (item.children || item.routes) {
      var newArray = pre.concat(getOpenKeysFromMenuData2(item.children || item.routes) || []);
      return newArray;
    }
    return pre;
  }, []);
};
var themeConfig = {
  techBlue: "#1677FF",
  daybreak: "#1890ff",
  dust: "#F5222D",
  volcano: "#FA541C",
  sunset: "#FAAD14",
  cyan: "#13C2C2",
  green: "#52C41A",
  geekblue: "#2F54EB",
  purple: "#722ED1"
};
function genStringToTheme(val) {
  return val && themeConfig[val] ? themeConfig[val] : val || "";
}
function clearMenuItem(menusData) {
  return menusData.map(function(item) {
    var children = item.children || [];
    var finalItem = _objectSpread2({}, item);
    if (!finalItem.children && finalItem.routes) {
      finalItem.children = finalItem.routes;
    }
    if (!finalItem.name || finalItem.hideInMenu) {
      return null;
    }
    if (finalItem && finalItem !== null && finalItem !== void 0 && finalItem.children) {
      if (!finalItem.hideChildrenInMenu && children.some(function(child) {
        return child && child.name && !child.hideInMenu;
      })) {
        return _objectSpread2(_objectSpread2({}, item), {}, {
          children: clearMenuItem(children)
        });
      }
      delete finalItem.children;
    }
    delete finalItem.routes;
    return finalItem;
  }).filter(function(item) {
    return item;
  });
}

// node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js
init_defineProperty();
var import_classnames13 = __toESM(require_classnames());
var import_react19 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/index.js
init_defineProperty();
var import_classnames7 = __toESM(require_classnames());
var import_react11 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/AppsLogo.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
var AppsLogo = function AppsLogo2() {
  return (0, import_jsx_runtime17.jsx)("svg", {
    width: "1em",
    height: "1em",
    viewBox: "0 0 12 12",
    fill: "currentColor",
    "aria-hidden": "true",
    children: (0, import_jsx_runtime17.jsx)("path", {
      d: "M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/DefaultContent.js
var import_react9 = __toESM(require_react());
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
var import_jsx_runtime19 = __toESM(require_jsx_runtime());
var DefaultContent = function DefaultContent2(props) {
  var appList = props.appList, baseClassName = props.baseClassName, hashId = props.hashId, itemClick = props.itemClick;
  return (0, import_jsx_runtime18.jsx)("div", {
    className: "".concat(baseClassName, "-content ").concat(hashId).trim(),
    children: (0, import_jsx_runtime18.jsx)("ul", {
      className: "".concat(baseClassName, "-content-list ").concat(hashId).trim(),
      children: appList === null || appList === void 0 ? void 0 : appList.map(function(app, index) {
        var _app$children;
        if (app !== null && app !== void 0 && (_app$children = app.children) !== null && _app$children !== void 0 && _app$children.length) {
          return (0, import_jsx_runtime19.jsxs)("div", {
            className: "".concat(baseClassName, "-content-list-item-group ").concat(hashId).trim(),
            children: [(0, import_jsx_runtime18.jsx)("div", {
              className: "".concat(baseClassName, "-content-list-item-group-title ").concat(hashId).trim(),
              children: app.title
            }), (0, import_jsx_runtime18.jsx)(DefaultContent2, {
              hashId,
              itemClick,
              appList: app === null || app === void 0 ? void 0 : app.children,
              baseClassName
            })]
          }, index);
        }
        return (0, import_jsx_runtime18.jsx)("li", {
          className: "".concat(baseClassName, "-content-list-item ").concat(hashId).trim(),
          onClick: function onClick(e) {
            e.stopPropagation();
            itemClick === null || itemClick === void 0 || itemClick(app);
          },
          children: (0, import_jsx_runtime19.jsxs)("a", {
            href: itemClick ? void 0 : app.url,
            target: app.target,
            rel: "noreferrer",
            children: [defaultRenderLogo(app.icon), (0, import_jsx_runtime19.jsxs)("div", {
              children: [(0, import_jsx_runtime18.jsx)("div", {
                children: app.title
              }), app.desc ? (0, import_jsx_runtime18.jsx)("span", {
                children: app.desc
              }) : null]
            })]
          })
        }, index);
      })
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/SimpleContent.js
var import_react10 = __toESM(require_react());
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
var import_jsx_runtime21 = __toESM(require_jsx_runtime());
var renderLogo = function renderLogo2(logo, title) {
  if (logo && typeof logo === "string" && isUrl(logo)) {
    return (0, import_jsx_runtime20.jsx)("img", {
      src: logo,
      alt: "logo"
    });
  }
  if (typeof logo === "function") {
    return logo();
  }
  if (logo && typeof logo === "string") {
    return (0, import_jsx_runtime20.jsx)("div", {
      id: "avatarLogo",
      children: logo
    });
  }
  if (!logo && title && typeof title === "string") {
    var symbol = title.substring(0, 1);
    return (0, import_jsx_runtime20.jsx)("div", {
      id: "avatarLogo",
      children: symbol
    });
  }
  return logo;
};
var SimpleContent = function SimpleContent2(props) {
  var appList = props.appList, baseClassName = props.baseClassName, hashId = props.hashId, itemClick = props.itemClick;
  return (0, import_jsx_runtime20.jsx)("div", {
    className: "".concat(baseClassName, "-content ").concat(hashId).trim(),
    children: (0, import_jsx_runtime20.jsx)("ul", {
      className: "".concat(baseClassName, "-content-list ").concat(hashId).trim(),
      children: appList === null || appList === void 0 ? void 0 : appList.map(function(app, index) {
        var _app$children;
        if (app !== null && app !== void 0 && (_app$children = app.children) !== null && _app$children !== void 0 && _app$children.length) {
          return (0, import_jsx_runtime21.jsxs)("div", {
            className: "".concat(baseClassName, "-content-list-item-group ").concat(hashId).trim(),
            children: [(0, import_jsx_runtime20.jsx)("div", {
              className: "".concat(baseClassName, "-content-list-item-group-title ").concat(hashId).trim(),
              children: app.title
            }), (0, import_jsx_runtime20.jsx)(SimpleContent2, {
              hashId,
              itemClick,
              appList: app === null || app === void 0 ? void 0 : app.children,
              baseClassName
            })]
          }, index);
        }
        return (0, import_jsx_runtime20.jsx)("li", {
          className: "".concat(baseClassName, "-content-list-item ").concat(hashId).trim(),
          onClick: function onClick(e) {
            e.stopPropagation();
            itemClick === null || itemClick === void 0 || itemClick(app);
          },
          children: (0, import_jsx_runtime21.jsxs)("a", {
            href: itemClick ? "javascript:;" : app.url,
            target: app.target,
            rel: "noreferrer",
            children: [renderLogo(app.icon, app.title), (0, import_jsx_runtime20.jsx)("div", {
              children: (0, import_jsx_runtime20.jsx)("div", {
                children: app.title
              })
            })]
          })
        }, index);
      })
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/index.js
init_defineProperty();

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/default.js
var genAppsLogoComponentsDefaultListStyle = function genAppsLogoComponentsDefaultListStyle2(token) {
  return {
    "&-content": {
      maxHeight: "calc(100vh - 48px)",
      overflow: "auto",
      "&-list": {
        boxSizing: "content-box",
        maxWidth: 656,
        marginBlock: 0,
        marginInline: 0,
        paddingBlock: 0,
        paddingInline: 0,
        listStyle: "none",
        "&-item": {
          position: "relative",
          display: "inline-block",
          width: 328,
          height: 72,
          paddingInline: 16,
          paddingBlock: 16,
          verticalAlign: "top",
          listStyleType: "none",
          transition: "transform 0.2s cubic-bezier(0.333, 0, 0, 1)",
          borderRadius: token.borderRadius,
          "&-group": {
            marginBottom: 16,
            "&-title": {
              margin: "16px 0 8px 12px",
              fontWeight: 600,
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: 16,
              opacity: 0.85,
              lineHeight: 1.5,
              "&:first-child": {
                marginTop: 12
              }
            }
          },
          "&:hover": {
            backgroundColor: token.colorBgTextHover
          },
          "* div": resetComponent === null || resetComponent === void 0 ? void 0 : resetComponent(token),
          a: {
            display: "flex",
            height: "100%",
            fontSize: 12,
            textDecoration: "none",
            "& > img": {
              width: 40,
              height: 40
            },
            "& > div": {
              marginInlineStart: 14,
              color: token.colorTextHeading,
              fontSize: 14,
              lineHeight: "22px",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis"
            },
            "& > div > span": {
              color: token.colorTextSecondary,
              fontSize: 12,
              lineHeight: "20px"
            }
          }
        }
      }
    }
  };
};

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/simple.js
var genAppsLogoComponentsSimpleListStyle = function genAppsLogoComponentsSimpleListStyle2(token) {
  return {
    "&-content": {
      maxHeight: "calc(100vh - 48px)",
      overflow: "auto",
      "&-list": {
        boxSizing: "border-box",
        maxWidth: 376,
        marginBlock: 0,
        marginInline: 0,
        paddingBlock: 0,
        paddingInline: 0,
        listStyle: "none",
        "&-item": {
          position: "relative",
          display: "inline-block",
          width: 104,
          height: 104,
          marginBlock: 8,
          marginInline: 8,
          paddingInline: 24,
          paddingBlock: 24,
          verticalAlign: "top",
          listStyleType: "none",
          transition: "transform 0.2s cubic-bezier(0.333, 0, 0, 1)",
          borderRadius: token.borderRadius,
          "&-group": {
            marginBottom: 16,
            "&-title": {
              margin: "16px 0 8px 12px",
              fontWeight: 600,
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: 16,
              opacity: 0.85,
              lineHeight: 1.5,
              "&:first-child": {
                marginTop: 12
              }
            }
          },
          "&:hover": {
            backgroundColor: token.colorBgTextHover
          },
          a: {
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            height: "100%",
            fontSize: 12,
            textDecoration: "none",
            "& > #avatarLogo": {
              width: 40,
              height: 40,
              margin: "0 auto",
              color: token.colorPrimary,
              fontSize: 22,
              lineHeight: "40px",
              textAlign: "center",
              backgroundImage: "linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",
              borderRadius: token.borderRadius
            },
            "& > img": {
              width: 40,
              height: 40
            },
            "& > div": {
              marginBlockStart: 5,
              marginInlineStart: 0,
              color: token.colorTextHeading,
              fontSize: 14,
              lineHeight: "22px",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis"
            },
            "& > div > span": {
              color: token.colorTextSecondary,
              fontSize: 12,
              lineHeight: "20px"
            }
          }
        }
      }
    }
  };
};

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/index.js
var genAppsLogoComponentsStyle = function genAppsLogoComponentsStyle2(token) {
  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5;
  return _defineProperty({}, token.componentCls, {
    "&-icon": {
      display: "inline-flex",
      alignItems: "center",
      justifyContent: "center",
      paddingInline: 4,
      paddingBlock: 0,
      fontSize: 14,
      lineHeight: "14px",
      height: 28,
      width: 28,
      cursor: "pointer",
      color: (_token$layout = token.layout) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextAppListIcon,
      borderRadius: token.borderRadius,
      "&:hover": {
        color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorTextAppListIconHover,
        backgroundColor: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgAppListIconHover
      },
      "&-active": {
        color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextAppListIconHover,
        backgroundColor: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgAppListIconHover
      }
    },
    "&-item-title": {
      marginInlineStart: "16px",
      marginInlineEnd: "8px",
      marginBlockStart: 0,
      marginBlockEnd: "12px",
      fontWeight: 600,
      color: "rgba(0, 0, 0, 0.88)",
      fontSize: 16,
      opacity: 0.85,
      lineHeight: 1.5,
      "&:first-child": {
        marginBlockStart: 12
      }
    },
    "&-popover": _defineProperty({}, "".concat(token.antCls, "-popover-arrow"), {
      display: "none"
    }),
    "&-simple": genAppsLogoComponentsSimpleListStyle(token),
    "&-default": genAppsLogoComponentsDefaultListStyle(token)
  });
};
function useStyle7(prefixCls) {
  return useStyle("AppsLogoComponents", function(token) {
    var proCardToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genAppsLogoComponentsStyle(proCardToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/index.js
var import_jsx_runtime22 = __toESM(require_jsx_runtime());
var import_jsx_runtime23 = __toESM(require_jsx_runtime());
var import_jsx_runtime24 = __toESM(require_jsx_runtime());
var defaultRenderLogo = function defaultRenderLogo2(logo) {
  if (typeof logo === "string") {
    return (0, import_jsx_runtime22.jsx)("img", {
      width: "auto",
      height: 22,
      src: logo,
      alt: "logo"
    });
  }
  if (typeof logo === "function") {
    return logo();
  }
  return logo;
};
var AppsLogoComponents = function AppsLogoComponents2(props) {
  var _props$appList;
  var appList = props.appList, appListRender = props.appListRender, _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? "ant-pro" : _props$prefixCls, itemClick = props.onItemClick;
  var ref = import_react11.default.useRef(null);
  var popoverRef = import_react11.default.useRef(null);
  var baseClassName = "".concat(prefixCls, "-layout-apps");
  var _useStyle = useStyle7(baseClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var _useState = (0, import_react11.useState)(false), _useState2 = _slicedToArray(_useState, 2), open = _useState2[0], setOpen = _useState2[1];
  var cloneItemClick = function cloneItemClick2(app) {
    itemClick === null || itemClick === void 0 || itemClick(app, popoverRef);
  };
  var defaultDomContent = (0, import_react11.useMemo)(function() {
    var isSimple = appList === null || appList === void 0 ? void 0 : appList.some(function(app) {
      return !(app !== null && app !== void 0 && app.desc);
    });
    if (isSimple) {
      return (0, import_jsx_runtime22.jsx)(SimpleContent, {
        hashId,
        appList,
        itemClick: itemClick ? cloneItemClick : void 0,
        baseClassName: "".concat(baseClassName, "-simple")
      });
    }
    return (0, import_jsx_runtime22.jsx)(DefaultContent, {
      hashId,
      appList,
      itemClick: itemClick ? cloneItemClick : void 0,
      baseClassName: "".concat(baseClassName, "-default")
    });
  }, [appList, baseClassName, hashId]);
  if (!(props !== null && props !== void 0 && (_props$appList = props.appList) !== null && _props$appList !== void 0 && _props$appList.length))
    return null;
  var popoverContent = appListRender ? appListRender(props === null || props === void 0 ? void 0 : props.appList, defaultDomContent) : defaultDomContent;
  var popoverOpenProps = openVisibleCompatible(void 0, function(openChange) {
    return setOpen(openChange);
  });
  return wrapSSR((0, import_jsx_runtime24.jsxs)(import_jsx_runtime23.Fragment, {
    children: [(0, import_jsx_runtime22.jsx)("div", {
      ref,
      onClick: function onClick(e) {
        e.stopPropagation();
        e.preventDefault();
      }
    }), (0, import_jsx_runtime22.jsx)(popover_default, _objectSpread2(_objectSpread2({
      placement: "bottomRight",
      trigger: ["click"],
      zIndex: 9999,
      arrow: false
    }, popoverOpenProps), {}, {
      overlayClassName: "".concat(baseClassName, "-popover ").concat(hashId).trim(),
      content: popoverContent,
      getPopupContainer: function getPopupContainer() {
        return ref.current || document.body;
      },
      children: (0, import_jsx_runtime22.jsx)("span", {
        ref: popoverRef,
        onClick: function onClick(e) {
          e.stopPropagation();
        },
        className: (0, import_classnames7.default)("".concat(baseClassName, "-icon"), hashId, _defineProperty({}, "".concat(baseClassName, "-icon-active"), open)),
        children: (0, import_jsx_runtime22.jsx)(AppsLogo, {})
      })
    }))]
  }));
};

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js
init_defineProperty();
var import_classnames10 = __toESM(require_classnames());
var import_react14 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/index.js
init_defineProperty();
var import_classnames8 = __toESM(require_classnames());

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/Arrow.js
var import_jsx_runtime25 = __toESM(require_jsx_runtime());
function ArrowSvgIcon() {
  return (0, import_jsx_runtime25.jsx)("svg", {
    width: "1em",
    height: "1em",
    viewBox: "0 0 12 12",
    fill: "currentColor",
    "aria-hidden": "true",
    children: (0, import_jsx_runtime25.jsx)("path", {
      d: "M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"
    })
  });
}

// node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/style.js
init_defineProperty();
var genSiderMenuStyle = function genSiderMenuStyle2(token) {
  var _token$layout, _token$layout2, _token$layout3;
  return _defineProperty({}, token.componentCls, {
    position: "absolute",
    insetBlockStart: "18px",
    zIndex: "101",
    width: "24px",
    height: "24px",
    fontSize: ["14px", "16px"],
    textAlign: "center",
    borderRadius: "40px",
    insetInlineEnd: "-13px",
    transition: "transform 0.3s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextCollapsedButton,
    backgroundColor: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgCollapsedButton,
    boxShadow: "0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)",
    "&:hover": {
      color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextCollapsedButtonHover,
      boxShadow: "0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"
    },
    ".anticon": {
      fontSize: "14px"
    },
    "& > svg": {
      transition: "transform  0.3s",
      transform: "rotate(90deg)"
    },
    "&-collapsed": {
      "& > svg": {
        transform: "rotate(-90deg)"
      }
    }
  });
};
function useStyle8(prefixCls) {
  return useStyle("SiderMenuCollapsedIcon", function(token) {
    var siderMenuToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genSiderMenuStyle(siderMenuToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/index.js
var import_jsx_runtime26 = __toESM(require_jsx_runtime());
var _excluded4 = ["isMobile", "collapsed"];
var CollapsedIcon = function CollapsedIcon2(props) {
  var isMobile = props.isMobile, collapsed = props.collapsed, rest = _objectWithoutProperties(props, _excluded4);
  var _useStyle = useStyle8(props.className), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  if (isMobile && collapsed)
    return null;
  return wrapSSR((0, import_jsx_runtime26.jsx)("div", _objectSpread2(_objectSpread2({}, rest), {}, {
    className: (0, import_classnames8.default)(props.className, hashId, _defineProperty(_defineProperty({}, "".concat(props.className, "-collapsed"), collapsed), "".concat(props.className, "-is-mobile"), isMobile)),
    children: (0, import_jsx_runtime26.jsx)(ArrowSvgIcon, {})
  })));
};

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js
init_defineProperty();
var import_classnames9 = __toESM(require_classnames());
var import_react12 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/defaultSettings.js
var defaultSettings = {
  navTheme: "light",
  layout: "side",
  contentWidth: "Fluid",
  fixedHeader: false,
  fixSiderbar: true,
  iconfontUrl: "",
  colorPrimary: "#1677FF",
  splitMenus: false
};

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/menu.js
init_defineProperty();
var genProLayoutBaseMenuStyle = function genProLayoutBaseMenuStyle2(token, mode) {
  var _token$layout, _token$layout2;
  var menuToken = mode.includes("horizontal") ? (_token$layout = token.layout) === null || _token$layout === void 0 ? void 0 : _token$layout.header : (_token$layout2 = token.layout) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.sider;
  return _objectSpread2(_objectSpread2(_defineProperty({}, "".concat(token.componentCls), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    background: "transparent",
    color: menuToken === null || menuToken === void 0 ? void 0 : menuToken.colorTextMenu,
    border: "none"
  }, "".concat(token.componentCls, "-menu-item"), {
    transition: "none !important"
  }), "".concat(token.componentCls, "-submenu-has-icon"), _defineProperty({}, "> ".concat(token.antCls, "-menu-sub"), {
    paddingInlineStart: 10
  })), "".concat(token.antCls, "-menu-title-content"), {
    width: "100%",
    height: "100%",
    display: "inline-flex"
  }), "".concat(token.antCls, "-menu-title-content"), {
    "&:first-child": {
      width: "100%"
    }
  }), "".concat(token.componentCls, "-item-icon"), {
    display: "flex",
    alignItems: "center"
  }), "&&-collapsed", _defineProperty(_defineProperty(_defineProperty({}, "".concat(token.antCls, "-menu-item, \n        ").concat(token.antCls, "-menu-item-group > ").concat(token.antCls, "-menu-item-group-list > ").concat(token.antCls, "-menu-item, \n        ").concat(token.antCls, "-menu-item-group > ").concat(token.antCls, "-menu-item-group-list > ").concat(token.antCls, "-menu-submenu > ").concat(token.antCls, "-menu-submenu-title, \n        ").concat(token.antCls, "-menu-submenu > ").concat(token.antCls, "-menu-submenu-title"), {
    paddingInline: "0 !important",
    marginBlock: "4px !important"
  }), "".concat(token.antCls, "-menu-item-group > ").concat(token.antCls, "-menu-item-group-list > ").concat(token.antCls, "-menu-submenu-selected > ").concat(token.antCls, "-menu-submenu-title, \n        ").concat(token.antCls, "-menu-submenu-selected > ").concat(token.antCls, "-menu-submenu-title"), {
    backgroundColor: menuToken === null || menuToken === void 0 ? void 0 : menuToken.colorBgMenuItemSelected,
    borderRadius: token.borderRadiusLG
  }), "".concat(token.componentCls, "-group"), _defineProperty({}, "".concat(token.antCls, "-menu-item-group-title"), {
    paddingInline: 0
  }))), "&-item-title", _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: token.marginXS
  }, "".concat(token.componentCls, "-item-text"), {
    maxWidth: "100%",
    textOverflow: "ellipsis",
    overflow: "hidden",
    wordBreak: "break-all",
    whiteSpace: "nowrap"
  }), "&-collapsed", _defineProperty(_defineProperty({
    minWidth: 40,
    height: 40
  }, "".concat(token.componentCls, "-item-icon"), {
    height: "16px",
    width: "16px",
    lineHeight: "16px !important",
    ".anticon": {
      lineHeight: "16px !important",
      height: "16px"
    }
  }), "".concat(token.componentCls, "-item-text-has-icon"), {
    display: "none !important"
  })), "&-collapsed-level-0", {
    flexDirection: "column",
    justifyContent: "center"
  }), "&".concat(token.componentCls, "-group-item-title"), {
    gap: token.marginXS,
    height: 18,
    overflow: "hidden"
  }), "&".concat(token.componentCls, "-item-collapsed-show-title"), _defineProperty({
    lineHeight: "16px",
    gap: 0
  }, "&".concat(token.componentCls, "-item-title-collapsed"), _defineProperty(_defineProperty({
    display: "flex"
  }, "".concat(token.componentCls, "-item-icon"), {
    height: "16px",
    width: "16px",
    lineHeight: "16px !important",
    ".anticon": {
      lineHeight: "16px!important",
      height: "16px"
    }
  }), "".concat(token.componentCls, "-item-text"), {
    opacity: "1 !important",
    display: "inline !important",
    textAlign: "center",
    fontSize: 12,
    height: 12,
    lineHeight: "12px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    width: "100%",
    margin: 0,
    padding: 0,
    marginBlockStart: 4
  })))), "&-group", _defineProperty({}, "".concat(token.antCls, "-menu-item-group-title"), {
    fontSize: 12,
    color: token.colorTextLabel,
    ".anticon": {
      marginInlineEnd: 8
    }
  })), "&-group-divider", {
    color: token.colorTextSecondary,
    fontSize: 12,
    lineHeight: 20
  })), mode.includes("horizontal") ? {} : _defineProperty({}, "".concat(token.antCls, "-menu-submenu").concat(token.antCls, "-menu-submenu-popup"), _defineProperty({}, "".concat(token.componentCls, "-item-title"), {
    alignItems: "flex-start"
  }))), {}, _defineProperty({}, "".concat(token.antCls, "-menu-submenu-popup"), {
    backgroundColor: "rgba(255, 255, 255, 0.42)",
    "-webkit-backdrop-filter": "blur(8px)",
    backdropFilter: "blur(8px)"
  }));
};
function useStyle9(prefixCls, mode) {
  return useStyle("ProLayoutBaseMenu" + mode, function(token) {
    var proLayoutMenuToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProLayoutBaseMenuStyle(proLayoutMenuToken, mode || "inline")];
  });
}

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js
var import_jsx_runtime27 = __toESM(require_jsx_runtime());
var import_jsx_runtime28 = __toESM(require_jsx_runtime());
var import_react13 = __toESM(require_react());
var MenuItemTooltip = function MenuItemTooltip2(props) {
  var _useState = (0, import_react12.useState)(props.collapsed), _useState2 = _slicedToArray(_useState, 2), collapsed = _useState2[0], setCollapsed = _useState2[1];
  var _useState3 = (0, import_react12.useState)(false), _useState4 = _slicedToArray(_useState3, 2), open = _useState4[0], setOpen = _useState4[1];
  (0, import_react12.useEffect)(function() {
    setOpen(false);
    setTimeout(function() {
      setCollapsed(props.collapsed);
    }, 400);
  }, [props.collapsed]);
  if (props.disable) {
    return props.children;
  }
  return (0, import_jsx_runtime27.jsx)(tooltip_default, {
    title: props.title,
    open: collapsed && props.collapsed ? open : false,
    placement: "right",
    onOpenChange: setOpen,
    children: props.children
  });
};
var IconFont = create({
  scriptUrl: defaultSettings.iconfontUrl
});
var getIcon = function getIcon2(icon) {
  var iconPrefixes = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "icon-";
  var className = arguments.length > 2 ? arguments[2] : void 0;
  if (typeof icon === "string" && icon !== "") {
    if (isUrl(icon) || isImg(icon)) {
      return (0, import_jsx_runtime27.jsx)("img", {
        width: 16,
        src: icon,
        alt: "icon",
        className
      }, icon);
    }
    if (icon.startsWith(iconPrefixes)) {
      return (0, import_jsx_runtime27.jsx)(IconFont, {
        type: icon
      });
    }
  }
  return icon;
};
var getMenuTitleSymbol = function getMenuTitleSymbol2(title) {
  if (title && typeof title === "string") {
    var symbol = title.substring(0, 1).toUpperCase();
    return symbol;
  }
  return null;
};
var MenuUtil = _createClass(function MenuUtil2(props) {
  var _this = this;
  _classCallCheck(this, MenuUtil2);
  _defineProperty(this, "props", void 0);
  _defineProperty(this, "getNavMenuItems", function() {
    var menusData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
    var level = arguments.length > 1 ? arguments[1] : void 0;
    var noGroupLevel = arguments.length > 2 ? arguments[2] : void 0;
    return menusData.map(function(item) {
      return _this.getSubMenuOrItem(item, level, noGroupLevel);
    }).filter(function(item) {
      return item;
    }).flat(1);
  });
  _defineProperty(this, "getSubMenuOrItem", function(item, level, noGroupLevel) {
    var _this$props = _this.props, subMenuItemRender = _this$props.subMenuItemRender, baseClassName = _this$props.baseClassName, prefixCls = _this$props.prefixCls, collapsed = _this$props.collapsed, menu = _this$props.menu, iconPrefixes = _this$props.iconPrefixes, layout = _this$props.layout;
    var isGroup = (menu === null || menu === void 0 ? void 0 : menu.type) === "group" && layout !== "top";
    var designToken = _this.props.token;
    var name = _this.getIntlName(item);
    var children = (item === null || item === void 0 ? void 0 : item.children) || (item === null || item === void 0 ? void 0 : item.routes);
    var menuType = isGroup && level === 0 ? "group" : void 0;
    if (Array.isArray(children) && children.length > 0) {
      var _this$props2, _this$props3, _this$props4, _this$props5, _designToken$layout;
      var shouldHasIcon = level === 0 || isGroup && level === 1;
      var iconDom = getIcon(item.icon, iconPrefixes, "".concat(baseClassName, "-icon ").concat((_this$props2 = _this.props) === null || _this$props2 === void 0 ? void 0 : _this$props2.hashId));
      var defaultIcon = collapsed && shouldHasIcon ? getMenuTitleSymbol(name) : null;
      var defaultTitle = (0, import_jsx_runtime28.jsxs)("div", {
        className: (0, import_classnames9.default)("".concat(baseClassName, "-item-title"), (_this$props3 = _this.props) === null || _this$props3 === void 0 ? void 0 : _this$props3.hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-item-title-collapsed"), collapsed), "".concat(baseClassName, "-item-title-collapsed-level-").concat(noGroupLevel), collapsed), "".concat(baseClassName, "-group-item-title"), menuType === "group"), "".concat(baseClassName, "-item-collapsed-show-title"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),
        children: [menuType === "group" && collapsed ? null : shouldHasIcon && iconDom ? (0, import_jsx_runtime27.jsx)("span", {
          className: "".concat(baseClassName, "-item-icon ").concat((_this$props4 = _this.props) === null || _this$props4 === void 0 ? void 0 : _this$props4.hashId).trim(),
          children: iconDom
        }) : defaultIcon, (0, import_jsx_runtime27.jsx)("span", {
          className: (0, import_classnames9.default)("".concat(baseClassName, "-item-text"), (_this$props5 = _this.props) === null || _this$props5 === void 0 ? void 0 : _this$props5.hashId, _defineProperty({}, "".concat(baseClassName, "-item-text-has-icon"), menuType !== "group" && shouldHasIcon && (iconDom || defaultIcon))),
          children: name
        })]
      });
      var title = subMenuItemRender ? subMenuItemRender(_objectSpread2(_objectSpread2({}, item), {}, {
        isUrl: false
      }), defaultTitle, _this.props) : defaultTitle;
      if (isGroup && level === 0 && _this.props.collapsed && !menu.collapsedShowGroupTitle) {
        return _this.getNavMenuItems(children, level + 1, level);
      }
      var childrenList = _this.getNavMenuItems(children, level + 1, isGroup && level === 0 && _this.props.collapsed ? level : level + 1);
      return [{
        type: menuType,
        key: item.key || item.path,
        label: title,
        onClick: isGroup ? void 0 : item.onTitleClick,
        children: childrenList,
        className: (0, import_classnames9.default)(_defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-group"), menuType === "group"), "".concat(baseClassName, "-submenu"), menuType !== "group"), "".concat(baseClassName, "-submenu-has-icon"), menuType !== "group" && shouldHasIcon && iconDom))
      }, isGroup && level === 0 ? {
        type: "divider",
        prefixCls,
        className: "".concat(baseClassName, "-divider"),
        key: (item.key || item.path) + "-group-divider",
        style: {
          padding: 0,
          borderBlockEnd: 0,
          margin: _this.props.collapsed ? "4px" : "6px 16px",
          marginBlockStart: _this.props.collapsed ? 4 : 8,
          borderColor: designToken === null || designToken === void 0 || (_designToken$layout = designToken.layout) === null || _designToken$layout === void 0 || (_designToken$layout = _designToken$layout.sider) === null || _designToken$layout === void 0 ? void 0 : _designToken$layout.colorMenuItemDivider
        }
      } : void 0].filter(Boolean);
    }
    return {
      className: "".concat(baseClassName, "-menu-item"),
      disabled: item.disabled,
      key: item.key || item.path,
      onClick: item.onTitleClick,
      // eslint-disable-next-line react/no-is-mounted
      label: _this.getMenuItemPath(item, level, noGroupLevel)
    };
  });
  _defineProperty(this, "getIntlName", function(item) {
    var name = item.name, locale = item.locale;
    var _this$props6 = _this.props, menu = _this$props6.menu, formatMessage = _this$props6.formatMessage;
    var finalName = name;
    if (locale && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {
      finalName = formatMessage === null || formatMessage === void 0 ? void 0 : formatMessage({
        id: locale,
        defaultMessage: name
      });
    }
    if (_this.props.menuTextRender) {
      return _this.props.menuTextRender(item, finalName, _this.props);
    }
    return finalName;
  });
  _defineProperty(this, "getMenuItemPath", function(item, level, noGroupLevel) {
    var _this$props9, _this$props10, _this$props11, _this$props12;
    var itemPath = _this.conversionPath(item.path || "/");
    var _this$props7 = _this.props, _this$props7$location = _this$props7.location, location2 = _this$props7$location === void 0 ? {
      pathname: "/"
    } : _this$props7$location, isMobile = _this$props7.isMobile, onCollapse = _this$props7.onCollapse, menuItemRender = _this$props7.menuItemRender, iconPrefixes = _this$props7.iconPrefixes;
    var menuItemTitle = _this.getIntlName(item);
    var _this$props8 = _this.props, baseClassName = _this$props8.baseClassName, menu = _this$props8.menu, collapsed = _this$props8.collapsed;
    var isGroup = (menu === null || menu === void 0 ? void 0 : menu.type) === "group";
    var hasIcon = level === 0 || isGroup && level === 1;
    var icon = !hasIcon ? null : getIcon(item.icon, iconPrefixes, "".concat(baseClassName, "-icon ").concat((_this$props9 = _this.props) === null || _this$props9 === void 0 ? void 0 : _this$props9.hashId));
    var defaultIcon = collapsed && hasIcon ? getMenuTitleSymbol(menuItemTitle) : null;
    var defaultItem = (0, import_jsx_runtime28.jsxs)("div", {
      className: (0, import_classnames9.default)("".concat(baseClassName, "-item-title"), (_this$props10 = _this.props) === null || _this$props10 === void 0 ? void 0 : _this$props10.hashId, _defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-item-title-collapsed"), collapsed), "".concat(baseClassName, "-item-title-collapsed-level-").concat(noGroupLevel), collapsed), "".concat(baseClassName, "-item-collapsed-show-title"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),
      children: [(0, import_jsx_runtime27.jsx)("span", {
        className: "".concat(baseClassName, "-item-icon ").concat((_this$props11 = _this.props) === null || _this$props11 === void 0 ? void 0 : _this$props11.hashId).trim(),
        style: {
          display: defaultIcon === null && !icon ? "none" : ""
        },
        children: icon || (0, import_jsx_runtime27.jsx)("span", {
          className: "anticon",
          children: defaultIcon
        })
      }), (0, import_jsx_runtime27.jsx)("span", {
        className: (0, import_classnames9.default)("".concat(baseClassName, "-item-text"), (_this$props12 = _this.props) === null || _this$props12 === void 0 ? void 0 : _this$props12.hashId, _defineProperty({}, "".concat(baseClassName, "-item-text-has-icon"), hasIcon && (icon || defaultIcon))),
        children: menuItemTitle
      })]
    }, itemPath);
    var isHttpUrl = isUrl(itemPath);
    if (isHttpUrl) {
      var _this$props13, _this$props14, _this$props15;
      defaultItem = (0, import_jsx_runtime28.jsxs)("span", {
        onClick: function onClick() {
          var _window, _window$open;
          (_window = window) === null || _window === void 0 || (_window$open = _window.open) === null || _window$open === void 0 || _window$open.call(_window, itemPath, "_blank");
        },
        className: (0, import_classnames9.default)("".concat(baseClassName, "-item-title"), (_this$props13 = _this.props) === null || _this$props13 === void 0 ? void 0 : _this$props13.hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-item-title-collapsed"), collapsed), "".concat(baseClassName, "-item-title-collapsed-level-").concat(noGroupLevel), collapsed), "".concat(baseClassName, "-item-link"), true), "".concat(baseClassName, "-item-collapsed-show-title"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),
        children: [(0, import_jsx_runtime27.jsx)("span", {
          className: "".concat(baseClassName, "-item-icon ").concat((_this$props14 = _this.props) === null || _this$props14 === void 0 ? void 0 : _this$props14.hashId).trim(),
          style: {
            display: defaultIcon === null && !icon ? "none" : ""
          },
          children: icon || (0, import_jsx_runtime27.jsx)("span", {
            className: "anticon",
            children: defaultIcon
          })
        }), (0, import_jsx_runtime27.jsx)("span", {
          className: (0, import_classnames9.default)("".concat(baseClassName, "-item-text"), (_this$props15 = _this.props) === null || _this$props15 === void 0 ? void 0 : _this$props15.hashId, _defineProperty({}, "".concat(baseClassName, "-item-text-has-icon"), hasIcon && (icon || defaultIcon))),
          children: menuItemTitle
        })]
      }, itemPath);
    }
    if (menuItemRender) {
      var renderItemProps = _objectSpread2(_objectSpread2({}, item), {}, {
        isUrl: isHttpUrl,
        itemPath,
        isMobile,
        replace: itemPath === location2.pathname,
        onClick: function onClick() {
          return onCollapse && onCollapse(true);
        },
        children: void 0
      });
      return level === 0 ? (0, import_jsx_runtime27.jsx)(MenuItemTooltip, {
        collapsed,
        title: menuItemTitle,
        disable: item.disabledTooltip,
        children: menuItemRender(renderItemProps, defaultItem, _this.props)
      }) : menuItemRender(renderItemProps, defaultItem, _this.props);
    }
    return level === 0 ? (0, import_jsx_runtime27.jsx)(MenuItemTooltip, {
      collapsed,
      title: menuItemTitle,
      disable: item.disabledTooltip,
      children: defaultItem
    }) : defaultItem;
  });
  _defineProperty(this, "conversionPath", function(path) {
    if (path && path.indexOf("http") === 0) {
      return path;
    }
    return "/".concat(path || "").replace(/\/+/g, "/");
  });
  this.props = props;
});
var getOpenKeysProps = function getOpenKeysProps2(openKeys, _ref) {
  var layout = _ref.layout, collapsed = _ref.collapsed;
  var openKeysProps = {};
  if (openKeys && !collapsed && ["side", "mix"].includes(layout || "mix")) {
    openKeysProps = {
      openKeys
    };
  }
  return openKeysProps;
};
var BaseMenu = function BaseMenu2(props) {
  var mode = props.mode, className = props.className, handleOpenChange = props.handleOpenChange, style = props.style, menuData = props.menuData, prefixCls = props.prefixCls, menu = props.menu, matchMenuKeys = props.matchMenuKeys, iconfontUrl = props.iconfontUrl, propsSelectedKeys = props.selectedKeys, onSelect = props.onSelect, menuRenderType = props.menuRenderType, propsOpenKeys = props.openKeys;
  var _useContext = (0, import_react12.useContext)(ProProvider), dark = _useContext.dark, designToken = _useContext.token;
  var baseClassName = "".concat(prefixCls, "-base-menu-").concat(mode);
  var defaultOpenKeysRef = (0, import_react12.useRef)([]);
  var _useMountMergeState = useMergedState(menu === null || menu === void 0 ? void 0 : menu.defaultOpenAll), _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2), defaultOpenAll = _useMountMergeState2[0], setDefaultOpenAll = _useMountMergeState2[1];
  var _useMountMergeState3 = useMergedState(function() {
    if (menu !== null && menu !== void 0 && menu.defaultOpenAll) {
      return getOpenKeysFromMenuData(menuData) || [];
    }
    if (propsOpenKeys === false) {
      return false;
    }
    return [];
  }, {
    value: propsOpenKeys === false ? void 0 : propsOpenKeys,
    onChange: handleOpenChange
  }), _useMountMergeState4 = _slicedToArray(_useMountMergeState3, 2), openKeys = _useMountMergeState4[0], setOpenKeys = _useMountMergeState4[1];
  var _useMountMergeState5 = useMergedState([], {
    value: propsSelectedKeys,
    onChange: onSelect ? function(keys) {
      if (onSelect && keys) {
        onSelect(keys);
      }
    } : void 0
  }), _useMountMergeState6 = _slicedToArray(_useMountMergeState5, 2), selectedKeys = _useMountMergeState6[0], setSelectedKeys = _useMountMergeState6[1];
  (0, import_react12.useEffect)(function() {
    if (menu !== null && menu !== void 0 && menu.defaultOpenAll || propsOpenKeys === false) {
      return;
    }
    if (matchMenuKeys) {
      setOpenKeys(matchMenuKeys);
      setSelectedKeys(matchMenuKeys);
    }
  }, [matchMenuKeys.join("-")]);
  (0, import_react12.useEffect)(function() {
    if (iconfontUrl) {
      IconFont = create({
        scriptUrl: iconfontUrl
      });
    }
  }, [iconfontUrl]);
  (0, import_react12.useEffect)(
    function() {
      if (matchMenuKeys.join("-") !== (selectedKeys || []).join("-")) {
        setSelectedKeys(matchMenuKeys);
      }
      if (!defaultOpenAll && propsOpenKeys !== false && matchMenuKeys.join("-") !== (openKeys || []).join("-")) {
        var newKeys = matchMenuKeys;
        if ((menu === null || menu === void 0 ? void 0 : menu.autoClose) === false) {
          newKeys = Array.from(new Set([].concat(_toConsumableArray(matchMenuKeys), _toConsumableArray(openKeys || []))));
        }
        setOpenKeys(newKeys);
      } else if (menu !== null && menu !== void 0 && menu.ignoreFlatMenu && defaultOpenAll) {
        setOpenKeys(getOpenKeysFromMenuData(menuData));
      } else {
        setDefaultOpenAll(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [matchMenuKeys.join("-")]
  );
  var openKeysProps = (0, import_react12.useMemo)(
    function() {
      return getOpenKeysProps(openKeys, props);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [openKeys && openKeys.join(","), props.layout, props.collapsed]
  );
  var _useStyle = useStyle9(baseClassName, mode), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var menuUtils = (0, import_react12.useMemo)(function() {
    return new MenuUtil(_objectSpread2(_objectSpread2({}, props), {}, {
      token: designToken,
      menuRenderType,
      baseClassName,
      hashId
    }));
  }, [props, designToken, menuRenderType, baseClassName, hashId]);
  if (menu !== null && menu !== void 0 && menu.loading) {
    return (0, import_jsx_runtime27.jsx)("div", {
      style: mode !== null && mode !== void 0 && mode.includes("inline") ? {
        padding: 24
      } : {
        marginBlockStart: 16
      },
      children: (0, import_jsx_runtime27.jsx)(skeleton_default, {
        active: true,
        title: false,
        paragraph: {
          rows: mode !== null && mode !== void 0 && mode.includes("inline") ? 6 : 1
        }
      })
    });
  }
  if (props.openKeys === false && !props.handleOpenChange) {
    defaultOpenKeysRef.current = matchMenuKeys;
  }
  var finallyData = props.postMenuData ? props.postMenuData(menuData) : menuData;
  if (finallyData && (finallyData === null || finallyData === void 0 ? void 0 : finallyData.length) < 1) {
    return null;
  }
  return wrapSSR((0, import_react13.createElement)(menu_default, _objectSpread2(_objectSpread2({}, openKeysProps), {}, {
    _internalDisableMenuItemTitleTooltip: true,
    key: "Menu",
    mode,
    inlineIndent: 16,
    defaultOpenKeys: defaultOpenKeysRef.current,
    theme: dark ? "dark" : "light",
    selectedKeys,
    style: _objectSpread2({
      backgroundColor: "transparent",
      border: "none"
    }, style),
    className: (0, import_classnames9.default)(className, hashId, baseClassName, _defineProperty(_defineProperty({}, "".concat(baseClassName, "-horizontal"), mode === "horizontal"), "".concat(baseClassName, "-collapsed"), props.collapsed)),
    items: menuUtils.getNavMenuItems(finallyData, 0, 0),
    onOpenChange: function onOpenChange(_openKeys) {
      if (!props.collapsed) {
        setOpenKeys(_openKeys);
      }
    }
  }, props.menuProps)));
};

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/stylish.js
init_defineProperty();
function useStylish3(prefixCls, _ref) {
  var stylish = _ref.stylish, proLayoutCollapsedWidth = _ref.proLayoutCollapsedWidth;
  return useStyle("ProLayoutSiderMenuStylish", function(token) {
    var siderMenuToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls),
      proLayoutCollapsedWidth
    });
    if (!stylish)
      return [];
    return [_defineProperty({}, "div".concat(token.proComponentsCls, "-layout"), _defineProperty({}, "".concat(siderMenuToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(siderMenuToken)))];
  });
}

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js
var import_jsx_runtime29 = __toESM(require_jsx_runtime());
var import_jsx_runtime30 = __toESM(require_jsx_runtime());
var import_jsx_runtime31 = __toESM(require_jsx_runtime());
var import_react15 = __toESM(require_react());
var _excluded5 = ["title", "render"];
var _SafetyWarningProvider = import_react14.default.memo(function(props) {
  if (true) {
    console.warn("[pro-layout] SiderMenu required antd@^4.24.15 || antd@^5.11.2 for access the menu context, please upgrade your antd version (current ".concat(version_default, ")."));
  }
  return (0, import_jsx_runtime30.jsx)(import_jsx_runtime29.Fragment, {
    children: props.children
  });
});
var Sider = layout_default.Sider;
var _Layout$_InternalSide = layout_default._InternalSiderContext;
var SiderContext = _Layout$_InternalSide === void 0 ? {
  Provider: _SafetyWarningProvider
} : _Layout$_InternalSide;
var renderLogoAndTitle = function renderLogoAndTitle2(props) {
  var renderKey = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "menuHeaderRender";
  var logo = props.logo, title = props.title, layout = props.layout;
  var renderFunction = props[renderKey];
  if (renderFunction === false) {
    return null;
  }
  var logoDom = defaultRenderLogo(logo);
  var titleDom = (0, import_jsx_runtime30.jsx)("h1", {
    children: title !== null && title !== void 0 ? title : "Ant Design Pro"
  });
  if (renderFunction) {
    return renderFunction(logoDom, props.collapsed ? null : titleDom, props);
  }
  if (props.isMobile) {
    return null;
  }
  if (layout === "mix" && renderKey === "menuHeaderRender")
    return false;
  if (props.collapsed) {
    return (0, import_jsx_runtime30.jsx)("a", {
      children: logoDom
    }, "title");
  }
  return (0, import_jsx_runtime31.jsxs)("a", {
    children: [logoDom, titleDom]
  }, "title");
};
var SiderMenu = function SiderMenu2(props) {
  var _props$menu2;
  var collapsed = props.collapsed, originCollapsed = props.originCollapsed, fixSiderbar = props.fixSiderbar, menuFooterRender = props.menuFooterRender, _onCollapse = props.onCollapse, theme = props.theme, siderWidth = props.siderWidth, isMobile = props.isMobile, onMenuHeaderClick = props.onMenuHeaderClick, _props$breakpoint = props.breakpoint, breakpoint = _props$breakpoint === void 0 ? "lg" : _props$breakpoint, style = props.style, layout = props.layout, _props$menuExtraRende = props.menuExtraRender, menuExtraRender = _props$menuExtraRende === void 0 ? false : _props$menuExtraRende, links = props.links, menuContentRender = props.menuContentRender, collapsedButtonRender = props.collapsedButtonRender, prefixCls = props.prefixCls, avatarProps = props.avatarProps, rightContentRender = props.rightContentRender, actionsRender = props.actionsRender, onOpenChange = props.onOpenChange, stylish = props.stylish, logoStyle = props.logoStyle;
  var _useContext = (0, import_react14.useContext)(ProProvider), hashId = _useContext.hashId;
  var showSiderExtraDom = (0, import_react14.useMemo)(function() {
    if (isMobile)
      return false;
    if (layout === "mix")
      return false;
    return true;
  }, [isMobile, layout]);
  var baseClassName = "".concat(prefixCls, "-sider");
  var collapsedWidth = 64;
  var stylishClassName = useStylish3("".concat(baseClassName, ".").concat(baseClassName, "-stylish"), {
    stylish,
    proLayoutCollapsedWidth: collapsedWidth
  });
  var siderClassName = (0, import_classnames10.default)("".concat(baseClassName), hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-fixed"), fixSiderbar), "".concat(baseClassName, "-fixed-mix"), layout === "mix" && !isMobile && fixSiderbar), "".concat(baseClassName, "-collapsed"), props.collapsed), "".concat(baseClassName, "-layout-").concat(layout), layout && !isMobile), "".concat(baseClassName, "-light"), theme !== "dark"), "".concat(baseClassName, "-mix"), layout === "mix" && !isMobile), "".concat(baseClassName, "-stylish"), !!stylish));
  var headerDom = renderLogoAndTitle(props);
  var extraDom = menuExtraRender && menuExtraRender(props);
  var menuDom = (0, import_react14.useMemo)(function() {
    return menuContentRender !== false && (0, import_react15.createElement)(BaseMenu, _objectSpread2(_objectSpread2({}, props), {}, {
      key: "base-menu",
      mode: collapsed && !isMobile ? "vertical" : "inline",
      handleOpenChange: onOpenChange,
      style: {
        width: "100%"
      },
      className: "".concat(baseClassName, "-menu ").concat(hashId).trim()
    }));
  }, [baseClassName, hashId, menuContentRender, onOpenChange, props]);
  var linksMenuItems = (links || []).map(function(node, index) {
    return {
      className: "".concat(baseClassName, "-link"),
      label: node,
      key: index
    };
  });
  var menuRenderDom = (0, import_react14.useMemo)(function() {
    return menuContentRender ? menuContentRender(props, menuDom) : menuDom;
  }, [menuContentRender, menuDom, props]);
  var avatarDom = (0, import_react14.useMemo)(function() {
    if (!avatarProps)
      return null;
    var title = avatarProps.title, render = avatarProps.render, rest = _objectWithoutProperties(avatarProps, _excluded5);
    var dom = (0, import_jsx_runtime31.jsxs)("div", {
      className: "".concat(baseClassName, "-actions-avatar"),
      children: [rest !== null && rest !== void 0 && rest.src || rest !== null && rest !== void 0 && rest.srcSet || rest.icon || rest.children ? (0, import_jsx_runtime30.jsx)(avatar_default, _objectSpread2({
        size: 28
      }, rest)) : null, avatarProps.title && !collapsed && (0, import_jsx_runtime30.jsx)("span", {
        children: title
      })]
    });
    if (render) {
      return render(avatarProps, dom, props);
    }
    return dom;
  }, [avatarProps, baseClassName, collapsed]);
  var actionsDom = (0, import_react14.useMemo)(
    function() {
      if (!actionsRender)
        return null;
      return (0, import_jsx_runtime30.jsx)(space_default, {
        align: "center",
        size: 4,
        direction: collapsed ? "vertical" : "horizontal",
        className: (0, import_classnames10.default)(["".concat(baseClassName, "-actions-list"), collapsed && "".concat(baseClassName, "-actions-list-collapsed"), hashId]),
        children: [actionsRender === null || actionsRender === void 0 ? void 0 : actionsRender(props)].flat(1).map(function(item, index) {
          return (0, import_jsx_runtime30.jsx)("div", {
            className: "".concat(baseClassName, "-actions-list-item ").concat(hashId).trim(),
            children: item
          }, index);
        })
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [actionsRender, baseClassName, collapsed]
  );
  var appsDom = (0, import_react14.useMemo)(function() {
    return (0, import_jsx_runtime30.jsx)(AppsLogoComponents, {
      onItemClick: props.itemClick,
      appListRender: props.appListRender,
      appList: props.appList,
      prefixCls: props.prefixCls
    });
  }, [props.appList, props.appListRender, props.prefixCls]);
  var collapsedDom = (0, import_react14.useMemo)(function() {
    if (collapsedButtonRender === false)
      return null;
    var dom = (0, import_jsx_runtime30.jsx)(CollapsedIcon, {
      isMobile,
      collapsed: originCollapsed,
      className: "".concat(baseClassName, "-collapsed-button"),
      onClick: function onClick() {
        _onCollapse === null || _onCollapse === void 0 || _onCollapse(!originCollapsed);
      }
    });
    if (collapsedButtonRender)
      return collapsedButtonRender(collapsed, dom);
    return dom;
  }, [collapsedButtonRender, isMobile, originCollapsed, baseClassName, collapsed, _onCollapse]);
  var actionAreaDom = (0, import_react14.useMemo)(function() {
    if (!avatarDom && !actionsDom)
      return null;
    return (0, import_jsx_runtime31.jsxs)("div", {
      className: (0, import_classnames10.default)("".concat(baseClassName, "-actions"), hashId, collapsed && "".concat(baseClassName, "-actions-collapsed")),
      children: [avatarDom, actionsDom]
    });
  }, [actionsDom, avatarDom, baseClassName, collapsed, hashId]);
  var hideMenuWhenCollapsedClassName = (0, import_react14.useMemo)(function() {
    var _props$menu;
    if (props !== null && props !== void 0 && (_props$menu = props.menu) !== null && _props$menu !== void 0 && _props$menu.hideMenuWhenCollapsed && collapsed) {
      return "".concat(baseClassName, "-hide-menu-collapsed");
    }
    return null;
  }, [baseClassName, collapsed, props === null || props === void 0 || (_props$menu2 = props.menu) === null || _props$menu2 === void 0 ? void 0 : _props$menu2.hideMenuWhenCollapsed]);
  var menuFooterDom = menuFooterRender && (menuFooterRender === null || menuFooterRender === void 0 ? void 0 : menuFooterRender(props));
  var menuDomItems = (0, import_jsx_runtime31.jsxs)(import_jsx_runtime29.Fragment, {
    children: [headerDom && (0, import_jsx_runtime31.jsxs)("div", {
      className: (0, import_classnames10.default)([(0, import_classnames10.default)("".concat(baseClassName, "-logo"), hashId, _defineProperty({}, "".concat(baseClassName, "-logo-collapsed"), collapsed))]),
      onClick: showSiderExtraDom ? onMenuHeaderClick : void 0,
      id: "logo",
      style: logoStyle,
      children: [headerDom, appsDom]
    }), extraDom && (0, import_jsx_runtime30.jsx)("div", {
      className: (0, import_classnames10.default)(["".concat(baseClassName, "-extra"), !headerDom && "".concat(baseClassName, "-extra-no-logo"), hashId]),
      children: extraDom
    }), (0, import_jsx_runtime30.jsx)("div", {
      style: {
        flex: 1,
        overflowY: "auto",
        overflowX: "hidden"
      },
      children: menuRenderDom
    }), (0, import_jsx_runtime31.jsxs)(SiderContext.Provider, {
      value: {},
      children: [links ? (0, import_jsx_runtime30.jsx)("div", {
        className: "".concat(baseClassName, "-links ").concat(hashId).trim(),
        children: (0, import_jsx_runtime30.jsx)(menu_default, {
          inlineIndent: 16,
          className: "".concat(baseClassName, "-link-menu ").concat(hashId).trim(),
          selectedKeys: [],
          openKeys: [],
          theme,
          mode: "inline",
          items: linksMenuItems
        })
      }) : null, showSiderExtraDom && (0, import_jsx_runtime31.jsxs)(import_jsx_runtime29.Fragment, {
        children: [actionAreaDom, !actionsDom && rightContentRender ? (0, import_jsx_runtime30.jsx)("div", {
          className: (0, import_classnames10.default)("".concat(baseClassName, "-actions"), hashId, _defineProperty({}, "".concat(baseClassName, "-actions-collapsed"), collapsed)),
          children: rightContentRender === null || rightContentRender === void 0 ? void 0 : rightContentRender(props)
        }) : null]
      }), menuFooterDom && (0, import_jsx_runtime30.jsx)("div", {
        className: (0, import_classnames10.default)(["".concat(baseClassName, "-footer"), hashId, _defineProperty({}, "".concat(baseClassName, "-footer-collapsed"), collapsed)]),
        children: menuFooterDom
      })]
    })]
  });
  return stylishClassName.wrapSSR((0, import_jsx_runtime31.jsxs)(import_jsx_runtime29.Fragment, {
    children: [fixSiderbar && !isMobile && !hideMenuWhenCollapsedClassName && (0, import_jsx_runtime30.jsx)("div", {
      style: _objectSpread2({
        width: collapsed ? collapsedWidth : siderWidth,
        overflow: "hidden",
        flex: "0 0 ".concat(collapsed ? collapsedWidth : siderWidth, "px"),
        maxWidth: collapsed ? collapsedWidth : siderWidth,
        minWidth: collapsed ? collapsedWidth : siderWidth,
        transition: "all 0.2s ease 0s"
      }, style)
    }), (0, import_jsx_runtime31.jsxs)(Sider, {
      collapsible: true,
      trigger: null,
      collapsed,
      breakpoint: breakpoint === false ? void 0 : breakpoint,
      onCollapse: function onCollapse(collapse) {
        if (isMobile)
          return;
        _onCollapse === null || _onCollapse === void 0 || _onCollapse(collapse);
      },
      collapsedWidth,
      style,
      theme,
      width: siderWidth,
      className: (0, import_classnames10.default)(siderClassName, hashId, hideMenuWhenCollapsedClassName),
      children: [hideMenuWhenCollapsedClassName ? (0, import_jsx_runtime30.jsx)("div", {
        className: "".concat(baseClassName, "-hide-when-collapsed ").concat(hashId).trim(),
        style: {
          height: "100%",
          width: "100%",
          opacity: hideMenuWhenCollapsedClassName ? 0 : 1
        },
        children: menuDomItems
      }) : menuDomItems, collapsedDom]
    })]
  }));
};

// node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js
init_defineProperty();
var import_classnames12 = __toESM(require_classnames());
var import_react18 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/GlobalHeader/ActionsContent.js
init_defineProperty();
var import_classnames11 = __toESM(require_classnames());
var import_react16 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/GlobalHeader/rightContentStyle.js
init_defineProperty();
var genTopNavHeaderStyle = function genTopNavHeaderStyle2(token) {
  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5;
  return _defineProperty({}, token.componentCls, {
    "&-header-actions": {
      display: "flex",
      height: "100%",
      alignItems: "center",
      "&-item": {
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        paddingBlock: 0,
        paddingInline: 2,
        color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextRightActionsItem,
        fontSize: "16px",
        cursor: "pointer",
        borderRadius: token.borderRadius,
        "> *": {
          paddingInline: 6,
          paddingBlock: 6,
          borderRadius: token.borderRadius,
          "&:hover": {
            backgroundColor: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgRightActionsItemHover
          }
        }
      },
      "&-avatar": {
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        paddingInlineStart: token.padding,
        paddingInlineEnd: token.padding,
        cursor: "pointer",
        color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextRightActionsItem,
        "> div": {
          height: "44px",
          color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextRightActionsItem,
          paddingInline: 8,
          paddingBlock: 8,
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          lineHeight: "44px",
          borderRadius: token.borderRadius,
          "&:hover": {
            backgroundColor: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.header) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgRightActionsItemHover
          }
        }
      }
    }
  });
};
function useStyle10(prefixCls) {
  return useStyle("ProLayoutRightContent", function(token) {
    var proToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genTopNavHeaderStyle(proToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/GlobalHeader/ActionsContent.js
var import_react17 = __toESM(require_react());
var import_jsx_runtime32 = __toESM(require_jsx_runtime());
var import_jsx_runtime33 = __toESM(require_jsx_runtime());
var _excluded6 = ["rightContentRender", "avatarProps", "actionsRender", "headerContentRender"];
var _excluded23 = ["title", "render"];
var ActionsContent = function ActionsContent2(_ref) {
  var rightContentRender = _ref.rightContentRender, avatarProps = _ref.avatarProps, actionsRender = _ref.actionsRender, headerContentRender = _ref.headerContentRender, props = _objectWithoutProperties(_ref, _excluded6);
  var _useContext = (0, import_react16.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = "".concat(getPrefixCls(), "-pro-global-header");
  var _useStyle = useStyle10(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var _useState = (0, import_react16.useState)("auto"), _useState2 = _slicedToArray(_useState, 2), rightSize = _useState2[0], setRightSize = _useState2[1];
  var avatarDom = (0, import_react16.useMemo)(function() {
    if (!avatarProps)
      return null;
    var title = avatarProps.title, render = avatarProps.render, rest = _objectWithoutProperties(avatarProps, _excluded23);
    var domList = [rest !== null && rest !== void 0 && rest.src || rest !== null && rest !== void 0 && rest.srcSet || rest.icon || rest.children ? (0, import_react17.createElement)(avatar_default, _objectSpread2(_objectSpread2({}, rest), {}, {
      size: 28,
      key: "avatar"
    })) : null, title ? (0, import_jsx_runtime32.jsx)("span", {
      style: {
        marginInlineStart: 8
      },
      children: title
    }, "name") : void 0];
    if (render) {
      return render(avatarProps, (0, import_jsx_runtime32.jsx)("div", {
        children: domList
      }), props);
    }
    return (0, import_jsx_runtime32.jsx)("div", {
      children: domList
    });
  }, [avatarProps]);
  var rightActionsRender = actionsRender || avatarDom ? function(restParams) {
    var doms = actionsRender && (actionsRender === null || actionsRender === void 0 ? void 0 : actionsRender(restParams));
    if (!doms && !avatarDom)
      return null;
    if (!Array.isArray(doms))
      return wrapSSR((0, import_jsx_runtime33.jsxs)("div", {
        className: "".concat(prefixCls, "-header-actions ").concat(hashId).trim(),
        children: [doms, avatarDom && (0, import_jsx_runtime32.jsx)("span", {
          className: "".concat(prefixCls, "-header-actions-avatar ").concat(hashId).trim(),
          children: avatarDom
        })]
      }));
    return wrapSSR((0, import_jsx_runtime33.jsxs)("div", {
      className: "".concat(prefixCls, "-header-actions ").concat(hashId).trim(),
      children: [doms.filter(Boolean).map(function(dom, index) {
        var hideHover = false;
        if (import_react16.default.isValidElement(dom)) {
          var _dom$props;
          hideHover = !!(dom !== null && dom !== void 0 && (_dom$props = dom.props) !== null && _dom$props !== void 0 && _dom$props["aria-hidden"]);
        }
        return (0, import_jsx_runtime32.jsx)("div", {
          className: (0, import_classnames11.default)("".concat(prefixCls, "-header-actions-item ").concat(hashId), _defineProperty({}, "".concat(prefixCls, "-header-actions-hover"), !hideHover)),
          children: dom
        }, index);
      }), avatarDom && (0, import_jsx_runtime32.jsx)("span", {
        className: "".concat(prefixCls, "-header-actions-avatar ").concat(hashId).trim(),
        children: avatarDom
      })]
    }));
  } : void 0;
  var setRightSizeDebounceFn = useDebounceFn(function() {
    var _ref2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(width) {
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              setRightSize(width);
            case 1:
            case "end":
              return _context.stop();
          }
      }, _callee);
    }));
    return function(_x) {
      return _ref2.apply(this, arguments);
    };
  }(), 160);
  var contentRender = rightActionsRender || rightContentRender;
  return (0, import_jsx_runtime32.jsx)("div", {
    className: "".concat(prefixCls, "-right-content ").concat(hashId).trim(),
    style: {
      minWidth: rightSize,
      height: "100%"
    },
    children: (0, import_jsx_runtime32.jsx)("div", {
      style: {
        height: "100%"
      },
      children: (0, import_jsx_runtime32.jsx)(es_default, {
        onResize: function onResize(_ref3) {
          var width = _ref3.width;
          setRightSizeDebounceFn.run(width);
        },
        children: contentRender ? (0, import_jsx_runtime32.jsx)("div", {
          style: {
            display: "flex",
            alignItems: "center",
            height: "100%",
            justifyContent: "flex-end"
          },
          children: contentRender(_objectSpread2(_objectSpread2({}, props), {}, {
            // 测试专用
            //@ts-ignore
            rightContentSize: rightSize
          }))
        }) : null
      })
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/TopNavHeader/style.js
init_defineProperty();
var genTopNavHeaderStyle3 = function genTopNavHeaderStyle4(token) {
  var _token$layout, _token$layout2;
  return _defineProperty({}, token.componentCls, {
    position: "relative",
    width: "100%",
    height: "100%",
    backgroundColor: "transparent",
    ".anticon": {
      color: "inherit"
    },
    "&-main": {
      display: "flex",
      height: "100%",
      paddingInlineStart: "16px",
      "&-left": _defineProperty({
        display: "flex",
        alignItems: "center"
      }, "".concat(token.proComponentsCls, "-layout-apps-icon"), {
        marginInlineEnd: 16,
        marginInlineStart: -8
      })
    },
    "&-wide": {
      maxWidth: 1152,
      margin: "0 auto"
    },
    "&-logo": {
      position: "relative",
      display: "flex",
      height: "100%",
      alignItems: "center",
      overflow: "hidden",
      "> *:first-child": {
        display: "flex",
        alignItems: "center",
        minHeight: "22px",
        fontSize: "22px"
      },
      "> *:first-child > img": {
        display: "inline-block",
        height: "32px",
        verticalAlign: "middle"
      },
      "> *:first-child > h1": {
        display: "inline-block",
        marginBlock: 0,
        marginInline: 0,
        lineHeight: "24px",
        marginInlineStart: 6,
        fontWeight: "600",
        fontSize: "16px",
        color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorHeaderTitle,
        verticalAlign: "top"
      }
    },
    "&-menu": {
      minWidth: 0,
      display: "flex",
      alignItems: "center",
      paddingInline: 6,
      paddingBlock: 6,
      lineHeight: "".concat(Math.max((((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader) || 56) - 12, 40), "px")
    }
  });
};
function useStyle11(prefixCls) {
  return useStyle("ProLayoutTopNavHeader", function(token) {
    var topNavHeaderToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genTopNavHeaderStyle3(topNavHeaderToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js
var import_jsx_runtime34 = __toESM(require_jsx_runtime());
var import_jsx_runtime35 = __toESM(require_jsx_runtime());
var TopNavHeader = function TopNavHeader2(props) {
  var _token$layout13, _token$layout14, _token$layout15, _token$layout16, _token$layout17, _token$layout18, _token$layout19;
  var ref = (0, import_react18.useRef)(null);
  var onMenuHeaderClick = props.onMenuHeaderClick, contentWidth = props.contentWidth, rightContentRender = props.rightContentRender, propsClassName = props.className, style = props.style, headerContentRender = props.headerContentRender, layout = props.layout, actionsRender = props.actionsRender;
  var _useContext = (0, import_react18.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var _useContext2 = (0, import_react18.useContext)(ProProvider), dark = _useContext2.dark;
  var prefixCls = "".concat(props.prefixCls || getPrefixCls("pro"), "-top-nav-header");
  var _useStyle = useStyle11(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var renderKey = void 0;
  if (props.menuHeaderRender !== void 0) {
    renderKey = "menuHeaderRender";
  } else if (layout === "mix" || layout === "top") {
    renderKey = "headerTitleRender";
  }
  var headerDom = renderLogoAndTitle(_objectSpread2(_objectSpread2({}, props), {}, {
    collapsed: false
  }), renderKey);
  var _useContext3 = (0, import_react18.useContext)(ProProvider), token = _useContext3.token;
  var contentDom = (0, import_react18.useMemo)(function() {
    var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12, _props$menuProps;
    var defaultDom = (0, import_jsx_runtime34.jsx)(
      config_provider_default,
      {
        theme: {
          hashed: isNeedOpenHash(),
          components: {
            Layout: {
              headerBg: "transparent",
              bodyBg: "transparent"
            },
            Menu: _objectSpread2({}, coverToNewToken({
              colorItemBg: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorBgHeader) || "transparent",
              colorSubItemBg: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgHeader) || "transparent",
              radiusItem: token.borderRadius,
              colorItemBgSelected: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),
              itemHoverBg: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgMenuItemHover) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),
              colorItemBgSelectedHorizontal: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.header) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),
              colorActiveBarWidth: 0,
              colorActiveBarHeight: 0,
              colorActiveBarBorderSize: 0,
              colorItemText: ((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.header) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorTextMenu) || (token === null || token === void 0 ? void 0 : token.colorTextSecondary),
              colorItemTextHoverHorizontal: ((_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.header) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuActive) || (token === null || token === void 0 ? void 0 : token.colorText),
              colorItemTextSelectedHorizontal: ((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.header) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorTextMenuSelected) || (token === null || token === void 0 ? void 0 : token.colorTextBase),
              horizontalItemBorderRadius: 4,
              colorItemTextHover: ((_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.header) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuActive) || "rgba(0, 0, 0, 0.85)",
              horizontalItemHoverBg: ((_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.header) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorBgMenuItemHover) || "rgba(0, 0, 0, 0.04)",
              colorItemTextSelected: ((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.header) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected) || "rgba(0, 0, 0, 1)",
              popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,
              subMenuItemBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,
              darkSubMenuItemBg: "transparent",
              darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated
            }))
          },
          token: {
            colorBgElevated: ((_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.header) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.colorBgHeader) || "transparent"
          }
        },
        children: (0, import_jsx_runtime34.jsx)(BaseMenu, _objectSpread2(_objectSpread2(_objectSpread2({
          theme: dark ? "dark" : "light"
        }, props), {}, {
          className: "".concat(prefixCls, "-base-menu ").concat(hashId).trim()
        }, props.menuProps), {}, {
          style: _objectSpread2({
            width: "100%"
          }, (_props$menuProps = props.menuProps) === null || _props$menuProps === void 0 ? void 0 : _props$menuProps.style),
          collapsed: false,
          menuRenderType: "header",
          mode: "horizontal"
        }))
      }
    );
    if (headerContentRender) {
      return headerContentRender(props, defaultDom);
    }
    return defaultDom;
  }, [(_token$layout13 = token.layout) === null || _token$layout13 === void 0 || (_token$layout13 = _token$layout13.header) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.colorBgHeader, (_token$layout14 = token.layout) === null || _token$layout14 === void 0 || (_token$layout14 = _token$layout14.header) === null || _token$layout14 === void 0 ? void 0 : _token$layout14.colorBgMenuItemSelected, (_token$layout15 = token.layout) === null || _token$layout15 === void 0 || (_token$layout15 = _token$layout15.header) === null || _token$layout15 === void 0 ? void 0 : _token$layout15.colorBgMenuItemHover, (_token$layout16 = token.layout) === null || _token$layout16 === void 0 || (_token$layout16 = _token$layout16.header) === null || _token$layout16 === void 0 ? void 0 : _token$layout16.colorTextMenu, (_token$layout17 = token.layout) === null || _token$layout17 === void 0 || (_token$layout17 = _token$layout17.header) === null || _token$layout17 === void 0 ? void 0 : _token$layout17.colorTextMenuActive, (_token$layout18 = token.layout) === null || _token$layout18 === void 0 || (_token$layout18 = _token$layout18.header) === null || _token$layout18 === void 0 ? void 0 : _token$layout18.colorTextMenuSelected, (_token$layout19 = token.layout) === null || _token$layout19 === void 0 || (_token$layout19 = _token$layout19.header) === null || _token$layout19 === void 0 ? void 0 : _token$layout19.colorBgMenuElevated, token.borderRadius, token === null || token === void 0 ? void 0 : token.colorBgTextHover, token === null || token === void 0 ? void 0 : token.colorTextSecondary, token === null || token === void 0 ? void 0 : token.colorText, token === null || token === void 0 ? void 0 : token.colorTextBase, token.colorBgElevated, dark, props, prefixCls, hashId, headerContentRender]);
  return wrapSSR((0, import_jsx_runtime34.jsx)("div", {
    className: (0, import_classnames12.default)(prefixCls, hashId, propsClassName, _defineProperty({}, "".concat(prefixCls, "-light"), true)),
    style,
    children: (0, import_jsx_runtime35.jsxs)("div", {
      ref,
      className: (0, import_classnames12.default)("".concat(prefixCls, "-main"), hashId, _defineProperty({}, "".concat(prefixCls, "-wide"), contentWidth === "Fixed" && layout === "top")),
      children: [headerDom && (0, import_jsx_runtime35.jsxs)("div", {
        className: (0, import_classnames12.default)("".concat(prefixCls, "-main-left ").concat(hashId)),
        onClick: onMenuHeaderClick,
        children: [(0, import_jsx_runtime34.jsx)(AppsLogoComponents, _objectSpread2({}, props)), (0, import_jsx_runtime34.jsx)("div", {
          className: "".concat(prefixCls, "-logo ").concat(hashId).trim(),
          id: "logo",
          children: headerDom
        }, "logo")]
      }), (0, import_jsx_runtime34.jsx)("div", {
        style: {
          flex: 1
        },
        className: "".concat(prefixCls, "-menu ").concat(hashId).trim(),
        children: contentDom
      }), (rightContentRender || actionsRender || props.avatarProps) && (0, import_jsx_runtime34.jsx)(ActionsContent, _objectSpread2(_objectSpread2({
        rightContentRender
      }, props), {}, {
        prefixCls
      }))]
    })
  }));
};

// node_modules/@ant-design/pro-layout/es/components/GlobalHeader/style.js
init_defineProperty();
var genGlobalHeaderStyle = function genGlobalHeaderStyle2(token) {
  var _token$layout, _token$layout2, _token$layout3;
  return _defineProperty({}, token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty({
    position: "relative",
    background: "transparent",
    display: "flex",
    alignItems: "center",
    marginBlock: 0,
    marginInline: 16,
    height: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56,
    boxSizing: "border-box",
    "> a": {
      height: "100%"
    }
  }, "".concat(token.proComponentsCls, "-layout-apps-icon"), {
    marginInlineEnd: 16
  }), "&-collapsed-button", {
    minHeight: "22px",
    color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorHeaderTitle,
    fontSize: "18px",
    marginInlineEnd: "16px"
  }), "&-logo", {
    position: "relative",
    marginInlineEnd: "16px",
    a: {
      display: "flex",
      alignItems: "center",
      height: "100%",
      minHeight: "22px",
      fontSize: "20px"
    },
    img: {
      height: "28px"
    },
    h1: {
      height: "32px",
      marginBlock: 0,
      marginInline: 0,
      marginInlineStart: 8,
      fontWeight: "600",
      color: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorHeaderTitle) || token.colorTextHeading,
      fontSize: "18px",
      lineHeight: "32px"
    },
    "&-mix": {
      display: "flex",
      alignItems: "center"
    }
  }), "&-logo-mobile", {
    minWidth: "24px",
    marginInlineEnd: 0
  }));
};
function useStyle12(prefixCls) {
  return useStyle("ProLayoutGlobalHeader", function(token) {
    var GlobalHeaderToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genGlobalHeaderStyle(GlobalHeaderToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js
var import_jsx_runtime36 = __toESM(require_jsx_runtime());
var import_jsx_runtime37 = __toESM(require_jsx_runtime());
var import_jsx_runtime38 = __toESM(require_jsx_runtime());
var renderLogo3 = function renderLogo4(menuHeaderRender, logoDom) {
  if (menuHeaderRender === false) {
    return null;
  }
  if (menuHeaderRender) {
    return menuHeaderRender(logoDom, null);
  }
  return logoDom;
};
var GlobalHeader = function GlobalHeader2(props) {
  var isMobile = props.isMobile, logo = props.logo, collapsed = props.collapsed, onCollapse = props.onCollapse, rightContentRender = props.rightContentRender, menuHeaderRender = props.menuHeaderRender, onMenuHeaderClick = props.onMenuHeaderClick, propClassName = props.className, style = props.style, layout = props.layout, children = props.children, splitMenus = props.splitMenus, menuData = props.menuData, prefixCls = props.prefixCls;
  var _useContext = (0, import_react19.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls, direction = _useContext.direction;
  var baseClassName = "".concat(prefixCls || getPrefixCls("pro"), "-global-header");
  var _useStyle = useStyle12(baseClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var className = (0, import_classnames13.default)(propClassName, baseClassName, hashId);
  if (layout === "mix" && !isMobile && splitMenus) {
    var noChildrenMenuData = (menuData || []).map(function(item) {
      return _objectSpread2(_objectSpread2({}, item), {}, {
        children: void 0,
        routes: void 0
      });
    });
    var clearMenuData = clearMenuItem(noChildrenMenuData);
    return (0, import_jsx_runtime36.jsx)(TopNavHeader, _objectSpread2(_objectSpread2({
      mode: "horizontal"
    }, props), {}, {
      splitMenus: false,
      menuData: clearMenuData
    }));
  }
  var logoClassNames = (0, import_classnames13.default)("".concat(baseClassName, "-logo"), hashId, _defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-logo-rtl"), direction === "rtl"), "".concat(baseClassName, "-logo-mix"), layout === "mix"), "".concat(baseClassName, "-logo-mobile"), isMobile));
  var logoDom = (0, import_jsx_runtime36.jsx)("span", {
    className: logoClassNames,
    children: (0, import_jsx_runtime36.jsx)("a", {
      children: defaultRenderLogo(logo)
    })
  }, "logo");
  return wrapSSR((0, import_jsx_runtime38.jsxs)("div", {
    className,
    style: _objectSpread2({}, style),
    children: [isMobile && (0, import_jsx_runtime36.jsx)("span", {
      className: "".concat(baseClassName, "-collapsed-button ").concat(hashId).trim(),
      onClick: function onClick() {
        onCollapse === null || onCollapse === void 0 || onCollapse(!collapsed);
      },
      children: (0, import_jsx_runtime36.jsx)(MenuOutlined_default, {})
    }), isMobile && renderLogo3(menuHeaderRender, logoDom), layout === "mix" && !isMobile && (0, import_jsx_runtime38.jsxs)(import_jsx_runtime37.Fragment, {
      children: [(0, import_jsx_runtime36.jsx)(AppsLogoComponents, _objectSpread2({}, props)), (0, import_jsx_runtime36.jsx)("div", {
        className: logoClassNames,
        onClick: onMenuHeaderClick,
        children: renderLogoAndTitle(_objectSpread2(_objectSpread2({}, props), {}, {
          collapsed: false
        }), "headerTitleRender")
      })]
    }), (0, import_jsx_runtime36.jsx)("div", {
      style: {
        flex: 1
      },
      children
    }), (rightContentRender || props.actionsRender || props.avatarProps) && (0, import_jsx_runtime36.jsx)(ActionsContent, _objectSpread2({
      rightContentRender
    }, props))]
  }));
};

// node_modules/@ant-design/pro-layout/es/components/Header/style/header.js
init_defineProperty();
var genProLayoutHeaderStyle = function genProLayoutHeaderStyle2(token) {
  var _token$layout, _token$layout2, _token$layout3, _token$layout4;
  return _defineProperty({}, "".concat(token.proComponentsCls, "-layout"), _defineProperty({}, "".concat(token.antCls, "-layout-header").concat(token.componentCls), {
    height: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56,
    lineHeight: "".concat(((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader) || 56, "px"),
    // hitu 用了这个属性，不能删除哦 @南取
    zIndex: 19,
    width: "100%",
    paddingBlock: 0,
    paddingInline: 0,
    borderBlockEnd: "1px solid ".concat(token.colorSplit),
    backgroundColor: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgHeader) || "rgba(255, 255, 255, 0.4)",
    WebkitBackdropFilter: "blur(8px)",
    backdropFilter: "blur(8px)",
    transition: "background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)",
    "&-fixed-header": {
      position: "fixed",
      insetBlockStart: 0,
      width: "100%",
      zIndex: 100,
      insetInlineEnd: 0
    },
    "&-fixed-header-scroll": {
      backgroundColor: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgScrollHeader) || "rgba(255, 255, 255, 0.8)"
    },
    "&-header-actions": {
      display: "flex",
      alignItems: "center",
      fontSize: "16",
      cursor: "pointer",
      "& &-item": {
        paddingBlock: 0,
        paddingInline: 8,
        "&:hover": {
          color: token.colorText
        }
      }
    },
    "&-header-realDark": {
      boxShadow: "0 2px 8px 0 rgba(0, 0, 0, 65%)"
    },
    "&-header-actions-header-action": {
      transition: "width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"
    }
  }));
};
function useStyle13(prefixCls) {
  return useStyle("ProLayoutHeader", function(token) {
    var ProLayoutHeaderToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProLayoutHeaderStyle(ProLayoutHeaderToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/Header/style/stylish.js
init_defineProperty();
function useStylish4(prefixCls, _ref) {
  var stylish = _ref.stylish, proLayoutCollapsedWidth = _ref.proLayoutCollapsedWidth;
  return useStyle("ProLayoutHeaderStylish", function(token) {
    var stylishToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls),
      proLayoutCollapsedWidth
    });
    if (!stylish)
      return [];
    return [_defineProperty({}, "div".concat(token.proComponentsCls, "-layout"), _defineProperty({}, "".concat(stylishToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(stylishToken)))];
  });
}

// node_modules/@ant-design/pro-layout/es/components/Header/index.js
var import_jsx_runtime39 = __toESM(require_jsx_runtime());
var import_jsx_runtime40 = __toESM(require_jsx_runtime());
var import_jsx_runtime41 = __toESM(require_jsx_runtime());
var Header = layout_default.Header;
var DefaultHeader = function DefaultHeader2(props) {
  var _token$layout2, _token$layout3, _token$layout4;
  var isMobile = props.isMobile, fixedHeader = props.fixedHeader, propsClassName = props.className, style = props.style, collapsed = props.collapsed, prefixCls = props.prefixCls, onCollapse = props.onCollapse, layout = props.layout, headerRender3 = props.headerRender, headerContentRender = props.headerContentRender;
  var _useContext = (0, import_react20.useContext)(ProProvider), token = _useContext.token;
  var context = (0, import_react20.useContext)(config_provider_default.ConfigContext);
  var _useState = (0, import_react20.useState)(false), _useState2 = _slicedToArray(_useState, 2), isFixedHeaderScroll = _useState2[0], setIsFixedHeaderScroll = _useState2[1];
  var needFixedHeader = fixedHeader || layout === "mix";
  var renderContent = (0, import_react20.useCallback)(function() {
    var isTop2 = layout === "top";
    var clearMenuData = clearMenuItem(props.menuData || []);
    var defaultDom = (0, import_jsx_runtime39.jsx)(GlobalHeader, _objectSpread2(_objectSpread2({
      onCollapse
    }, props), {}, {
      menuData: clearMenuData,
      children: headerContentRender && headerContentRender(props, null)
    }));
    if (isTop2 && !isMobile) {
      defaultDom = (0, import_jsx_runtime39.jsx)(TopNavHeader, _objectSpread2(_objectSpread2({
        mode: "horizontal",
        onCollapse
      }, props), {}, {
        menuData: clearMenuData
      }));
    }
    if (headerRender3 && typeof headerRender3 === "function") {
      return headerRender3(props, defaultDom);
    }
    return defaultDom;
  }, [headerContentRender, headerRender3, isMobile, layout, onCollapse, props]);
  (0, import_react20.useEffect)(function() {
    var _context$getTargetCon;
    var dom = (context === null || context === void 0 || (_context$getTargetCon = context.getTargetContainer) === null || _context$getTargetCon === void 0 ? void 0 : _context$getTargetCon.call(context)) || document.body;
    var isFixedHeaderFn = function isFixedHeaderFn2() {
      var _token$layout;
      var scrollTop = dom.scrollTop;
      if (scrollTop > (((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56) && !isFixedHeaderScroll) {
        setIsFixedHeaderScroll(true);
        return true;
      }
      if (isFixedHeaderScroll) {
        setIsFixedHeaderScroll(false);
      }
      return false;
    };
    if (!needFixedHeader)
      return;
    if (typeof window === "undefined")
      return;
    dom.addEventListener("scroll", isFixedHeaderFn, {
      passive: true
    });
    return function() {
      dom.removeEventListener("scroll", isFixedHeaderFn);
    };
  }, [(_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader, needFixedHeader, isFixedHeaderScroll]);
  var isTop = layout === "top";
  var baseClassName = "".concat(prefixCls, "-layout-header");
  var _useStyle = useStyle13(baseClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var stylish = useStylish4("".concat(baseClassName, ".").concat(baseClassName, "-stylish"), {
    proLayoutCollapsedWidth: 64,
    stylish: props.stylish
  });
  var className = (0, import_classnames14.default)(propsClassName, hashId, baseClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(baseClassName, "-fixed-header"), needFixedHeader), "".concat(baseClassName, "-fixed-header-scroll"), isFixedHeaderScroll), "".concat(baseClassName, "-mix"), layout === "mix"), "".concat(baseClassName, "-fixed-header-action"), !collapsed), "".concat(baseClassName, "-top-menu"), isTop), "".concat(baseClassName, "-header"), true), "".concat(baseClassName, "-stylish"), !!props.stylish));
  if (layout === "side" && !isMobile)
    return null;
  return stylish.wrapSSR(wrapSSR((0, import_jsx_runtime39.jsx)(import_jsx_runtime41.Fragment, {
    children: (0, import_jsx_runtime40.jsxs)(
      config_provider_default,
      {
        theme: {
          hashed: isNeedOpenHash(),
          components: {
            Layout: {
              headerBg: "transparent",
              bodyBg: "transparent"
            }
          }
        },
        children: [needFixedHeader && (0, import_jsx_runtime39.jsx)(Header, {
          style: _objectSpread2({
            height: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.heightLayoutHeader) || 56,
            lineHeight: "".concat(((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.heightLayoutHeader) || 56, "px"),
            backgroundColor: "transparent",
            zIndex: 19
          }, style)
        }), (0, import_jsx_runtime39.jsx)(Header, {
          className,
          style,
          children: renderContent()
        })]
      }
    )
  })));
};

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js
var import_react25 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/locales/en-US/settingDrawer.js
var settingDrawer_default = {
  "app.setting.pagestyle": "Page style setting",
  "app.setting.pagestyle.dark": "Dark Menu style",
  "app.setting.pagestyle.light": "Light Menu style",
  "app.setting.pagestyle.realdark": "Dark style (Beta)",
  "app.setting.content-width": "Content Width",
  "app.setting.content-width.fixed": "Fixed",
  "app.setting.content-width.fluid": "Fluid",
  "app.setting.themecolor": "Theme Color",
  "app.setting.themecolor.dust": "Dust Red",
  "app.setting.themecolor.volcano": "Volcano",
  "app.setting.themecolor.sunset": "Sunset Orange",
  "app.setting.themecolor.cyan": "Cyan",
  "app.setting.themecolor.green": "Polar Green",
  "app.setting.themecolor.techBlue": "Tech Blue (default)",
  "app.setting.themecolor.daybreak": "Daybreak Blue",
  "app.setting.themecolor.geekblue": "Geek Blue",
  "app.setting.themecolor.purple": "Golden Purple",
  "app.setting.sidermenutype": "SideMenu Type",
  "app.setting.sidermenutype-sub": "Classic",
  "app.setting.sidermenutype-group": "Grouping",
  "app.setting.navigationmode": "Navigation Mode",
  "app.setting.regionalsettings": "Regional Settings",
  "app.setting.regionalsettings.header": "Header",
  "app.setting.regionalsettings.menu": "Menu",
  "app.setting.regionalsettings.footer": "Footer",
  "app.setting.regionalsettings.menuHeader": "Menu Header",
  "app.setting.sidemenu": "Side Menu Layout",
  "app.setting.topmenu": "Top Menu Layout",
  "app.setting.mixmenu": "Mix Menu Layout",
  "app.setting.splitMenus": "Split Menus",
  "app.setting.fixedheader": "Fixed Header",
  "app.setting.fixedsidebar": "Fixed Sidebar",
  "app.setting.fixedsidebar.hint": "Works on Side Menu Layout",
  "app.setting.hideheader": "Hidden Header when scrolling",
  "app.setting.hideheader.hint": "Works when Hidden Header is enabled",
  "app.setting.othersettings": "Other Settings",
  "app.setting.weakmode": "Weak Mode",
  "app.setting.copy": "Copy Setting",
  "app.setting.loading": "Loading theme",
  "app.setting.copyinfo": "copy success，please replace defaultSettings in src/models/setting.js",
  "app.setting.production.hint": "Setting panel shows in development environment only, please manually modify"
};

// node_modules/@ant-design/pro-layout/es/locales/en-US.js
var en_US_default = _objectSpread2({}, settingDrawer_default);

// node_modules/@ant-design/pro-layout/es/locales/it-IT/settingDrawer.js
var settingDrawer_default2 = {
  "app.setting.pagestyle": "Impostazioni di stile",
  "app.setting.pagestyle.dark": "Tema scuro",
  "app.setting.pagestyle.light": "Tema chiaro",
  "app.setting.content-width": "Largezza contenuto",
  "app.setting.content-width.fixed": "Fissa",
  "app.setting.content-width.fluid": "Fluida",
  "app.setting.themecolor": "Colore del tema",
  "app.setting.themecolor.dust": "Rosso polvere",
  "app.setting.themecolor.volcano": "Vulcano",
  "app.setting.themecolor.sunset": "Arancione tramonto",
  "app.setting.themecolor.cyan": "Ciano",
  "app.setting.themecolor.green": "Verde polare",
  "app.setting.themecolor.techBlue": "Tech Blu (default)",
  "app.setting.themecolor.daybreak": "Blu cielo mattutino",
  "app.setting.themecolor.geekblue": "Blu geek",
  "app.setting.themecolor.purple": "Viola dorato",
  "app.setting.navigationmode": "Modalità di navigazione",
  "app.setting.sidemenu": "Menu laterale",
  "app.setting.topmenu": "Menu in testata",
  "app.setting.mixmenu": "Menu misto",
  "app.setting.splitMenus": "Menu divisi",
  "app.setting.fixedheader": "Testata fissa",
  "app.setting.fixedsidebar": "Menu laterale fisso",
  "app.setting.fixedsidebar.hint": "Solo se selezionato Menu laterale",
  "app.setting.hideheader": "Nascondi testata durante lo scorrimento",
  "app.setting.hideheader.hint": "Solo se abilitato Nascondi testata durante lo scorrimento",
  "app.setting.othersettings": "Altre impostazioni",
  "app.setting.weakmode": "Inverti colori",
  "app.setting.copy": "Copia impostazioni",
  "app.setting.loading": "Carico tema...",
  "app.setting.copyinfo": "Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js",
  "app.setting.production.hint": "Questo pannello è visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"
};

// node_modules/@ant-design/pro-layout/es/locales/it-IT.js
var it_IT_default = _objectSpread2({}, settingDrawer_default2);

// node_modules/@ant-design/pro-layout/es/locales/ko-KR/settingDrawer.js
var settingDrawer_default3 = {
  "app.setting.pagestyle": "스타일 설정",
  "app.setting.pagestyle.dark": "다크 모드",
  "app.setting.pagestyle.light": "라이트 모드",
  "app.setting.content-width": "컨텐츠 너비",
  "app.setting.content-width.fixed": "고정",
  "app.setting.content-width.fluid": "흐름",
  "app.setting.themecolor": "테마 색상",
  "app.setting.themecolor.dust": "Dust Red",
  "app.setting.themecolor.volcano": "Volcano",
  "app.setting.themecolor.sunset": "Sunset Orange",
  "app.setting.themecolor.cyan": "Cyan",
  "app.setting.themecolor.green": "Polar Green",
  "app.setting.themecolor.techBlue": "Tech Blu (default)",
  "app.setting.themecolor.daybreak": "Daybreak Blue",
  "app.setting.themecolor.geekblue": "Geek Blue",
  "app.setting.themecolor.purple": "Golden Purple",
  "app.setting.navigationmode": "네비게이션 모드",
  "app.setting.regionalsettings": "영역별 설정",
  "app.setting.regionalsettings.header": "헤더",
  "app.setting.regionalsettings.menu": "메뉴",
  "app.setting.regionalsettings.footer": "바닥글",
  "app.setting.regionalsettings.menuHeader": "메뉴 헤더",
  "app.setting.sidemenu": "메뉴 사이드 배치",
  "app.setting.topmenu": "메뉴 상단 배치",
  "app.setting.mixmenu": "혼합형 배치",
  "app.setting.splitMenus": "메뉴 분리",
  "app.setting.fixedheader": "헤더 고정",
  "app.setting.fixedsidebar": "사이드바 고정",
  "app.setting.fixedsidebar.hint": "'메뉴 사이드 배치'를 선택했을 때 동작함",
  "app.setting.hideheader": "스크롤 중 헤더 감추기",
  "app.setting.hideheader.hint": "'헤더 감추기 옵션'을 선택했을 때 동작함",
  "app.setting.othersettings": "다른 설정",
  "app.setting.weakmode": "고대비 모드",
  "app.setting.copy": "설정값 복사",
  "app.setting.loading": "테마 로딩 중",
  "app.setting.copyinfo": "복사 성공. src/models/settings.js에 있는 defaultSettings를 교체해 주세요.",
  "app.setting.production.hint": "설정 판넬은 개발 환경에서만 보여집니다. 직접 수동으로 변경바랍니다."
};

// node_modules/@ant-design/pro-layout/es/locales/ko-KR.js
var ko_KR_default = _objectSpread2({}, settingDrawer_default3);

// node_modules/@ant-design/pro-layout/es/locales/zh-CN/settingDrawer.js
var settingDrawer_default4 = {
  "app.setting.pagestyle": "整体风格设置",
  "app.setting.pagestyle.dark": "暗色菜单风格",
  "app.setting.pagestyle.light": "亮色菜单风格",
  "app.setting.pagestyle.realdark": "暗色风格(实验功能)",
  "app.setting.content-width": "内容区域宽度",
  "app.setting.content-width.fixed": "定宽",
  "app.setting.content-width.fluid": "流式",
  "app.setting.themecolor": "主题色",
  "app.setting.themecolor.dust": "薄暮",
  "app.setting.themecolor.volcano": "火山",
  "app.setting.themecolor.sunset": "日暮",
  "app.setting.themecolor.cyan": "明青",
  "app.setting.themecolor.green": "极光绿",
  "app.setting.themecolor.techBlue": "科技蓝（默认）",
  "app.setting.themecolor.daybreak": "拂晓",
  "app.setting.themecolor.geekblue": "极客蓝",
  "app.setting.themecolor.purple": "酱紫",
  "app.setting.navigationmode": "导航模式",
  "app.setting.sidermenutype": "侧边菜单类型",
  "app.setting.sidermenutype-sub": "经典模式",
  "app.setting.sidermenutype-group": "分组模式",
  "app.setting.regionalsettings": "内容区域",
  "app.setting.regionalsettings.header": "顶栏",
  "app.setting.regionalsettings.menu": "菜单",
  "app.setting.regionalsettings.footer": "页脚",
  "app.setting.regionalsettings.menuHeader": "菜单头",
  "app.setting.sidemenu": "侧边菜单布局",
  "app.setting.topmenu": "顶部菜单布局",
  "app.setting.mixmenu": "混合菜单布局",
  "app.setting.splitMenus": "自动分割菜单",
  "app.setting.fixedheader": "固定 Header",
  "app.setting.fixedsidebar": "固定侧边菜单",
  "app.setting.fixedsidebar.hint": "侧边菜单布局时可配置",
  "app.setting.hideheader": "下滑时隐藏 Header",
  "app.setting.hideheader.hint": "固定 Header 时可配置",
  "app.setting.othersettings": "其他设置",
  "app.setting.weakmode": "色弱模式",
  "app.setting.copy": "拷贝设置",
  "app.setting.loading": "正在加载主题",
  "app.setting.copyinfo": "拷贝成功，请到 src/defaultSettings.js 中替换默认配置",
  "app.setting.production.hint": "配置栏只在开发环境用于预览，生产环境不会展现，请拷贝后手动修改配置文件"
};

// node_modules/@ant-design/pro-layout/es/locales/zh-CN.js
var zh_CN_default = _objectSpread2({}, settingDrawer_default4);

// node_modules/@ant-design/pro-layout/es/locales/zh-TW/settingDrawer.js
var settingDrawer_default5 = {
  "app.setting.pagestyle": "整體風格設置",
  "app.setting.pagestyle.dark": "暗色菜單風格",
  "app.setting.pagestyle.realdark": "暗色風格(实验功能)",
  "app.setting.pagestyle.light": "亮色菜單風格",
  "app.setting.content-width": "內容區域寬度",
  "app.setting.content-width.fixed": "定寬",
  "app.setting.content-width.fluid": "流式",
  "app.setting.themecolor": "主題色",
  "app.setting.themecolor.dust": "薄暮",
  "app.setting.themecolor.volcano": "火山",
  "app.setting.themecolor.sunset": "日暮",
  "app.setting.themecolor.cyan": "明青",
  "app.setting.themecolor.green": "極光綠",
  "app.setting.themecolor.techBlue": "科技蓝（默認）",
  "app.setting.themecolor.daybreak": "拂曉藍",
  "app.setting.themecolor.geekblue": "極客藍",
  "app.setting.themecolor.purple": "醬紫",
  "app.setting.navigationmode": "導航模式",
  "app.setting.sidemenu": "側邊菜單布局",
  "app.setting.topmenu": "頂部菜單布局",
  "app.setting.mixmenu": "混合菜單布局",
  "app.setting.splitMenus": "自动分割菜单",
  "app.setting.fixedheader": "固定 Header",
  "app.setting.fixedsidebar": "固定側邊菜單",
  "app.setting.fixedsidebar.hint": "側邊菜單布局時可配置",
  "app.setting.hideheader": "下滑時隱藏 Header",
  "app.setting.hideheader.hint": "固定 Header 時可配置",
  "app.setting.othersettings": "其他設置",
  "app.setting.weakmode": "色弱模式",
  "app.setting.copy": "拷貝設置",
  "app.setting.loading": "正在加載主題",
  "app.setting.copyinfo": "拷貝成功，請到 src/defaultSettings.js 中替換默認配置",
  "app.setting.production.hint": "配置欄只在開發環境用於預覽，生產環境不會展現，請拷貝後手動修改配置文件"
};

// node_modules/@ant-design/pro-layout/es/locales/zh-TW.js
var zh_TW_default = _objectSpread2({}, settingDrawer_default5);

// node_modules/@ant-design/pro-layout/es/locales/index.js
var locales = {
  "zh-CN": zh_CN_default,
  "zh-TW": zh_TW_default,
  "en-US": en_US_default,
  "it-IT": it_IT_default,
  "ko-KR": ko_KR_default
};
var getLanguage = function getLanguage2() {
  if (!isBrowser())
    return "zh-CN";
  var lang = window.localStorage.getItem("umi_locale");
  return lang || window.g_locale || navigator.language;
};
var gLocaleObject = function gLocaleObject2() {
  var gLocale = getLanguage();
  return locales[gLocale] || locales["zh-CN"];
};

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/BlockCheckbox.js
var import_classnames15 = __toESM(require_classnames());
var import_react21 = __toESM(require_react());
var import_jsx_runtime42 = __toESM(require_jsx_runtime());
var import_jsx_runtime43 = __toESM(require_jsx_runtime());
var BlockCheckbox = function BlockCheckbox2(_ref) {
  var value = _ref.value, configType = _ref.configType, onChange = _ref.onChange, list = _ref.list, prefixCls = _ref.prefixCls, hashId = _ref.hashId;
  var baseClassName = "".concat(prefixCls, "-block-checkbox");
  var dom = (0, import_react21.useMemo)(function() {
    var domList = (list || []).map(function(item) {
      return (0, import_jsx_runtime42.jsx)(tooltip_default, {
        title: item.title,
        children: (0, import_jsx_runtime43.jsxs)("div", {
          className: (0, import_classnames15.default)(hashId, "".concat(baseClassName, "-item"), "".concat(baseClassName, "-item-").concat(item.key), "".concat(baseClassName, "-").concat(configType, "-item")),
          onClick: function onClick() {
            return onChange(item.key);
          },
          children: [(0, import_jsx_runtime42.jsx)(CheckOutlined_default, {
            className: "".concat(baseClassName, "-selectIcon ").concat(hashId).trim(),
            style: {
              display: value === item.key ? "block" : "none"
            }
          }), item !== null && item !== void 0 && item.icon ? (0, import_jsx_runtime42.jsx)("div", {
            className: "".concat(baseClassName, "-icon ").concat(hashId).trim(),
            children: item.icon
          }) : null]
        })
      }, item.key);
    });
    return domList;
  }, [value, list === null || list === void 0 ? void 0 : list.length, onChange]);
  return (0, import_jsx_runtime42.jsx)("div", {
    className: (0, import_classnames15.default)(baseClassName, hashId),
    children: dom
  });
};

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/LayoutChange.js
var import_react22 = __toESM(require_react());
var import_jsx_runtime44 = __toESM(require_jsx_runtime());
var import_jsx_runtime45 = __toESM(require_jsx_runtime());
var renderLayoutSettingItem = function renderLayoutSettingItem2(item) {
  var action = import_react22.default.cloneElement(item.action, {
    disabled: item.disabled
  });
  return (0, import_jsx_runtime44.jsx)(tooltip_default, {
    title: item.disabled ? item.disabledReason : "",
    placement: "left",
    children: (0, import_jsx_runtime44.jsx)(list_default.Item, {
      actions: [action],
      children: (0, import_jsx_runtime44.jsx)("span", {
        style: {
          opacity: item.disabled ? 0.5 : 1
        },
        children: item.title
      })
    })
  });
};
var LayoutSetting = function LayoutSetting2(_ref) {
  var settings = _ref.settings, prefixCls = _ref.prefixCls, changeSetting = _ref.changeSetting, hashId = _ref.hashId;
  var formatMessage = getFormatMessage();
  var _ref2 = settings || defaultSettings, contentWidth = _ref2.contentWidth, splitMenus = _ref2.splitMenus, fixedHeader = _ref2.fixedHeader, layout = _ref2.layout, fixSiderbar = _ref2.fixSiderbar;
  return (0, import_jsx_runtime44.jsx)(list_default, {
    className: "".concat(prefixCls, "-list ").concat(hashId).trim(),
    split: false,
    dataSource: [{
      title: formatMessage({
        id: "app.setting.content-width",
        defaultMessage: "Content Width"
      }),
      action: (0, import_jsx_runtime45.jsxs)(select_default, {
        value: contentWidth || "Fixed",
        size: "small",
        className: "content-width ".concat(hashId).trim(),
        onSelect: function onSelect(value) {
          changeSetting("contentWidth", value);
        },
        style: {
          width: 80
        },
        children: [layout === "side" ? null : (0, import_jsx_runtime44.jsx)(select_default.Option, {
          value: "Fixed",
          children: formatMessage({
            id: "app.setting.content-width.fixed",
            defaultMessage: "Fixed"
          })
        }), (0, import_jsx_runtime44.jsx)(select_default.Option, {
          value: "Fluid",
          children: formatMessage({
            id: "app.setting.content-width.fluid",
            defaultMessage: "Fluid"
          })
        })]
      })
    }, {
      title: formatMessage({
        id: "app.setting.fixedheader",
        defaultMessage: "Fixed Header"
      }),
      action: (0, import_jsx_runtime44.jsx)(switch_default, {
        size: "small",
        className: "fixed-header",
        checked: !!fixedHeader,
        onChange: function onChange(checked) {
          changeSetting("fixedHeader", checked);
        }
      })
    }, {
      title: formatMessage({
        id: "app.setting.fixedsidebar",
        defaultMessage: "Fixed Sidebar"
      }),
      disabled: layout === "top",
      disabledReason: formatMessage({
        id: "app.setting.fixedsidebar.hint",
        defaultMessage: "Works on Side Menu Layout"
      }),
      action: (0, import_jsx_runtime44.jsx)(switch_default, {
        size: "small",
        className: "fix-siderbar",
        checked: !!fixSiderbar,
        onChange: function onChange(checked) {
          return changeSetting("fixSiderbar", checked);
        }
      })
    }, {
      title: formatMessage({
        id: "app.setting.splitMenus"
      }),
      disabled: layout !== "mix",
      action: (0, import_jsx_runtime44.jsx)(switch_default, {
        size: "small",
        checked: !!splitMenus,
        className: "split-menus",
        onChange: function onChange(checked) {
          changeSetting("splitMenus", checked);
        }
      })
    }],
    renderItem: renderLayoutSettingItem
  });
};

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/RegionalChange.js
var import_react23 = __toESM(require_react());
var import_jsx_runtime46 = __toESM(require_jsx_runtime());
var RegionalSetting = function RegionalSetting2(_ref) {
  var settings = _ref.settings, prefixCls = _ref.prefixCls, changeSetting = _ref.changeSetting, hashId = _ref.hashId;
  var formatMessage = getFormatMessage();
  var regionalSetting = ["header", "footer", "menu", "menuHeader"];
  return (0, import_jsx_runtime46.jsx)(list_default, {
    className: "".concat(prefixCls, "-list ").concat(hashId).trim(),
    split: false,
    renderItem: renderLayoutSettingItem,
    dataSource: regionalSetting.map(function(key) {
      return {
        title: formatMessage({
          id: "app.setting.regionalsettings.".concat(key)
        }),
        action: (0, import_jsx_runtime46.jsx)(switch_default, {
          size: "small",
          className: "regional-".concat(key, " ").concat(hashId).trim(),
          checked: settings["".concat(key, "Render")] || settings["".concat(key, "Render")] === void 0,
          onChange: function onChange(checked) {
            return changeSetting("".concat(key, "Render"), checked === true ? void 0 : false);
          }
        })
      };
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.js
var import_react24 = __toESM(require_react());
var import_jsx_runtime47 = __toESM(require_jsx_runtime());
var _excluded7 = ["color", "check"];
var Tag = import_react24.default.forwardRef(function(_ref, ref) {
  var color = _ref.color, check = _ref.check, rest = _objectWithoutProperties(_ref, _excluded7);
  return (0, import_jsx_runtime47.jsx)("div", _objectSpread2(_objectSpread2({}, rest), {}, {
    style: {
      backgroundColor: color
    },
    ref,
    children: check ? (0, import_jsx_runtime47.jsx)(CheckOutlined_default, {}) : ""
  }));
});
var ThemeColor = function ThemeColor2(_ref2) {
  var value = _ref2.value, colorList = _ref2.colorList, onChange = _ref2.onChange, prefixCls = _ref2.prefixCls, formatMessage = _ref2.formatMessage, hashId = _ref2.hashId;
  if (!colorList || (colorList === null || colorList === void 0 ? void 0 : colorList.length) < 1) {
    return null;
  }
  var baseClassName = "".concat(prefixCls, "-theme-color");
  return (0, import_jsx_runtime47.jsx)("div", {
    className: "".concat(baseClassName, " ").concat(hashId).trim(),
    children: colorList === null || colorList === void 0 ? void 0 : colorList.map(function(_ref3) {
      var key = _ref3.key, color = _ref3.color, title = _ref3.title;
      if (!key)
        return null;
      return (0, import_jsx_runtime47.jsx)(tooltip_default, {
        title: title !== null && title !== void 0 ? title : formatMessage({
          id: "app.setting.themecolor.".concat(key)
        }),
        children: (0, import_jsx_runtime47.jsx)(Tag, {
          className: "".concat(baseClassName, "-block ").concat(hashId).trim(),
          color,
          check: value === color,
          onClick: function onClick() {
            return onChange && onChange(color);
          }
        })
      }, color);
    })
  });
};

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/icon/group.js
var import_jsx_runtime48 = __toESM(require_jsx_runtime());
var import_jsx_runtime49 = __toESM(require_jsx_runtime());
function GroupIcon() {
  return (0, import_jsx_runtime49.jsxs)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    width: "1em",
    height: "1em",
    viewBox: "0 0 104 104",
    children: [(0, import_jsx_runtime49.jsxs)("defs", {
      children: [(0, import_jsx_runtime48.jsx)("rect", {
        id: "path-1",
        width: "90",
        height: "72",
        x: "0",
        y: "0",
        rx: "10"
      }), (0, import_jsx_runtime49.jsxs)("filter", {
        id: "filter-2",
        width: "152.2%",
        height: "165.3%",
        x: "-26.1%",
        y: "-27.1%",
        filterUnits: "objectBoundingBox",
        children: [(0, import_jsx_runtime48.jsx)("feMorphology", {
          in: "SourceAlpha",
          radius: "0.25",
          result: "shadowSpreadOuter1"
        }), (0, import_jsx_runtime48.jsx)("feOffset", {
          dy: "1",
          in: "shadowSpreadOuter1",
          result: "shadowOffsetOuter1"
        }), (0, import_jsx_runtime48.jsx)("feGaussianBlur", {
          in: "shadowOffsetOuter1",
          result: "shadowBlurOuter1",
          stdDeviation: "1"
        }), (0, import_jsx_runtime48.jsx)("feColorMatrix", {
          in: "shadowBlurOuter1",
          result: "shadowMatrixOuter1",
          values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
        }), (0, import_jsx_runtime48.jsx)("feMorphology", {
          in: "SourceAlpha",
          radius: "1",
          result: "shadowSpreadOuter2"
        }), (0, import_jsx_runtime48.jsx)("feOffset", {
          dy: "2",
          in: "shadowSpreadOuter2",
          result: "shadowOffsetOuter2"
        }), (0, import_jsx_runtime48.jsx)("feGaussianBlur", {
          in: "shadowOffsetOuter2",
          result: "shadowBlurOuter2",
          stdDeviation: "4"
        }), (0, import_jsx_runtime48.jsx)("feColorMatrix", {
          in: "shadowBlurOuter2",
          result: "shadowMatrixOuter2",
          values: "0 0 0 0 0.098466735 0 0 0 0 0.0599695403 0 0 0 0 0.0599695403 0 0 0 0.07 0"
        }), (0, import_jsx_runtime48.jsx)("feMorphology", {
          in: "SourceAlpha",
          radius: "2",
          result: "shadowSpreadOuter3"
        }), (0, import_jsx_runtime48.jsx)("feOffset", {
          dy: "4",
          in: "shadowSpreadOuter3",
          result: "shadowOffsetOuter3"
        }), (0, import_jsx_runtime48.jsx)("feGaussianBlur", {
          in: "shadowOffsetOuter3",
          result: "shadowBlurOuter3",
          stdDeviation: "8"
        }), (0, import_jsx_runtime48.jsx)("feColorMatrix", {
          in: "shadowBlurOuter3",
          result: "shadowMatrixOuter3",
          values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"
        }), (0, import_jsx_runtime49.jsxs)("feMerge", {
          children: [(0, import_jsx_runtime48.jsx)("feMergeNode", {
            in: "shadowMatrixOuter1"
          }), (0, import_jsx_runtime48.jsx)("feMergeNode", {
            in: "shadowMatrixOuter2"
          }), (0, import_jsx_runtime48.jsx)("feMergeNode", {
            in: "shadowMatrixOuter3"
          })]
        })]
      })]
    }), (0, import_jsx_runtime49.jsxs)("g", {
      fill: "none",
      fillRule: "evenodd",
      stroke: "none",
      strokeWidth: "1",
      children: [(0, import_jsx_runtime49.jsxs)("g", {
        children: [(0, import_jsx_runtime48.jsx)("use", {
          fill: "#000",
          filter: "url(#filter-2)",
          xlinkHref: "#path-1"
        }), (0, import_jsx_runtime48.jsx)("use", {
          fill: "#F0F2F5",
          xlinkHref: "#path-1"
        })]
      }), (0, import_jsx_runtime48.jsx)("path", {
        fill: "#FFF",
        d: "M25 15h65v47c0 5.523-4.477 10-10 10H25V15z"
      }), (0, import_jsx_runtime48.jsx)("path", {
        stroke: "#E6EAF0",
        strokeLinecap: "square",
        d: "M0.5 15.5L90.5 15.5"
      }), (0, import_jsx_runtime48.jsx)("rect", {
        width: "14",
        height: "3",
        x: "4",
        y: "26",
        fill: "#D7DDE6",
        rx: "1.5"
      }), (0, import_jsx_runtime48.jsx)("rect", {
        width: "9",
        height: "3",
        x: "4",
        y: "32",
        fill: "#D7DDE6",
        rx: "1.5"
      }), (0, import_jsx_runtime48.jsx)("rect", {
        width: "9",
        height: "3",
        x: "4",
        y: "42",
        fill: "#E6EAF0",
        rx: "1.5"
      }), (0, import_jsx_runtime48.jsx)("rect", {
        width: "9",
        height: "3",
        x: "4",
        y: "21",
        fill: "#E6EAF0",
        rx: "1.5"
      }), (0, import_jsx_runtime48.jsx)("rect", {
        width: "9",
        height: "3",
        x: "4",
        y: "53",
        fill: "#D7DDE6",
        rx: "1.5"
      }), (0, import_jsx_runtime48.jsx)("rect", {
        width: "14",
        height: "3",
        x: "4",
        y: "47",
        fill: "#D7DDE6",
        rx: "1.5"
      }), (0, import_jsx_runtime48.jsx)("path", {
        stroke: "#E6EAF0",
        strokeLinecap: "square",
        d: "M25.5 15.5L25.5 72.5"
      })]
    })]
  });
}

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/icon/sub.js
var import_jsx_runtime50 = __toESM(require_jsx_runtime());
var import_jsx_runtime51 = __toESM(require_jsx_runtime());
function SubIcon() {
  return (0, import_jsx_runtime51.jsxs)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    width: "1em",
    height: "1em",
    viewBox: "0 0 104 104",
    children: [(0, import_jsx_runtime51.jsxs)("defs", {
      children: [(0, import_jsx_runtime50.jsx)("rect", {
        id: "path-1",
        width: "90",
        height: "72",
        x: "0",
        y: "0",
        rx: "10"
      }), (0, import_jsx_runtime51.jsxs)("filter", {
        id: "filter-2",
        width: "152.2%",
        height: "165.3%",
        x: "-26.1%",
        y: "-27.1%",
        filterUnits: "objectBoundingBox",
        children: [(0, import_jsx_runtime50.jsx)("feMorphology", {
          in: "SourceAlpha",
          radius: "0.25",
          result: "shadowSpreadOuter1"
        }), (0, import_jsx_runtime50.jsx)("feOffset", {
          dy: "1",
          in: "shadowSpreadOuter1",
          result: "shadowOffsetOuter1"
        }), (0, import_jsx_runtime50.jsx)("feGaussianBlur", {
          in: "shadowOffsetOuter1",
          result: "shadowBlurOuter1",
          stdDeviation: "1"
        }), (0, import_jsx_runtime50.jsx)("feColorMatrix", {
          in: "shadowBlurOuter1",
          result: "shadowMatrixOuter1",
          values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
        }), (0, import_jsx_runtime50.jsx)("feMorphology", {
          in: "SourceAlpha",
          radius: "1",
          result: "shadowSpreadOuter2"
        }), (0, import_jsx_runtime50.jsx)("feOffset", {
          dy: "2",
          in: "shadowSpreadOuter2",
          result: "shadowOffsetOuter2"
        }), (0, import_jsx_runtime50.jsx)("feGaussianBlur", {
          in: "shadowOffsetOuter2",
          result: "shadowBlurOuter2",
          stdDeviation: "4"
        }), (0, import_jsx_runtime50.jsx)("feColorMatrix", {
          in: "shadowBlurOuter2",
          result: "shadowMatrixOuter2",
          values: "0 0 0 0 0.098466735 0 0 0 0 0.0599695403 0 0 0 0 0.0599695403 0 0 0 0.07 0"
        }), (0, import_jsx_runtime50.jsx)("feMorphology", {
          in: "SourceAlpha",
          radius: "2",
          result: "shadowSpreadOuter3"
        }), (0, import_jsx_runtime50.jsx)("feOffset", {
          dy: "4",
          in: "shadowSpreadOuter3",
          result: "shadowOffsetOuter3"
        }), (0, import_jsx_runtime50.jsx)("feGaussianBlur", {
          in: "shadowOffsetOuter3",
          result: "shadowBlurOuter3",
          stdDeviation: "8"
        }), (0, import_jsx_runtime50.jsx)("feColorMatrix", {
          in: "shadowBlurOuter3",
          result: "shadowMatrixOuter3",
          values: "0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"
        }), (0, import_jsx_runtime51.jsxs)("feMerge", {
          children: [(0, import_jsx_runtime50.jsx)("feMergeNode", {
            in: "shadowMatrixOuter1"
          }), (0, import_jsx_runtime50.jsx)("feMergeNode", {
            in: "shadowMatrixOuter2"
          }), (0, import_jsx_runtime50.jsx)("feMergeNode", {
            in: "shadowMatrixOuter3"
          })]
        })]
      })]
    }), (0, import_jsx_runtime51.jsxs)("g", {
      fill: "none",
      fillRule: "evenodd",
      stroke: "none",
      strokeWidth: "1",
      children: [(0, import_jsx_runtime51.jsxs)("g", {
        children: [(0, import_jsx_runtime50.jsx)("use", {
          fill: "#000",
          filter: "url(#filter-2)",
          xlinkHref: "#path-1"
        }), (0, import_jsx_runtime50.jsx)("use", {
          fill: "#F0F2F5",
          xlinkHref: "#path-1"
        })]
      }), (0, import_jsx_runtime50.jsx)("path", {
        fill: "#FFF",
        d: "M26 0h55c5.523 0 10 4.477 10 10v8H26V0z"
      }), (0, import_jsx_runtime50.jsx)("path", {
        fill: "#001529",
        d: "M10 0h19v72H10C4.477 72 0 67.523 0 62V10C0 4.477 4.477 0 10 0z"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "14",
        height: "3",
        x: "5",
        y: "18",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "14",
        height: "3",
        x: "5",
        y: "42",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "9",
        height: "3",
        x: "9",
        y: "24",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "9",
        height: "3",
        x: "9",
        y: "48",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "9",
        height: "3",
        x: "9",
        y: "36",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "14",
        height: "3",
        x: "9",
        y: "30",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      }), (0, import_jsx_runtime50.jsx)("rect", {
        width: "14",
        height: "3",
        x: "9",
        y: "54",
        fill: "#D7DDE6",
        opacity: "0.2",
        rx: "1.5"
      })]
    })]
  });
}

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/style/index.js
init_defineProperty();
var genSettingDrawerStyle = function genSettingDrawerStyle2(token) {
  return _defineProperty(_defineProperty({}, "".concat(token.componentCls, "-handle"), {
    position: "fixed",
    insetBlockStart: "240px",
    insetInlineEnd: "0px",
    zIndex: 0,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "48px",
    height: "48px",
    fontSize: "16px",
    textAlign: "center",
    backgroundColor: token.colorPrimary,
    borderEndStartRadius: token.borderRadiusLG,
    borderStartStartRadius: token.borderRadiusLG,
    "-webkit-backdropilter": "saturate(180%) blur(20px)",
    backdropFilter: "saturate(180%) blur(20px)",
    cursor: "pointer",
    pointerEvents: "auto"
  }), token.componentCls, {
    "&-content": {
      position: "relative",
      minHeight: "100%",
      color: token.colorText
    },
    "&-body-title": {
      marginBlock: token.marginXS,
      fontSize: "14px",
      lineHeight: "22px",
      color: token.colorTextHeading
    },
    "&-block-checkbox": {
      display: "flex",
      minHeight: 42,
      gap: token.marginSM,
      "& &-item": {
        position: "relative",
        width: "44px",
        height: "36px",
        overflow: "hidden",
        borderRadius: "4px",
        boxShadow: token.boxShadow,
        cursor: "pointer",
        fontSize: 56,
        lineHeight: "56px",
        "&::before": {
          position: "absolute",
          insetBlockStart: 0,
          insetInlineStart: 0,
          width: "33%",
          height: "100%",
          content: "''"
        },
        "&::after": {
          position: "absolute",
          insetBlockStart: 0,
          insetInlineStart: 0,
          width: "100%",
          height: "25%",
          content: "''"
        },
        "&-realDark": {
          backgroundColor: "rgba(0, 21, 41, 0.85)",
          "&::before": {
            backgroundColor: "rgba(0, 0, 0, 0.65)"
          },
          "&::after": {
            backgroundColor: "rgba(0, 0, 0, 0.85)"
          }
        },
        "&-light": {
          backgroundColor: "#fff",
          "&::before": {
            backgroundColor: "#fff"
          },
          "&::after": {
            backgroundColor: "#fff"
          }
        },
        "&-dark,&-side": {
          backgroundColor: token.colorBgElevated,
          "&::before": {
            zIndex: "1",
            backgroundColor: "#001529"
          },
          "&::after": {
            backgroundColor: token.colorBgContainer
          }
        },
        "&-top": {
          backgroundColor: token.colorBgElevated,
          "&::before": {
            backgroundColor: "transparent"
          },
          "&::after": {
            backgroundColor: "#001529"
          }
        },
        "&-mix": {
          backgroundColor: token.colorBgElevated,
          "&::before": {
            backgroundColor: token.colorBgContainer
          },
          "&::after": {
            backgroundColor: "#001529"
          }
        }
      },
      "& &-selectIcon": {
        position: "absolute",
        insetInlineEnd: "6px",
        bottom: "4px",
        color: token.colorPrimary,
        fontWeight: "bold",
        fontSize: "14px",
        pointerEvents: "none",
        ".action": {
          color: token.colorPrimary
        }
      },
      "& &-icon": {
        fontSize: 56,
        lineHeight: "56px"
      }
    },
    "&-theme-color": {
      marginBlockStart: "16px",
      overflow: "hidden",
      "& &-block": {
        float: "left",
        width: "20px",
        height: "20px",
        marginBlockStart: 8,
        marginInlineEnd: 8,
        color: "#fff",
        fontWeight: "bold",
        textAlign: "center",
        borderRadius: "2px",
        cursor: "pointer"
      }
    },
    "&-list": _defineProperty({}, "li".concat(token.antCls, "-list-item"), {
      paddingInline: 0,
      paddingBlock: 8
    })
  });
};
function useStyle14(prefixCls) {
  return useStyle("ProLayoutSettingDrawer", function(token) {
    var settingDrawerToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genSettingDrawerStyle(settingDrawerToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js
var import_jsx_runtime52 = __toESM(require_jsx_runtime());
var import_jsx_runtime53 = __toESM(require_jsx_runtime());
var import_jsx_runtime54 = __toESM(require_jsx_runtime());
var Body = function Body2(_ref) {
  var children = _ref.children, hashId = _ref.hashId, prefixCls = _ref.prefixCls, title = _ref.title;
  return (0, import_jsx_runtime53.jsxs)("div", {
    style: {
      marginBlockEnd: 12
    },
    children: [(0, import_jsx_runtime52.jsx)("h3", {
      className: "".concat(prefixCls, "-body-title ").concat(hashId).trim(),
      children: title
    }), children]
  });
};
var getDifferentSetting = function getDifferentSetting2(state) {
  var stateObj = {};
  Object.keys(state).forEach(function(key) {
    if (state[key] !== defaultSettings[key] && //@ts-ignore
    key !== "collapse") {
      stateObj[key] = state[key];
    } else {
      stateObj[key] = void 0;
    }
    if (key.includes("Render"))
      stateObj[key] = state[key] === false ? false : void 0;
  });
  stateObj.menu = void 0;
  return stateObj;
};
var getFormatMessage = function getFormatMessage2() {
  var formatMessage = function formatMessage2(_ref2) {
    var id = _ref2.id;
    var locales2 = gLocaleObject();
    return locales2[id];
  };
  return formatMessage;
};
var initState = function initState2(urlParams, settings, onSettingChange) {
  if (!isBrowser())
    return;
  var replaceSetting = {};
  Object.keys(urlParams).forEach(function(key) {
    if (defaultSettings[key] || defaultSettings[key] === void 0) {
      if (key === "colorPrimary") {
        replaceSetting[key] = genStringToTheme(urlParams[key]);
        return;
      }
      replaceSetting[key] = urlParams[key];
    }
  });
  var newSettings = merge({}, settings, replaceSetting);
  delete newSettings.menu;
  delete newSettings.title;
  delete newSettings.iconfontUrl;
  onSettingChange === null || onSettingChange === void 0 || onSettingChange(newSettings);
};
var getParamsFromUrl = function getParamsFromUrl2(urlParams, settings) {
  if (!isBrowser())
    return defaultSettings;
  return _objectSpread2(_objectSpread2(_objectSpread2({}, defaultSettings), settings || {}), urlParams);
};
var genCopySettingJson = function genCopySettingJson2(settingState) {
  return JSON.stringify(omit(_objectSpread2(_objectSpread2({}, settingState), {}, {
    colorPrimary: settingState.colorPrimary
  }), ["colorWeak"]), null, 2);
};
var SettingDrawer = function SettingDrawer2(props) {
  var _props$defaultSetting = props.defaultSettings, propsDefaultSettings = _props$defaultSetting === void 0 ? void 0 : _props$defaultSetting, _props$settings = props.settings, propsSettings = _props$settings === void 0 ? void 0 : _props$settings, hideHintAlert = props.hideHintAlert, hideCopyButton = props.hideCopyButton, _props$colorList = props.colorList, colorList = _props$colorList === void 0 ? [{
    key: "techBlue",
    color: "#1677FF"
  }, {
    key: "daybreak",
    color: "#1890ff"
  }, {
    key: "dust",
    color: "#F5222D"
  }, {
    key: "volcano",
    color: "#FA541C"
  }, {
    key: "sunset",
    color: "#FAAD14"
  }, {
    key: "cyan",
    color: "#13C2C2"
  }, {
    key: "green",
    color: "#52C41A"
  }, {
    key: "geekblue",
    color: "#2F54EB"
  }, {
    key: "purple",
    color: "#722ED1"
  }] : _props$colorList, getContainer = props.getContainer, onSettingChange = props.onSettingChange, enableDarkTheme = props.enableDarkTheme, _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? "ant-pro" : _props$prefixCls, _props$pathname = props.pathname, pathname = _props$pathname === void 0 ? isBrowser() ? window.location.pathname : "" : _props$pathname, _props$disableUrlPara = props.disableUrlParams, disableUrlParams = _props$disableUrlPara === void 0 ? true : _props$disableUrlPara, themeOnly = props.themeOnly, drawerProps = props.drawerProps;
  var firstRender = (0, import_react25.useRef)(true);
  var _useMergedState = useMergedState(false, {
    value: props.collapse,
    onChange: props.onCollapseChange
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), open = _useMergedState2[0], setOpen = _useMergedState2[1];
  var _useState = (0, import_react25.useState)(getLanguage()), _useState2 = _slicedToArray(_useState, 2), language = _useState2[0], setLanguage = _useState2[1];
  var _useUrlSearchParams = useUrlSearchParams({}, {
    disabled: disableUrlParams
  }), _useUrlSearchParams2 = _slicedToArray(_useUrlSearchParams, 2), urlParams = _useUrlSearchParams2[0], setUrlParams = _useUrlSearchParams2[1];
  var _useMergedState3 = useMergedState(function() {
    return getParamsFromUrl(urlParams, propsSettings || propsDefaultSettings);
  }, {
    value: propsSettings,
    onChange: onSettingChange
  }), _useMergedState4 = _slicedToArray(_useMergedState3, 2), settingState = _useMergedState4[0], setSettingState = _useMergedState4[1];
  var _ref3 = settingState || {}, navTheme = _ref3.navTheme, colorPrimary = _ref3.colorPrimary, siderMenuType = _ref3.siderMenuType, layout = _ref3.layout, colorWeak = _ref3.colorWeak;
  (0, import_react25.useEffect)(function() {
    var onLanguageChange = function onLanguageChange2() {
      if (language !== getLanguage()) {
        setLanguage(getLanguage());
      }
    };
    if (!isBrowser())
      return function() {
        return null;
      };
    initState(getParamsFromUrl(urlParams, propsSettings), settingState, setSettingState);
    window.document.addEventListener("languagechange", onLanguageChange, {
      passive: true
    });
    return function() {
      return window.document.removeEventListener("languagechange", onLanguageChange);
    };
  }, []);
  (0, import_react25.useEffect)(function() {
    if (compareVersions(version_default, "5.0.0") < 0) {
      config_provider_default.config({
        theme: {
          primaryColor: settingState.colorPrimary
        }
      });
    }
  }, [settingState.colorPrimary, settingState.navTheme]);
  var changeSetting = function changeSetting2(key, value) {
    var nextState = {};
    nextState[key] = value;
    if (key === "layout") {
      nextState.contentWidth = value === "top" ? "Fixed" : "Fluid";
    }
    if (key === "layout" && value !== "mix") {
      nextState.splitMenus = false;
    }
    if (key === "layout" && value === "mix") {
      nextState.navTheme = "light";
    }
    if (key === "colorWeak" && value === true) {
      var dom = document.querySelector("body");
      if (dom) {
        dom.dataset.prosettingdrawer = dom.style.filter;
        dom.style.filter = "invert(80%)";
      }
    }
    if (key === "colorWeak" && value === false) {
      var _dom = document.querySelector("body");
      if (_dom) {
        _dom.style.filter = _dom.dataset.prosettingdrawer || "none";
        delete _dom.dataset.prosettingdrawer;
      }
    }
    delete nextState.menu;
    delete nextState.title;
    delete nextState.iconfontUrl;
    delete nextState.logo;
    delete nextState.pwa;
    setSettingState(_objectSpread2(_objectSpread2({}, settingState), nextState));
  };
  var formatMessage = getFormatMessage();
  (0, import_react25.useEffect)(function() {
    if (!isBrowser())
      return;
    if (disableUrlParams)
      return;
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }
    var urlSearchParams = new URLSearchParams(window.location.search);
    var params = Object.fromEntries(urlSearchParams.entries());
    var diffParams = getDifferentSetting(_objectSpread2(_objectSpread2({}, params), settingState));
    delete diffParams.logo;
    delete diffParams.menu;
    delete diffParams.title;
    delete diffParams.iconfontUrl;
    delete diffParams.pwa;
    setUrlParams(diffParams);
  }, [setUrlParams, settingState, urlParams, pathname, disableUrlParams]);
  var baseClassName = "".concat(prefixCls, "-setting-drawer");
  var _useStyle = useStyle14(baseClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var drawerOpenProps = openVisibleCompatible(open);
  return wrapSSR((0, import_jsx_runtime53.jsxs)(import_jsx_runtime54.Fragment, {
    children: [(0, import_jsx_runtime52.jsx)("div", {
      className: "".concat(baseClassName, "-handle ").concat(hashId).trim(),
      onClick: function onClick() {
        return setOpen(!open);
      },
      style: {
        width: 48,
        height: 48
      },
      children: open ? (0, import_jsx_runtime52.jsx)(CloseOutlined_default, {
        style: {
          color: "#fff",
          fontSize: 20
        }
      }) : (0, import_jsx_runtime52.jsx)(SettingOutlined_default, {
        style: {
          color: "#fff",
          fontSize: 20
        }
      })
    }), (0, import_jsx_runtime52.jsx)(drawer_default, _objectSpread2(_objectSpread2(_objectSpread2({}, drawerOpenProps), {}, {
      width: 300,
      onClose: function onClose() {
        return setOpen(false);
      },
      closable: false,
      placement: "right",
      getContainer,
      style: {
        zIndex: 999
      }
    }, drawerProps), {}, {
      children: (0, import_jsx_runtime53.jsxs)("div", {
        className: "".concat(baseClassName, "-drawer-content ").concat(hashId).trim(),
        children: [(0, import_jsx_runtime52.jsx)(Body, {
          title: formatMessage({
            id: "app.setting.pagestyle",
            defaultMessage: "Page style setting"
          }),
          hashId,
          prefixCls: baseClassName,
          children: (0, import_jsx_runtime52.jsx)(BlockCheckbox, {
            hashId,
            prefixCls: baseClassName,
            list: [{
              key: "light",
              title: formatMessage({
                id: "app.setting.pagestyle.light",
                defaultMessage: "亮色菜单风格"
              })
            }, {
              key: "realDark",
              title: formatMessage({
                id: "app.setting.pagestyle.realdark",
                defaultMessage: "暗色菜单风格"
              })
            }].filter(function(item) {
              if (item.key === "dark" && settingState.layout === "mix")
                return false;
              if (item.key === "realDark" && !enableDarkTheme)
                return false;
              return true;
            }),
            value: navTheme,
            configType: "theme",
            onChange: function onChange(value) {
              return changeSetting("navTheme", value);
            }
          }, "navTheme")
        }), colorList !== false && (0, import_jsx_runtime52.jsx)(Body, {
          hashId,
          title: formatMessage({
            id: "app.setting.themecolor",
            defaultMessage: "Theme color"
          }),
          prefixCls: baseClassName,
          children: (0, import_jsx_runtime52.jsx)(ThemeColor, {
            hashId,
            prefixCls: baseClassName,
            colorList,
            value: genStringToTheme(colorPrimary),
            formatMessage,
            onChange: function onChange(color) {
              return changeSetting("colorPrimary", color);
            }
          })
        }), !themeOnly && (0, import_jsx_runtime53.jsxs)(import_jsx_runtime54.Fragment, {
          children: [(0, import_jsx_runtime52.jsx)(divider_default, {}), (0, import_jsx_runtime52.jsx)(Body, {
            hashId,
            prefixCls: baseClassName,
            title: formatMessage({
              id: "app.setting.navigationmode"
            }),
            children: (0, import_jsx_runtime52.jsx)(BlockCheckbox, {
              prefixCls: baseClassName,
              value: layout,
              hashId,
              configType: "layout",
              list: [{
                key: "side",
                title: formatMessage({
                  id: "app.setting.sidemenu"
                })
              }, {
                key: "top",
                title: formatMessage({
                  id: "app.setting.topmenu"
                })
              }, {
                key: "mix",
                title: formatMessage({
                  id: "app.setting.mixmenu"
                })
              }],
              onChange: function onChange(value) {
                return changeSetting("layout", value);
              }
            }, "layout")
          }), settingState.layout == "side" || settingState.layout == "mix" ? (0, import_jsx_runtime52.jsx)(Body, {
            hashId,
            prefixCls: baseClassName,
            title: formatMessage({
              id: "app.setting.sidermenutype"
            }),
            children: (0, import_jsx_runtime52.jsx)(BlockCheckbox, {
              prefixCls: baseClassName,
              value: siderMenuType,
              hashId,
              configType: "siderMenuType",
              list: [{
                key: "sub",
                icon: (0, import_jsx_runtime52.jsx)(SubIcon, {}),
                title: formatMessage({
                  id: "app.setting.sidermenutype-sub"
                })
              }, {
                key: "group",
                icon: (0, import_jsx_runtime52.jsx)(GroupIcon, {}),
                title: formatMessage({
                  id: "app.setting.sidermenutype-group"
                })
              }],
              onChange: function onChange(value) {
                return changeSetting("siderMenuType", value);
              }
            }, "siderMenuType")
          }) : null, (0, import_jsx_runtime52.jsx)(LayoutSetting, {
            prefixCls: baseClassName,
            hashId,
            settings: settingState,
            changeSetting
          }), (0, import_jsx_runtime52.jsx)(divider_default, {}), (0, import_jsx_runtime52.jsx)(Body, {
            hashId,
            prefixCls: baseClassName,
            title: formatMessage({
              id: "app.setting.regionalsettings"
            }),
            children: (0, import_jsx_runtime52.jsx)(RegionalSetting, {
              hashId,
              prefixCls: baseClassName,
              settings: settingState,
              changeSetting
            })
          }), (0, import_jsx_runtime52.jsx)(divider_default, {}), (0, import_jsx_runtime52.jsx)(Body, {
            hashId,
            prefixCls: baseClassName,
            title: formatMessage({
              id: "app.setting.othersettings"
            }),
            children: (0, import_jsx_runtime52.jsx)(list_default, {
              className: "".concat(baseClassName, "-list ").concat(hashId).trim(),
              split: false,
              size: "small",
              renderItem: renderLayoutSettingItem,
              dataSource: [{
                title: formatMessage({
                  id: "app.setting.weakmode"
                }),
                action: (0, import_jsx_runtime52.jsx)(switch_default, {
                  size: "small",
                  className: "color-weak",
                  checked: !!colorWeak,
                  onChange: function onChange(checked) {
                    changeSetting("colorWeak", checked);
                  }
                })
              }]
            })
          }), hideHintAlert && hideCopyButton ? null : (0, import_jsx_runtime52.jsx)(divider_default, {}), hideHintAlert ? null : (0, import_jsx_runtime52.jsx)(alert_default, {
            type: "warning",
            message: formatMessage({
              id: "app.setting.production.hint"
            }),
            icon: (0, import_jsx_runtime52.jsx)(NotificationOutlined_default, {}),
            showIcon: true,
            style: {
              marginBlockEnd: 16
            }
          }), hideCopyButton ? null : (0, import_jsx_runtime52.jsx)(button_default, {
            block: true,
            icon: (0, import_jsx_runtime52.jsx)(CopyOutlined_default, {}),
            style: {
              marginBlockEnd: 24
            },
            onClick: _asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
              return _regeneratorRuntime().wrap(function _callee$(_context) {
                while (1)
                  switch (_context.prev = _context.next) {
                    case 0:
                      _context.prev = 0;
                      _context.next = 3;
                      return navigator.clipboard.writeText(genCopySettingJson(settingState));
                    case 3:
                      message_default.success(formatMessage({
                        id: "app.setting.copyinfo"
                      }));
                      _context.next = 8;
                      break;
                    case 6:
                      _context.prev = 6;
                      _context.t0 = _context["catch"](0);
                    case 8:
                    case "end":
                      return _context.stop();
                  }
              }, _callee, null, [[0, 6]]);
            })),
            children: formatMessage({
              id: "app.setting.copy"
            })
          })]
        })]
      })
    }))]
  }));
};

// node_modules/@ant-design/pro-layout/es/getPageTitle.js
var import_path_to_regexp = __toESM(require_dist());
var matchParamsPath = function matchParamsPath2(pathname, breadcrumb, breadcrumbMap) {
  if (breadcrumbMap) {
    var pathKey = _toConsumableArray(breadcrumbMap.keys()).find(function(key) {
      try {
        if (key.startsWith("http")) {
          return false;
        }
        return (0, import_path_to_regexp.match)(key)(pathname);
      } catch (error) {
        console.log("key", key, error);
        return false;
      }
    });
    if (pathKey) {
      return breadcrumbMap.get(pathKey);
    }
  }
  if (breadcrumb) {
    var _pathKey = Object.keys(breadcrumb).find(function(key) {
      try {
        if (key !== null && key !== void 0 && key.startsWith("http")) {
          return false;
        }
        return (0, import_path_to_regexp.match)(key)(pathname);
      } catch (error) {
        console.log("key", key, error);
        return false;
      }
    });
    if (_pathKey) {
      return breadcrumb[_pathKey];
    }
  }
  return {
    path: ""
  };
};
var getPageTitleInfo = function getPageTitleInfo2(props, ignoreTitle) {
  var _props$pathname = props.pathname, pathname = _props$pathname === void 0 ? "/" : _props$pathname, breadcrumb = props.breadcrumb, breadcrumbMap = props.breadcrumbMap, formatMessage = props.formatMessage, title = props.title, _props$menu = props.menu, menu = _props$menu === void 0 ? {
    locale: false
  } : _props$menu;
  var pageTitle = ignoreTitle ? "" : title || "";
  var currRouterData = matchParamsPath(pathname, breadcrumb, breadcrumbMap);
  if (!currRouterData) {
    return {
      title: pageTitle,
      id: "",
      pageName: pageTitle
    };
  }
  var pageName = currRouterData.name;
  if (menu.locale !== false && currRouterData.locale && formatMessage) {
    pageName = formatMessage({
      id: currRouterData.locale || "",
      defaultMessage: currRouterData.name
    });
  }
  if (!pageName) {
    return {
      title: pageTitle,
      id: currRouterData.locale || "",
      pageName: pageTitle
    };
  }
  if (ignoreTitle || !title) {
    return {
      title: pageName,
      id: currRouterData.locale || "",
      pageName
    };
  }
  return {
    title: "".concat(pageName, " - ").concat(title),
    id: currRouterData.locale || "",
    pageName
  };
};
var getPageTitle = function getPageTitle2(props, ignoreTitle) {
  return getPageTitleInfo(props, ignoreTitle).title;
};

// node_modules/@ant-design/pro-layout/es/ProLayout.js
init_defineProperty();

// node_modules/@umijs/route-utils/es/transformRoute/transformRoute.js
var import_path_to_regexp2 = __toESM(require_path_to_regexp());

// node_modules/@umijs/route-utils/es/sha265.js
function rotateRight(n, x) {
  return x >>> n | x << 32 - n;
}
function choice(x, y, z) {
  return x & y ^ ~x & z;
}
function majority(x, y, z) {
  return x & y ^ x & z ^ y & z;
}
function sha256_Sigma0(x) {
  return rotateRight(2, x) ^ rotateRight(13, x) ^ rotateRight(22, x);
}
function sha256_Sigma1(x) {
  return rotateRight(6, x) ^ rotateRight(11, x) ^ rotateRight(25, x);
}
function sha256_sigma0(x) {
  return rotateRight(7, x) ^ rotateRight(18, x) ^ x >>> 3;
}
function sha256_sigma1(x) {
  return rotateRight(17, x) ^ rotateRight(19, x) ^ x >>> 10;
}
function sha256_expand(W, j) {
  return W[j & 15] += sha256_sigma1(W[j + 14 & 15]) + W[j + 9 & 15] + sha256_sigma0(W[j + 1 & 15]);
}
var K256 = [1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993, 2453635748, 2870763221, 3624381080, 310598401, 607225278, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298];
var ihash;
var count;
var buffer;
var sha256_hex_digits = "0123456789abcdef";
function safe_add(x, y) {
  var lsw = (x & 65535) + (y & 65535);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return msw << 16 | lsw & 65535;
}
function sha256_init() {
  ihash = new Array(8);
  count = new Array(2);
  buffer = new Array(64);
  count[0] = count[1] = 0;
  ihash[0] = 1779033703;
  ihash[1] = 3144134277;
  ihash[2] = 1013904242;
  ihash[3] = 2773480762;
  ihash[4] = 1359893119;
  ihash[5] = 2600822924;
  ihash[6] = 528734635;
  ihash[7] = 1541459225;
}
function sha256_transform() {
  var a;
  var b;
  var c;
  var d;
  var e;
  var f;
  var g;
  var h;
  var T1;
  var T2;
  var W = new Array(16);
  a = ihash[0];
  b = ihash[1];
  c = ihash[2];
  d = ihash[3];
  e = ihash[4];
  f = ihash[5];
  g = ihash[6];
  h = ihash[7];
  for (var i = 0; i < 16; i++) {
    W[i] = buffer[(i << 2) + 3] | buffer[(i << 2) + 2] << 8 | buffer[(i << 2) + 1] << 16 | buffer[i << 2] << 24;
  }
  for (var j = 0; j < 64; j++) {
    T1 = h + sha256_Sigma1(e) + choice(e, f, g) + K256[j];
    if (j < 16)
      T1 += W[j];
    else
      T1 += sha256_expand(W, j);
    T2 = sha256_Sigma0(a) + majority(a, b, c);
    h = g;
    g = f;
    f = e;
    e = safe_add(d, T1);
    d = c;
    c = b;
    b = a;
    a = safe_add(T1, T2);
  }
  ihash[0] += a;
  ihash[1] += b;
  ihash[2] += c;
  ihash[3] += d;
  ihash[4] += e;
  ihash[5] += f;
  ihash[6] += g;
  ihash[7] += h;
}
function sha256_update(data, inputLen) {
  var i;
  var index;
  var curpos = 0;
  index = count[0] >> 3 & 63;
  var remainder = inputLen & 63;
  if ((count[0] += inputLen << 3) < inputLen << 3)
    count[1]++;
  count[1] += inputLen >> 29;
  for (i = 0; i + 63 < inputLen; i += 64) {
    for (var j = index; j < 64; j++) {
      buffer[j] = data.charCodeAt(curpos++);
    }
    sha256_transform();
    index = 0;
  }
  for (var _j = 0; _j < remainder; _j++) {
    buffer[_j] = data.charCodeAt(curpos++);
  }
}
function sha256_final() {
  var index = count[0] >> 3 & 63;
  buffer[index++] = 128;
  if (index <= 56) {
    for (var i = index; i < 56; i++) {
      buffer[i] = 0;
    }
  } else {
    for (var _i = index; _i < 64; _i++) {
      buffer[_i] = 0;
    }
    sha256_transform();
    for (var _i2 = 0; _i2 < 56; _i2++) {
      buffer[_i2] = 0;
    }
  }
  buffer[56] = count[1] >>> 24 & 255;
  buffer[57] = count[1] >>> 16 & 255;
  buffer[58] = count[1] >>> 8 & 255;
  buffer[59] = count[1] & 255;
  buffer[60] = count[0] >>> 24 & 255;
  buffer[61] = count[0] >>> 16 & 255;
  buffer[62] = count[0] >>> 8 & 255;
  buffer[63] = count[0] & 255;
  sha256_transform();
}
function sha256_encode_hex() {
  var output = new String();
  for (var i = 0; i < 8; i++) {
    for (var j = 28; j >= 0; j -= 4) {
      output += sha256_hex_digits.charAt(ihash[i] >>> j & 15);
    }
  }
  return output;
}
function digest(data) {
  sha256_init();
  sha256_update(data, data.length);
  sha256_final();
  return sha256_encode_hex();
}
var sha265_default = digest;

// node_modules/@umijs/route-utils/es/transformRoute/transformRoute.js
function _typeof2(obj) {
  "@babel/helpers - typeof";
  return _typeof2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj2) {
    return typeof obj2;
  } : function(obj2) {
    return obj2 && "function" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
  }, _typeof2(obj);
}
var _excluded8 = ["pro_layout_parentKeys", "children", "icon", "flatMenu", "indexRoute", "routes"];
function _slicedToArray2(arr, i) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
}
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _iterableToArrayLimit(arr, i) {
  var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
  if (_i == null)
    return;
  var _arr = [];
  var _n = true;
  var _d = false;
  var _s, _e;
  try {
    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {
      _arr.push(_s.value);
      if (i && _arr.length === i)
        break;
    }
  } catch (err) {
    _d = true;
    _e = err;
  } finally {
    try {
      if (!_n && _i["return"] != null)
        _i["return"]();
    } finally {
      if (_d)
        throw _e;
    }
  }
  return _arr;
}
function _arrayWithHoles(arr) {
  if (Array.isArray(arr))
    return arr;
}
function _createForOfIteratorHelper(o, allowArrayLike) {
  var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
  if (!it) {
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
      if (it)
        o = it;
      var i = 0;
      var F = function F2() {
      };
      return { s: F, n: function n() {
        if (i >= o.length)
          return { done: true };
        return { done: false, value: o[i++] };
      }, e: function e(_e2) {
        throw _e2;
      }, f: F };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var normalCompletion = true, didErr = false, err;
  return { s: function s() {
    it = it.call(o);
  }, n: function n() {
    var step = it.next();
    normalCompletion = step.done;
    return step;
  }, e: function e(_e3) {
    didErr = true;
    err = _e3;
  }, f: function f() {
    try {
      if (!normalCompletion && it.return != null)
        it.return();
    } finally {
      if (didErr)
        throw err;
    }
  } };
}
function _classCallCheck2(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor)
      descriptor.writable = true;
    Object.defineProperty(target, descriptor.key, descriptor);
  }
}
function _createClass2(Constructor, protoProps, staticProps) {
  if (protoProps)
    _defineProperties(Constructor.prototype, protoProps);
  if (staticProps)
    _defineProperties(Constructor, staticProps);
  Object.defineProperty(Constructor, "prototype", { writable: false });
  return Constructor;
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } });
  Object.defineProperty(subClass, "prototype", { writable: false });
  if (superClass)
    _setPrototypeOf(subClass, superClass);
}
function _createSuper(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived), result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
function _possibleConstructorReturn(self, call) {
  if (call && (_typeof2(call) === "object" || typeof call === "function")) {
    return call;
  } else if (call !== void 0) {
    throw new TypeError("Derived constructors may only return object or undefined");
  }
  return _assertThisInitialized(self);
}
function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self;
}
function _wrapNativeSuper(Class) {
  var _cache = typeof Map === "function" ? /* @__PURE__ */ new Map() : void 0;
  _wrapNativeSuper = function _wrapNativeSuper2(Class2) {
    if (Class2 === null || !_isNativeFunction(Class2))
      return Class2;
    if (typeof Class2 !== "function") {
      throw new TypeError("Super expression must either be null or a function");
    }
    if (typeof _cache !== "undefined") {
      if (_cache.has(Class2))
        return _cache.get(Class2);
      _cache.set(Class2, Wrapper);
    }
    function Wrapper() {
      return _construct(Class2, arguments, _getPrototypeOf(this).constructor);
    }
    Wrapper.prototype = Object.create(Class2.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } });
    return _setPrototypeOf(Wrapper, Class2);
  };
  return _wrapNativeSuper(Class);
}
function _construct(Parent, args, Class) {
  if (_isNativeReflectConstruct()) {
    _construct = Reflect.construct.bind();
  } else {
    _construct = function _construct2(Parent2, args2, Class2) {
      var a = [null];
      a.push.apply(a, args2);
      var Constructor = Function.bind.apply(Parent2, a);
      var instance = new Constructor();
      if (Class2)
        _setPrototypeOf(instance, Class2.prototype);
      return instance;
    };
  }
  return _construct.apply(null, arguments);
}
function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct)
    return false;
  if (Reflect.construct.sham)
    return false;
  if (typeof Proxy === "function")
    return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
function _isNativeFunction(fn) {
  return Function.toString.call(fn).indexOf("[native code]") !== -1;
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _toConsumableArray2(arr) {
  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
  if (!o)
    return;
  if (typeof o === "string")
    return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor)
    n = o.constructor.name;
  if (n === "Map" || n === "Set")
    return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
    return _arrayLikeToArray(o, minLen);
}
function _iterableToArray(iter) {
  if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null)
    return Array.from(iter);
}
function _arrayWithoutHoles(arr) {
  if (Array.isArray(arr))
    return _arrayLikeToArray(arr);
}
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length)
    len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) {
    arr2[i] = arr[i];
  }
  return arr2;
}
function _objectWithoutProperties2(source, excluded) {
  if (source == null)
    return {};
  var target = _objectWithoutPropertiesLoose(source, excluded);
  var key, i;
  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0)
        continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key))
        continue;
      target[key] = source[key];
    }
  }
  return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null)
    return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;
  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0)
      continue;
    target[key] = source[key];
  }
  return target;
}
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty2(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _defineProperty2(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var childrenPropsName = "routes";
function stripQueryStringAndHashFromPath(url) {
  return url.split("?")[0].split("#")[0];
}
var isUrl2 = function isUrl3(path) {
  if (!path.startsWith("http")) {
    return false;
  }
  try {
    var url = new URL(path);
    return !!url;
  } catch (error) {
    return false;
  }
};
var getKeyByPath = function getKeyByPath2(item) {
  var path = item.path;
  if (!path || path === "/") {
    try {
      return "/".concat(sha265_default(JSON.stringify(item)));
    } catch (error) {
    }
  }
  return path ? stripQueryStringAndHashFromPath(path) : path;
};
var getItemLocaleName = function getItemLocaleName2(item, parentName) {
  var name = item.name, locale = item.locale;
  if ("locale" in item && locale === false || !name) {
    return false;
  }
  return item.locale || "".concat(parentName, ".").concat(name);
};
var mergePath = function mergePath2() {
  var path = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
  var parentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "/";
  if (path.endsWith("/*")) {
    return path.replace("/*", "/");
  }
  if ((path || parentPath).startsWith("/")) {
    return path;
  }
  if (isUrl2(path)) {
    return path;
  }
  return "/".concat(parentPath, "/").concat(path).replace(/\/\//g, "/").replace(/\/\//g, "/");
};
var bigfishCompatibleConversions = function bigfishCompatibleConversions2(route, props) {
  var _route$menu = route.menu, menu = _route$menu === void 0 ? {} : _route$menu, indexRoute = route.indexRoute, _route$path = route.path, path = _route$path === void 0 ? "" : _route$path;
  var routerChildren = route.children || [];
  var _menu$name = menu.name, name = _menu$name === void 0 ? route.name : _menu$name, _menu$icon = menu.icon, icon = _menu$icon === void 0 ? route.icon : _menu$icon, _menu$hideChildren = menu.hideChildren, hideChildren = _menu$hideChildren === void 0 ? route.hideChildren : _menu$hideChildren, _menu$flatMenu = menu.flatMenu, flatMenu = _menu$flatMenu === void 0 ? route.flatMenu : _menu$flatMenu;
  var childrenList = indexRoute && // 如果只有 redirect,不用处理的
  Object.keys(indexRoute).join(",") !== "redirect" ? [_objectSpread({
    path,
    menu
  }, indexRoute)].concat(routerChildren || []) : routerChildren;
  var result = _objectSpread({}, route);
  if (name) {
    result.name = name;
  }
  if (icon) {
    result.icon = icon;
  }
  if (childrenList && childrenList.length) {
    if (hideChildren) {
      delete result.children;
      return result;
    }
    var finalChildren = formatter(_objectSpread(_objectSpread({}, props), {}, {
      data: childrenList
    }), route);
    if (flatMenu) {
      return finalChildren;
    }
    delete result[childrenPropsName];
  }
  return result;
};
var notNullArray = function notNullArray2(value) {
  return Array.isArray(value) && value.length > 0;
};
function formatter(props) {
  var parent = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
    path: "/"
  };
  var data = props.data, formatMessage = props.formatMessage, parentName = props.parentName, menuLocale = props.locale;
  if (!data || !Array.isArray(data)) {
    return [];
  }
  return data.filter(function(item) {
    if (!item)
      return false;
    if (notNullArray(item.children))
      return true;
    if (item.path)
      return true;
    if (item.originPath)
      return true;
    if (item.layout)
      return true;
    if (item.redirect)
      return false;
    if (item.unaccessible)
      return false;
    return false;
  }).filter(function(item) {
    var _item$menu, _item$menu2;
    if ((item === null || item === void 0 ? void 0 : (_item$menu = item.menu) === null || _item$menu === void 0 ? void 0 : _item$menu.name) || (item === null || item === void 0 ? void 0 : item.flatMenu) || (item === null || item === void 0 ? void 0 : (_item$menu2 = item.menu) === null || _item$menu2 === void 0 ? void 0 : _item$menu2.flatMenu)) {
      return true;
    }
    if (item.menu === false) {
      return false;
    }
    return true;
  }).map(function(finallyItem) {
    var item = _objectSpread(_objectSpread({}, finallyItem), {}, {
      path: finallyItem.path || finallyItem.originPath
    });
    if (!item.children && item[childrenPropsName]) {
      item.children = item[childrenPropsName];
      delete item[childrenPropsName];
    }
    if (item.unaccessible) {
      delete item.name;
    }
    if (item.path === "*") {
      item.path = ".";
    }
    if (item.path === "/*") {
      item.path = ".";
    }
    if (!item.path && item.originPath) {
      item.path = item.originPath;
    }
    return item;
  }).map(function() {
    var item = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {
      path: "/"
    };
    var routerChildren = item.children || item[childrenPropsName] || [];
    var path = mergePath(item.path, parent ? parent.path : "/");
    var name = item.name;
    var locale = getItemLocaleName(item, parentName || "menu");
    var localeName = locale !== false && menuLocale !== false && formatMessage && locale ? formatMessage({
      id: locale,
      defaultMessage: name
    }) : name;
    var _parent$pro_layout_pa = parent.pro_layout_parentKeys, pro_layout_parentKeys = _parent$pro_layout_pa === void 0 ? [] : _parent$pro_layout_pa, children = parent.children, icon = parent.icon, flatMenu = parent.flatMenu, indexRoute = parent.indexRoute, routes = parent.routes, restParent = _objectWithoutProperties2(parent, _excluded8);
    var item_pro_layout_parentKeys = new Set([].concat(_toConsumableArray2(pro_layout_parentKeys), _toConsumableArray2(item.parentKeys || [])));
    if (parent.key) {
      item_pro_layout_parentKeys.add(parent.key);
    }
    var finallyItem = _objectSpread(_objectSpread(_objectSpread({}, restParent), {}, {
      menu: void 0
    }, item), {}, {
      path,
      locale,
      key: item.key || getKeyByPath(_objectSpread(_objectSpread({}, item), {}, {
        path
      })),
      pro_layout_parentKeys: Array.from(item_pro_layout_parentKeys).filter(function(key) {
        return key && key !== "/";
      })
    });
    if (localeName) {
      finallyItem.name = localeName;
    } else {
      delete finallyItem.name;
    }
    if (finallyItem.menu === void 0) {
      delete finallyItem.menu;
    }
    if (notNullArray(routerChildren)) {
      var formatterChildren = formatter(_objectSpread(_objectSpread({}, props), {}, {
        data: routerChildren,
        parentName: locale || ""
      }), finallyItem);
      if (notNullArray(formatterChildren)) {
        finallyItem.children = formatterChildren;
      }
    }
    return bigfishCompatibleConversions(finallyItem, props);
  }).flat(1);
}
var defaultFilterMenuData = function defaultFilterMenuData2() {
  var menuData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  return menuData.filter(function(item) {
    return item && (item.name || notNullArray(item.children)) && !item.hideInMenu && !item.redirect;
  }).map(function(item) {
    var newItem = _objectSpread({}, item);
    var routerChildren = newItem.children || item[childrenPropsName] || [];
    delete newItem[childrenPropsName];
    if (notNullArray(routerChildren) && !newItem.hideChildrenInMenu && routerChildren.some(function(child) {
      return child && !!child.name;
    })) {
      var newChildren = defaultFilterMenuData2(routerChildren);
      if (newChildren.length)
        return _objectSpread(_objectSpread({}, newItem), {}, {
          children: newChildren
        });
    }
    return _objectSpread({}, item);
  }).filter(function(item) {
    return item;
  });
};
var RouteListMap = function(_Map) {
  _inherits(RouteListMap2, _Map);
  var _super = _createSuper(RouteListMap2);
  function RouteListMap2() {
    _classCallCheck2(this, RouteListMap2);
    return _super.apply(this, arguments);
  }
  _createClass2(RouteListMap2, [{
    key: "get",
    value: function get(pathname) {
      var routeValue;
      try {
        var _iterator = _createForOfIteratorHelper(this.entries()), _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done; ) {
            var _step$value = _slicedToArray2(_step.value, 2), key = _step$value[0], value = _step$value[1];
            var path = stripQueryStringAndHashFromPath(key);
            if (!isUrl2(key) && (0, import_path_to_regexp2.pathToRegexp)(path, []).test(pathname)) {
              routeValue = value;
              break;
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      } catch (error) {
        routeValue = void 0;
      }
      return routeValue;
    }
  }]);
  return RouteListMap2;
}(_wrapNativeSuper(Map));
var getBreadcrumbNameMap = function getBreadcrumbNameMap2(menuData) {
  var routerMap = new RouteListMap();
  var flattenMenuData = function flattenMenuData2(data, parent) {
    data.forEach(function(menuItem) {
      var routerChildren = menuItem.children || menuItem[childrenPropsName] || [];
      if (notNullArray(routerChildren)) {
        flattenMenuData2(routerChildren, menuItem);
      }
      var path = mergePath(menuItem.path, parent ? parent.path : "/");
      routerMap.set(stripQueryStringAndHashFromPath(path), menuItem);
    });
  };
  flattenMenuData(menuData);
  return routerMap;
};
var clearChildren = function clearChildren2() {
  var menuData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  return menuData.map(function(item) {
    var routerChildren = item.children || item[childrenPropsName];
    if (notNullArray(routerChildren)) {
      var newChildren = clearChildren2(routerChildren);
      if (newChildren.length)
        return _objectSpread({}, item);
    }
    var finallyItem = _objectSpread({}, item);
    delete finallyItem[childrenPropsName];
    delete finallyItem.children;
    return finallyItem;
  }).filter(function(item) {
    return item;
  });
};
var transformRoute = function transformRoute2(routeList, locale, formatMessage, ignoreFilter) {
  var originalMenuData = formatter({
    data: routeList,
    formatMessage,
    locale
  });
  var menuData = ignoreFilter ? clearChildren(originalMenuData) : defaultFilterMenuData(originalMenuData);
  var breadcrumb = getBreadcrumbNameMap(originalMenuData);
  return {
    breadcrumb,
    menuData
  };
};
var transformRoute_default = transformRoute;

// node_modules/@umijs/route-utils/es/getFlatMenus/getFlatMenus.js
function ownKeys2(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread3(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys2(Object(source), true).forEach(function(key) {
      _defineProperty3(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys2(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _defineProperty3(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var getFlatMenus = function getFlatMenus2() {
  var menuData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  var menus = {};
  menuData.forEach(function(mapItem) {
    var item = _objectSpread3({}, mapItem);
    if (!item || !item.key) {
      return;
    }
    if (!item.children && item[childrenPropsName]) {
      item.children = item[childrenPropsName];
      delete item[childrenPropsName];
    }
    var routerChildren = item.children || [];
    menus[stripQueryStringAndHashFromPath(item.path || item.key || "/")] = _objectSpread3({}, item);
    menus[item.key || item.path || "/"] = _objectSpread3({}, item);
    if (routerChildren) {
      menus = _objectSpread3(_objectSpread3({}, menus), getFlatMenus2(routerChildren));
    }
  });
  return menus;
};
var getFlatMenus_default = getFlatMenus;

// node_modules/@umijs/route-utils/es/getMatchMenu/getMatchMenu.js
var import_path_to_regexp3 = __toESM(require_path_to_regexp());
var getMenuMatches = function getMenuMatches2() {
  var flatMenuKeys = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  var path = arguments.length > 1 ? arguments[1] : void 0;
  var exact = arguments.length > 2 ? arguments[2] : void 0;
  return flatMenuKeys.filter(function(item) {
    if (item === "/" && path === "/") {
      return true;
    }
    if (item !== "/" && item !== "/*" && item && !isUrl2(item)) {
      var pathKey = stripQueryStringAndHashFromPath(item);
      try {
        if (exact) {
          if ((0, import_path_to_regexp3.pathToRegexp)("".concat(pathKey)).test(path)) {
            return true;
          }
        }
        if ((0, import_path_to_regexp3.pathToRegexp)("".concat(pathKey), []).test(path)) {
          return true;
        }
        if ((0, import_path_to_regexp3.pathToRegexp)("".concat(pathKey, "/(.*)")).test(path)) {
          return true;
        }
      } catch (error) {
      }
    }
    return false;
  }).sort(function(a, b) {
    if (a === path) {
      return 10;
    }
    if (b === path) {
      return -10;
    }
    return a.substr(1).split("/").length - b.substr(1).split("/").length;
  });
};
var getMatchMenu = function getMatchMenu2(pathname, menuData, fullKeys, exact) {
  var flatMenus = getFlatMenus_default(menuData);
  var flatMenuKeys = Object.keys(flatMenus);
  var menuPathKeys = getMenuMatches(flatMenuKeys, pathname || "/", exact);
  if (!menuPathKeys || menuPathKeys.length < 1) {
    return [];
  }
  if (!fullKeys) {
    menuPathKeys = [menuPathKeys[menuPathKeys.length - 1]];
  }
  return menuPathKeys.map(function(menuPathKey) {
    var menuItem = flatMenus[menuPathKey] || {
      pro_layout_parentKeys: "",
      key: ""
    };
    var map = /* @__PURE__ */ new Map();
    var parentItems = (menuItem.pro_layout_parentKeys || []).map(function(key) {
      if (map.has(key)) {
        return null;
      }
      map.set(key, true);
      return flatMenus[key];
    }).filter(function(item) {
      return item;
    });
    if (menuItem.key) {
      parentItems.push(menuItem);
    }
    return parentItems;
  }).flat(1);
};
var getMatchMenu_default = getMatchMenu;

// node_modules/@ant-design/pro-layout/es/ProLayout.js
var import_classnames18 = __toESM(require_classnames());
var import_react29 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/WrapContent.js
init_defineProperty();
var import_classnames16 = __toESM(require_classnames());
var import_react26 = __toESM(require_react());
var import_jsx_runtime55 = __toESM(require_jsx_runtime());
var WrapContent = function WrapContent2(props) {
  var _useContext = (0, import_react26.useContext)(ProProvider), hashId = _useContext.hashId;
  var style = props.style, prefixCls = props.prefixCls, children = props.children, _props$hasPageContain = props.hasPageContainer, hasPageContainer = _props$hasPageContain === void 0 ? 0 : _props$hasPageContain;
  var contentClassName = (0, import_classnames16.default)("".concat(prefixCls, "-content"), hashId, _defineProperty(_defineProperty({}, "".concat(prefixCls, "-has-header"), props.hasHeader), "".concat(prefixCls, "-content-has-page-container"), hasPageContainer > 0));
  var ErrorComponent = props.ErrorBoundary || ErrorBoundary;
  return props.ErrorBoundary === false ? (0, import_jsx_runtime55.jsx)(layout_default.Content, {
    className: contentClassName,
    style,
    children
  }) : (0, import_jsx_runtime55.jsx)(ErrorComponent, {
    children: (0, import_jsx_runtime55.jsx)(layout_default.Content, {
      className: contentClassName,
      style,
      children
    })
  });
};

// node_modules/@ant-design/pro-layout/es/assert/Logo.js
var import_jsx_runtime56 = __toESM(require_jsx_runtime());
var import_jsx_runtime57 = __toESM(require_jsx_runtime());
var Logo = function Logo2() {
  return (0, import_jsx_runtime57.jsxs)("svg", {
    width: "1em",
    height: "1em",
    viewBox: "0 0 200 200",
    children: [(0, import_jsx_runtime57.jsxs)("defs", {
      children: [(0, import_jsx_runtime57.jsxs)("linearGradient", {
        x1: "62.1023273%",
        y1: "0%",
        x2: "108.19718%",
        y2: "37.8635764%",
        id: "linearGradient-1",
        children: [(0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#4285EB",
          offset: "0%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#2EC7FF",
          offset: "100%"
        })]
      }), (0, import_jsx_runtime57.jsxs)("linearGradient", {
        x1: "69.644116%",
        y1: "0%",
        x2: "54.0428975%",
        y2: "108.456714%",
        id: "linearGradient-2",
        children: [(0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#29CDFF",
          offset: "0%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#148EFF",
          offset: "37.8600687%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#0A60FF",
          offset: "100%"
        })]
      }), (0, import_jsx_runtime57.jsxs)("linearGradient", {
        x1: "69.6908165%",
        y1: "-12.9743587%",
        x2: "16.7228981%",
        y2: "117.391248%",
        id: "linearGradient-3",
        children: [(0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#FA816E",
          offset: "0%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#F74A5C",
          offset: "41.472606%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#F51D2C",
          offset: "100%"
        })]
      }), (0, import_jsx_runtime57.jsxs)("linearGradient", {
        x1: "68.1279872%",
        y1: "-35.6905737%",
        x2: "30.4400914%",
        y2: "114.942679%",
        id: "linearGradient-4",
        children: [(0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#FA8E7D",
          offset: "0%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#F74A5C",
          offset: "51.2635191%"
        }), (0, import_jsx_runtime56.jsx)("stop", {
          stopColor: "#F51D2C",
          offset: "100%"
        })]
      })]
    }), (0, import_jsx_runtime56.jsx)("g", {
      stroke: "none",
      strokeWidth: 1,
      fill: "none",
      fillRule: "evenodd",
      children: (0, import_jsx_runtime56.jsx)("g", {
        transform: "translate(-20.000000, -20.000000)",
        children: (0, import_jsx_runtime56.jsx)("g", {
          transform: "translate(20.000000, 20.000000)",
          children: (0, import_jsx_runtime57.jsxs)("g", {
            children: [(0, import_jsx_runtime57.jsxs)("g", {
              fillRule: "nonzero",
              children: [(0, import_jsx_runtime57.jsxs)("g", {
                children: [(0, import_jsx_runtime56.jsx)("path", {
                  d: "M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",
                  fill: "url(#linearGradient-1)"
                }), (0, import_jsx_runtime56.jsx)("path", {
                  d: "M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",
                  fill: "url(#linearGradient-2)"
                })]
              }), (0, import_jsx_runtime56.jsx)("path", {
                d: "M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",
                fill: "url(#linearGradient-3)"
              })]
            }), (0, import_jsx_runtime56.jsx)("ellipse", {
              fill: "url(#linearGradient-4)",
              cx: "100.519339",
              cy: "100.436681",
              rx: "23.6001926",
              ry: "23.580786"
            })]
          })
        })
      })
    })]
  });
};

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js
var import_classnames17 = __toESM(require_classnames());
var import_react27 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/index.js
init_defineProperty();
var proLayoutTitleHide = new Keyframes_default("antBadgeLoadingCircle", {
  "0%": {
    display: "none",
    opacity: 0,
    overflow: "hidden"
  },
  "80%": {
    overflow: "hidden"
  },
  "100%": {
    display: "unset",
    opacity: 1
  }
});
var genSiderMenuStyle3 = function genSiderMenuStyle4(token) {
  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12;
  return _defineProperty({}, "".concat(token.proComponentsCls, "-layout"), _defineProperty(_defineProperty(_defineProperty({}, "".concat(token.antCls, "-layout-sider").concat(token.componentCls), {
    background: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorMenuBackground) || "transparent"
  }), token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    position: "relative",
    boxSizing: "border-box",
    "&-menu": {
      position: "relative",
      zIndex: 10,
      minHeight: "100%"
    }
  }, "& ".concat(token.antCls, "-layout-sider-children"), {
    position: "relative",
    display: "flex",
    flexDirection: "column",
    height: "100%",
    paddingInline: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.paddingInlineLayoutMenu,
    paddingBlock: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.paddingBlockLayoutMenu,
    borderInlineEnd: "1px solid ".concat(token.colorSplit),
    marginInlineEnd: -1
  }), "".concat(token.antCls, "-menu"), _defineProperty(_defineProperty({}, "".concat(token.antCls, "-menu-item-group-title"), {
    fontSize: token.fontSizeSM,
    paddingBottom: 4
  }), "".concat(token.antCls, "-menu-item:not(").concat(token.antCls, "-menu-item-selected):hover"), {
    color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuItemHover
  })), "&-logo", {
    position: "relative",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    paddingInline: 12,
    paddingBlock: 16,
    color: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenu,
    cursor: "pointer",
    borderBlockEnd: "1px solid ".concat((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorMenuItemDivider),
    "> a": {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      minHeight: 22,
      fontSize: 22,
      "> img": {
        display: "inline-block",
        height: 22,
        verticalAlign: "middle"
      },
      "> h1": {
        display: "inline-block",
        height: 22,
        marginBlock: 0,
        marginInlineEnd: 0,
        marginInlineStart: 6,
        color: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuTitle,
        animationName: proLayoutTitleHide,
        animationDuration: ".4s",
        animationTimingFunction: "ease",
        fontWeight: 600,
        fontSize: 16,
        lineHeight: "22px",
        verticalAlign: "middle"
      }
    },
    "&-collapsed": _defineProperty({
      flexDirection: "column-reverse",
      margin: 0,
      padding: 12
    }, "".concat(token.proComponentsCls, "-layout-apps-icon"), {
      marginBlockEnd: 8,
      fontSize: 16,
      transition: "font-size 0.2s ease-in-out,color 0.2s ease-in-out"
    })
  }), "&-actions", {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBlock: 4,
    marginInline: 0,
    color: (_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.sider) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorTextMenu,
    "&-collapsed": {
      flexDirection: "column-reverse",
      paddingBlock: 0,
      paddingInline: 8,
      fontSize: 16,
      transition: "font-size 0.3s ease-in-out"
    },
    "&-list": {
      color: (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuSecondary,
      "&-collapsed": {
        marginBlockEnd: 8,
        animationName: "none"
      },
      "&-item": {
        paddingInline: 6,
        paddingBlock: 6,
        lineHeight: "16px",
        fontSize: 16,
        cursor: "pointer",
        borderRadius: token.borderRadius,
        "&:hover": {
          background: token.colorBgTextHover
        }
      }
    },
    "&-avatar": {
      fontSize: 14,
      paddingInline: 8,
      paddingBlock: 8,
      display: "flex",
      alignItems: "center",
      gap: token.marginXS,
      borderRadius: token.borderRadius,
      "& *": {
        cursor: "pointer"
      },
      "&:hover": {
        background: token.colorBgTextHover
      }
    }
  }), "&-hide-menu-collapsed", {
    insetInlineStart: "-".concat(token.proLayoutCollapsedWidth - 12, "px"),
    position: "absolute"
  }), "&-extra", {
    marginBlockEnd: 16,
    marginBlock: 0,
    marginInline: 16,
    "&-no-logo": {
      marginBlockStart: 16
    }
  }), "&-links", {
    width: "100%",
    ul: {
      height: "auto"
    }
  }), "&-link-menu", {
    border: "none",
    boxShadow: "none",
    background: "transparent"
  }), "&-footer", {
    color: (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuSecondary,
    paddingBlockEnd: 16,
    fontSize: token.fontSize,
    animationName: proLayoutTitleHide,
    animationDuration: ".4s",
    animationTimingFunction: "ease"
  })), "".concat(token.componentCls).concat(token.componentCls, "-fixed"), {
    position: "fixed",
    insetBlockStart: 0,
    insetInlineStart: 0,
    zIndex: "100",
    height: "100%",
    "&-mix": {
      height: "calc(100% - ".concat(((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.header) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.heightLayoutHeader) || 56, "px)"),
      insetBlockStart: "".concat(((_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.header) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.heightLayoutHeader) || 56, "px")
    }
  }));
};
function useStyle15(prefixCls, _ref2) {
  var proLayoutCollapsedWidth = _ref2.proLayoutCollapsedWidth;
  return useStyle("ProLayoutSiderMenu", function(token) {
    var siderMenuToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls),
      proLayoutCollapsedWidth
    });
    return [genSiderMenuStyle3(siderMenuToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js
var import_jsx_runtime58 = __toESM(require_jsx_runtime());
var SiderMenuWrapper = function SiderMenuWrapper2(props) {
  var _token$layout;
  var isMobile = props.isMobile, siderWidth = props.siderWidth, collapsed = props.collapsed, onCollapse = props.onCollapse, style = props.style, className = props.className, hide = props.hide, prefixCls = props.prefixCls, getContainer = props.getContainer;
  var _useContext = (0, import_react27.useContext)(ProProvider), token = _useContext.token;
  (0, import_react27.useEffect)(function() {
    if (isMobile === true) {
      onCollapse === null || onCollapse === void 0 || onCollapse(true);
    }
  }, [isMobile]);
  var omitProps = omit(props, ["className", "style"]);
  var _React$useContext = import_react27.default.useContext(config_provider_default.ConfigContext), direction = _React$useContext.direction;
  var _useStyle = useStyle15("".concat(prefixCls, "-sider"), {
    proLayoutCollapsedWidth: 64
  }), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var siderClassName = (0, import_classnames17.default)("".concat(prefixCls, "-sider"), className, hashId);
  if (hide) {
    return null;
  }
  var drawerOpenProps = openVisibleCompatible(!collapsed, function() {
    return onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(true);
  });
  return wrapSSR(isMobile ? (0, import_jsx_runtime58.jsx)(drawer_default, _objectSpread2(_objectSpread2({
    placement: direction === "rtl" ? "right" : "left",
    className: (0, import_classnames17.default)("".concat(prefixCls, "-drawer-sider"), className)
  }, drawerOpenProps), {}, {
    style: _objectSpread2({
      padding: 0,
      height: "100vh"
    }, style),
    onClose: function onClose() {
      onCollapse === null || onCollapse === void 0 || onCollapse(true);
    },
    maskClosable: true,
    closable: false,
    getContainer: getContainer || false,
    width: siderWidth,
    styles: {
      body: {
        height: "100vh",
        padding: 0,
        display: "flex",
        flexDirection: "row",
        backgroundColor: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorMenuBackground
      }
    },
    children: (0, import_jsx_runtime58.jsx)(SiderMenu, _objectSpread2(_objectSpread2({}, omitProps), {}, {
      isMobile: true,
      className: siderClassName,
      collapsed: isMobile ? false : collapsed,
      splitMenus: false,
      originCollapsed: collapsed
    }))
  })) : (0, import_jsx_runtime58.jsx)(SiderMenu, _objectSpread2(_objectSpread2({
    className: siderClassName,
    originCollapsed: collapsed
  }, omitProps), {}, {
    style
  })));
};

// node_modules/@ant-design/pro-layout/es/style/index.js
init_defineProperty();
var getVersion = function getVersion2() {
  var _process;
  if (typeof process === "undefined")
    return version_default;
  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version_default;
};
var compatibleStyle = function compatibleStyle2(token) {
  var _getVersion, _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12, _token$layout13, _token$layout14, _token$layout15, _token$layout16, _token$layout17, _token$layout18, _token$layout19, _$concat6, _token$layout20, _token$layout21, _token$layout22, _token$layout23, _token$layout24, _token$layout25, _token$layout26, _token$layout27, _token$layout28, _token$layout29, _token$layout30;
  if ((_getVersion = getVersion()) !== null && _getVersion !== void 0 && _getVersion.startsWith("5")) {
    return {};
  }
  return _defineProperty(_defineProperty(_defineProperty({}, token.componentCls, _defineProperty(_defineProperty({
    width: "100%",
    height: "100%"
  }, "".concat(token.proComponentsCls, "-base-menu"), (_$concat6 = {
    color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextMenu
  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_$concat6, "".concat(token.antCls, "-menu-sub"), {
    backgroundColor: "transparent!important",
    color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorTextMenu
  }), "& ".concat(token.antCls, "-layout"), {
    backgroundColor: "transparent",
    width: "100%"
  }), "".concat(token.antCls, "-menu-submenu-expand-icon, ").concat(token.antCls, "-menu-submenu-arrow"), {
    color: "inherit"
  }), "&".concat(token.antCls, "-menu"), _defineProperty(_defineProperty({
    color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextMenu
  }, "".concat(token.antCls, "-menu-item"), {
    "*": {
      transition: "none !important"
    }
  }), "".concat(token.antCls, "-menu-item a"), {
    color: "inherit"
  })), "&".concat(token.antCls, "-menu-inline"), _defineProperty({}, "".concat(token.antCls, "-menu-selected::after,").concat(token.antCls, "-menu-item-selected::after"), {
    display: "none"
  })), "".concat(token.antCls, "-menu-sub ").concat(token.antCls, "-menu-inline"), {
    backgroundColor: "transparent!important"
  }), "".concat(token.antCls, "-menu-item:active, \n        ").concat(token.antCls, "-menu-submenu-title:active"), {
    backgroundColor: "transparent!important"
  }), "&".concat(token.antCls, "-menu-light"), _defineProperty({}, "".concat(token.antCls, "-menu-item:hover, \n            ").concat(token.antCls, "-menu-item-active,\n            ").concat(token.antCls, "-menu-submenu-active, \n            ").concat(token.antCls, "-menu-submenu-title:hover"), _defineProperty({
    color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuActive,
    borderRadius: token.borderRadius
  }, "".concat(token.antCls, "-menu-submenu-arrow"), {
    color: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenuActive
  }))), "&".concat(token.antCls, "-menu:not(").concat(token.antCls, "-menu-horizontal)"), _defineProperty(_defineProperty({}, "".concat(token.antCls, "-menu-item-selected"), {
    backgroundColor: (_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorBgMenuItemSelected,
    borderRadius: token.borderRadius
  }), "".concat(token.antCls, "-menu-item:hover, \n            ").concat(token.antCls, "-menu-item-active,\n            ").concat(token.antCls, "-menu-submenu-title:hover"), _defineProperty({
    color: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuActive,
    borderRadius: token.borderRadius,
    backgroundColor: "".concat((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.header) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorBgMenuItemHover, " !important")
  }, "".concat(token.antCls, "-menu-submenu-arrow"), {
    color: (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuActive
  }))), "".concat(token.antCls, "-menu-item-selected"), {
    color: (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuSelected
  }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_$concat6, "".concat(token.antCls, "-menu-submenu-selected"), {
    color: (_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.sider) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected
  }), "&".concat(token.antCls, "-menu:not(").concat(token.antCls, "-menu-inline) ").concat(token.antCls, "-menu-submenu-open"), {
    color: (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.sider) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.colorTextMenuSelected
  }), "&".concat(token.antCls, "-menu-vertical"), _defineProperty({}, "".concat(token.antCls, "-menu-submenu-selected"), {
    borderRadius: token.borderRadius,
    color: (_token$layout13 = token.layout) === null || _token$layout13 === void 0 || (_token$layout13 = _token$layout13.sider) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.colorTextMenuSelected
  })), "".concat(token.antCls, "-menu-submenu:hover > ").concat(token.antCls, "-menu-submenu-title > ").concat(token.antCls, "-menu-submenu-arrow"), {
    color: (_token$layout14 = token.layout) === null || _token$layout14 === void 0 || (_token$layout14 = _token$layout14.sider) === null || _token$layout14 === void 0 ? void 0 : _token$layout14.colorTextMenuActive
  }), "&".concat(token.antCls, "-menu-horizontal"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(token.antCls, "-menu-item:hover,\n          ").concat(token.antCls, "-menu-submenu:hover,\n          ").concat(token.antCls, "-menu-item-active,\n          ").concat(token.antCls, "-menu-submenu-active"), {
    borderRadius: 4,
    transition: "none",
    color: (_token$layout15 = token.layout) === null || _token$layout15 === void 0 || (_token$layout15 = _token$layout15.header) === null || _token$layout15 === void 0 ? void 0 : _token$layout15.colorTextMenuActive,
    backgroundColor: "".concat((_token$layout16 = token.layout) === null || _token$layout16 === void 0 || (_token$layout16 = _token$layout16.header) === null || _token$layout16 === void 0 ? void 0 : _token$layout16.colorBgMenuItemHover, " !important")
  }), "".concat(token.antCls, "-menu-item-open,\n          ").concat(token.antCls, "-menu-submenu-open,\n          ").concat(token.antCls, "-menu-item-selected,\n          ").concat(token.antCls, "-menu-submenu-selected"), _defineProperty({
    backgroundColor: (_token$layout17 = token.layout) === null || _token$layout17 === void 0 || (_token$layout17 = _token$layout17.header) === null || _token$layout17 === void 0 ? void 0 : _token$layout17.colorBgMenuItemSelected,
    borderRadius: token.borderRadius,
    transition: "none",
    color: "".concat((_token$layout18 = token.layout) === null || _token$layout18 === void 0 || (_token$layout18 = _token$layout18.header) === null || _token$layout18 === void 0 ? void 0 : _token$layout18.colorTextMenuSelected, " !important")
  }, "".concat(token.antCls, "-menu-submenu-arrow"), {
    color: "".concat((_token$layout19 = token.layout) === null || _token$layout19 === void 0 || (_token$layout19 = _token$layout19.header) === null || _token$layout19 === void 0 ? void 0 : _token$layout19.colorTextMenuSelected, " !important")
  })), "> ".concat(token.antCls, "-menu-item, > ").concat(token.antCls, "-menu-submenu"), {
    paddingInline: 16,
    marginInline: 4
  }), "> ".concat(token.antCls, "-menu-item::after, > ").concat(token.antCls, "-menu-submenu::after"), {
    display: "none"
  })))), "".concat(token.proComponentsCls, "-top-nav-header-base-menu"), _defineProperty(_defineProperty({}, "&".concat(token.antCls, "-menu"), _defineProperty({
    color: (_token$layout20 = token.layout) === null || _token$layout20 === void 0 || (_token$layout20 = _token$layout20.header) === null || _token$layout20 === void 0 ? void 0 : _token$layout20.colorTextMenu
  }, "".concat(token.antCls, "-menu-item a"), {
    color: "inherit"
  })), "&".concat(token.antCls, "-menu-light"), _defineProperty(_defineProperty({}, "".concat(token.antCls, "-menu-item:hover, \n            ").concat(token.antCls, "-menu-item-active,\n            ").concat(token.antCls, "-menu-submenu-active, \n            ").concat(token.antCls, "-menu-submenu-title:hover"), _defineProperty({
    color: (_token$layout21 = token.layout) === null || _token$layout21 === void 0 || (_token$layout21 = _token$layout21.header) === null || _token$layout21 === void 0 ? void 0 : _token$layout21.colorTextMenuActive,
    borderRadius: token.borderRadius,
    transition: "none",
    backgroundColor: (_token$layout22 = token.layout) === null || _token$layout22 === void 0 || (_token$layout22 = _token$layout22.header) === null || _token$layout22 === void 0 ? void 0 : _token$layout22.colorBgMenuItemSelected
  }, "".concat(token.antCls, "-menu-submenu-arrow"), {
    color: (_token$layout23 = token.layout) === null || _token$layout23 === void 0 || (_token$layout23 = _token$layout23.header) === null || _token$layout23 === void 0 ? void 0 : _token$layout23.colorTextMenuActive
  })), "".concat(token.antCls, "-menu-item-selected"), {
    color: (_token$layout24 = token.layout) === null || _token$layout24 === void 0 || (_token$layout24 = _token$layout24.header) === null || _token$layout24 === void 0 ? void 0 : _token$layout24.colorTextMenuSelected,
    borderRadius: token.borderRadius,
    backgroundColor: (_token$layout25 = token.layout) === null || _token$layout25 === void 0 || (_token$layout25 = _token$layout25.header) === null || _token$layout25 === void 0 ? void 0 : _token$layout25.colorBgMenuItemSelected
  })))), "".concat(token.antCls, "-menu-sub").concat(token.antCls, "-menu-inline"), {
    backgroundColor: "transparent!important"
  }), "".concat(token.antCls, "-menu-submenu-popup"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({
    backgroundColor: "rgba(255, 255, 255, 0.42)",
    "-webkit-backdrop-filter": "blur(8px)",
    backdropFilter: "blur(8px)"
  }, "".concat(token.antCls, "-menu"), _defineProperty({
    background: "transparent !important",
    backgroundColor: "transparent !important"
  }, "".concat(token.antCls, "-menu-item:active, \n        ").concat(token.antCls, "-menu-submenu-title:active"), {
    backgroundColor: "transparent!important"
  })), "".concat(token.antCls, "-menu-item-selected"), {
    color: (_token$layout26 = token.layout) === null || _token$layout26 === void 0 || (_token$layout26 = _token$layout26.sider) === null || _token$layout26 === void 0 ? void 0 : _token$layout26.colorTextMenuSelected
  }), "".concat(token.antCls, "-menu-submenu-selected"), {
    color: (_token$layout27 = token.layout) === null || _token$layout27 === void 0 || (_token$layout27 = _token$layout27.sider) === null || _token$layout27 === void 0 ? void 0 : _token$layout27.colorTextMenuSelected
  }), "".concat(token.antCls, "-menu:not(").concat(token.antCls, "-menu-horizontal)"), _defineProperty(_defineProperty({}, "".concat(token.antCls, "-menu-item-selected"), {
    backgroundColor: "rgba(0, 0, 0, 0.04)",
    borderRadius: token.borderRadius,
    color: (_token$layout28 = token.layout) === null || _token$layout28 === void 0 || (_token$layout28 = _token$layout28.sider) === null || _token$layout28 === void 0 ? void 0 : _token$layout28.colorTextMenuSelected
  }), "".concat(token.antCls, "-menu-item:hover, \n          ").concat(token.antCls, "-menu-item-active,\n          ").concat(token.antCls, "-menu-submenu-title:hover"), _defineProperty({
    color: (_token$layout29 = token.layout) === null || _token$layout29 === void 0 || (_token$layout29 = _token$layout29.sider) === null || _token$layout29 === void 0 ? void 0 : _token$layout29.colorTextMenuActive,
    borderRadius: token.borderRadius
  }, "".concat(token.antCls, "-menu-submenu-arrow"), {
    color: (_token$layout30 = token.layout) === null || _token$layout30 === void 0 || (_token$layout30 = _token$layout30.sider) === null || _token$layout30 === void 0 ? void 0 : _token$layout30.colorTextMenuActive
  }))));
};
var genProLayoutStyle = function genProLayoutStyle2(token) {
  var _token$layout31, _token$layout32, _token$layout33, _token$layout34;
  return _defineProperty(_defineProperty({}, "".concat(token.antCls, "-layout"), {
    backgroundColor: "transparent !important"
  }), token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "& ".concat(token.antCls, "-layout"), {
    display: "flex",
    backgroundColor: "transparent",
    width: "100%"
  }), "".concat(token.componentCls, "-content"), {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    backgroundColor: ((_token$layout31 = token.layout) === null || _token$layout31 === void 0 || (_token$layout31 = _token$layout31.pageContainer) === null || _token$layout31 === void 0 ? void 0 : _token$layout31.colorBgPageContainer) || "transparent",
    position: "relative",
    paddingBlock: (_token$layout32 = token.layout) === null || _token$layout32 === void 0 || (_token$layout32 = _token$layout32.pageContainer) === null || _token$layout32 === void 0 ? void 0 : _token$layout32.paddingBlockPageContainerContent,
    paddingInline: (_token$layout33 = token.layout) === null || _token$layout33 === void 0 || (_token$layout33 = _token$layout33.pageContainer) === null || _token$layout33 === void 0 ? void 0 : _token$layout33.paddingInlinePageContainerContent,
    "&-has-page-container": {
      padding: 0
    }
  }), "".concat(token.componentCls, "-container"), {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    minWidth: 0,
    minHeight: 0,
    backgroundColor: "transparent"
  }), "".concat(token.componentCls, "-bg-list"), {
    pointerEvents: "none",
    position: "fixed",
    overflow: "hidden",
    insetBlockStart: 0,
    insetInlineStart: 0,
    zIndex: 0,
    height: "100%",
    width: "100%",
    background: (_token$layout34 = token.layout) === null || _token$layout34 === void 0 ? void 0 : _token$layout34.bgLayout
  }));
};
function useStyle16(prefixCls) {
  return useStyle("ProLayout", function(token) {
    var proLayoutToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProLayoutStyle(proLayoutToken), compatibleStyle(proLayoutToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js
var import_path_to_regexp4 = __toESM(require_dist());

// node_modules/@ant-design/pro-layout/es/utils/pathTools.js
function urlToList(url) {
  if (!url || url === "/") {
    return ["/"];
  }
  var urlList = url.split("/").filter(function(i) {
    return i;
  });
  return urlList.map(function(urlItem, index) {
    return "/".concat(urlList.slice(0, index + 1).join("/"));
  });
}

// node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js
var import_jsx_runtime59 = __toESM(require_jsx_runtime());
var getVersion3 = function getVersion4() {
  var _process;
  if (typeof process === "undefined")
    return version_default;
  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version_default;
};
var defaultItemRender = function defaultItemRender2(route, _, routes) {
  var _ref = route, breadcrumbName = _ref.breadcrumbName, title = _ref.title, path = _ref.path;
  var last = routes.findIndex(function(i) {
    return (
      // @ts-ignore
      i.linkPath === route.path
    );
  }) === routes.length - 1;
  return last ? (0, import_jsx_runtime59.jsx)("span", {
    children: title || breadcrumbName
  }) : (0, import_jsx_runtime59.jsx)("span", {
    onClick: path ? function() {
      return location.href = path;
    } : void 0,
    children: title || breadcrumbName
  });
};
var renderItemLocal = function renderItemLocal2(item, props) {
  var formatMessage = props.formatMessage, menu = props.menu;
  if (item.locale && formatMessage && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {
    return formatMessage({
      id: item.locale,
      defaultMessage: item.name
    });
  }
  return item.name;
};
var getBreadcrumb = function getBreadcrumb2(breadcrumbMap, url) {
  var breadcrumbItem = breadcrumbMap.get(url);
  if (!breadcrumbItem) {
    var keys = Array.from(breadcrumbMap.keys()) || [];
    var targetPath = keys.find(
      function(path) {
        try {
          if (path !== null && path !== void 0 && path.startsWith("http"))
            return false;
          return (0, import_path_to_regexp4.match)(path.replace("?", ""))(url);
        } catch (error) {
          console.log("path", path, error);
          return false;
        }
      }
      // remove ? ,不然会重复
    );
    if (targetPath)
      breadcrumbItem = breadcrumbMap.get(targetPath);
  }
  return breadcrumbItem || {
    path: ""
  };
};
var getBreadcrumbFromProps = function getBreadcrumbFromProps2(props) {
  var location2 = props.location, breadcrumbMap = props.breadcrumbMap;
  return {
    location: location2,
    breadcrumbMap
  };
};
var conversionFromLocation = function conversionFromLocation2(routerLocation, breadcrumbMap, props) {
  var pathSnippets = urlToList(routerLocation === null || routerLocation === void 0 ? void 0 : routerLocation.pathname);
  var extraBreadcrumbItems = pathSnippets.map(function(url) {
    var currentBreadcrumb = getBreadcrumb(breadcrumbMap, url);
    var name = renderItemLocal(currentBreadcrumb, props);
    var hideInBreadcrumb = currentBreadcrumb.hideInBreadcrumb;
    return name && !hideInBreadcrumb ? {
      linkPath: url,
      breadcrumbName: name,
      title: name,
      component: currentBreadcrumb.component
    } : {
      linkPath: "",
      breadcrumbName: "",
      title: ""
    };
  }).filter(function(item) {
    return item && item.linkPath;
  });
  return extraBreadcrumbItems;
};
var genBreadcrumbProps = function genBreadcrumbProps2(props) {
  var _getBreadcrumbFromPro = getBreadcrumbFromProps(props), location2 = _getBreadcrumbFromPro.location, breadcrumbMap = _getBreadcrumbFromPro.breadcrumbMap;
  if (location2 && location2.pathname && breadcrumbMap) {
    return conversionFromLocation(location2, breadcrumbMap, props);
  }
  return [];
};
var getBreadcrumbProps = function getBreadcrumbProps2(props, layoutPros) {
  var breadcrumbRender = props.breadcrumbRender, propsItemRender = props.itemRender;
  var _ref2 = layoutPros.breadcrumbProps || {}, _ref2$minLength = _ref2.minLength, minLength = _ref2$minLength === void 0 ? 2 : _ref2$minLength;
  var routesArray = genBreadcrumbProps(props);
  var itemRender = function itemRender2(item) {
    var renderFunction = propsItemRender || defaultItemRender;
    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      rest[_key - 1] = arguments[_key];
    }
    return renderFunction === null || renderFunction === void 0 ? void 0 : renderFunction.apply(void 0, [_objectSpread2(_objectSpread2({}, item), {}, {
      // 如果item.linkPath存在，则使用item.linkPath，否则使用item.path
      // @ts-ignore
      path: item.linkPath || item.path
    })].concat(rest));
  };
  var items = routesArray;
  if (breadcrumbRender) {
    items = breadcrumbRender(items || []) || void 0;
  }
  if (items && items.length < minLength || breadcrumbRender === false) {
    items = void 0;
  }
  return compareVersions(getVersion3(), "5.3.0") > -1 ? {
    items,
    itemRender
  } : {
    routes: items,
    itemRender
  };
};

// node_modules/@ant-design/pro-layout/es/utils/getMenuData.js
function fromEntries(iterable) {
  return _toConsumableArray(iterable).reduce(function(obj, _ref) {
    var _ref2 = _slicedToArray(_ref, 2), key = _ref2[0], val = _ref2[1];
    obj[key] = val;
    return obj;
  }, {});
}
var getMenuData = function getMenuData2(routes, menu, formatMessage, menuDataRender) {
  var _transformRoute = transformRoute_default(routes, (menu === null || menu === void 0 ? void 0 : menu.locale) || false, formatMessage, true), menuData = _transformRoute.menuData, breadcrumb = _transformRoute.breadcrumb;
  if (!menuDataRender) {
    return {
      breadcrumb: fromEntries(breadcrumb),
      breadcrumbMap: breadcrumb,
      menuData
    };
  }
  return getMenuData2(menuDataRender(menuData), menu, formatMessage, void 0);
};

// node_modules/@ant-design/pro-layout/es/utils/useCurrentMenuLayoutProps.js
init_typeof();
var import_react28 = __toESM(require_react());
var useCurrentMenuLayoutProps = function useCurrentMenuLayoutProps2(currentMenu) {
  var _useState = (0, import_react28.useState)({}), _useState2 = _slicedToArray(_useState, 2), currentMenuLayoutProps = _useState2[0], setCurrentMenuLayoutProps = _useState2[1];
  (0, import_react28.useEffect)(function() {
    setCurrentMenuLayoutProps(omitUndefined({
      // 有时候会变成对象，是原来的方式
      layout: _typeof(currentMenu.layout) !== "object" ? currentMenu.layout : void 0,
      navTheme: currentMenu.navTheme,
      menuRender: currentMenu.menuRender,
      footerRender: currentMenu.footerRender,
      menuHeaderRender: currentMenu.menuHeaderRender,
      headerRender: currentMenu.headerRender,
      fixSiderbar: currentMenu.fixSiderbar
    }));
  }, [currentMenu.layout, currentMenu.navTheme, currentMenu.menuRender, currentMenu.footerRender, currentMenu.menuHeaderRender, currentMenu.headerRender, currentMenu.fixSiderbar]);
  return currentMenuLayoutProps;
};

// node_modules/@ant-design/pro-layout/es/ProLayout.js
var import_jsx_runtime60 = __toESM(require_jsx_runtime());
var import_jsx_runtime61 = __toESM(require_jsx_runtime());
var import_jsx_runtime62 = __toESM(require_jsx_runtime());
var _excluded9 = ["id", "defaultMessage"];
var _excluded24 = ["fixSiderbar", "navTheme", "layout"];
var layoutIndex = 0;
var headerRender = function headerRender2(props, matchMenuKeys) {
  var _props$stylish;
  if (props.headerRender === false || props.pure) {
    return null;
  }
  return (0, import_jsx_runtime60.jsx)(DefaultHeader, _objectSpread2(_objectSpread2({
    matchMenuKeys
  }, props), {}, {
    stylish: (_props$stylish = props.stylish) === null || _props$stylish === void 0 ? void 0 : _props$stylish.header
  }));
};
var footerRender = function footerRender2(props) {
  if (props.footerRender === false || props.pure) {
    return null;
  }
  if (props.footerRender) {
    return props.footerRender(_objectSpread2({}, props), (0, import_jsx_runtime60.jsx)(DefaultFooter, {}));
  }
  return null;
};
var renderSiderMenu = function renderSiderMenu2(props, matchMenuKeys) {
  var _props$stylish3;
  var layout = props.layout, isMobile = props.isMobile, selectedKeys = props.selectedKeys, openKeys = props.openKeys, splitMenus = props.splitMenus, suppressSiderWhenMenuEmpty = props.suppressSiderWhenMenuEmpty, menuRender = props.menuRender;
  if (props.menuRender === false || props.pure) {
    return null;
  }
  var menuData = props.menuData;
  if (splitMenus && (openKeys !== false || layout === "mix") && !isMobile) {
    var _ref = selectedKeys || matchMenuKeys, _ref2 = _slicedToArray(_ref, 1), key = _ref2[0];
    if (key) {
      var _props$menuData;
      menuData = ((_props$menuData = props.menuData) === null || _props$menuData === void 0 || (_props$menuData = _props$menuData.find(function(item) {
        return item.key === key;
      })) === null || _props$menuData === void 0 ? void 0 : _props$menuData.children) || [];
    } else {
      menuData = [];
    }
  }
  var clearMenuData = clearMenuItem(menuData || []);
  if (clearMenuData && (clearMenuData === null || clearMenuData === void 0 ? void 0 : clearMenuData.length) < 1 && (splitMenus || suppressSiderWhenMenuEmpty)) {
    return null;
  }
  if (layout === "top" && !isMobile) {
    var _props$stylish2;
    return (0, import_jsx_runtime60.jsx)(SiderMenuWrapper, _objectSpread2(_objectSpread2({
      matchMenuKeys
    }, props), {}, {
      hide: true,
      stylish: (_props$stylish2 = props.stylish) === null || _props$stylish2 === void 0 ? void 0 : _props$stylish2.sider
    }));
  }
  var defaultDom = (0, import_jsx_runtime60.jsx)(SiderMenuWrapper, _objectSpread2(_objectSpread2({
    matchMenuKeys
  }, props), {}, {
    // 这里走了可以少一次循环
    menuData: clearMenuData,
    stylish: (_props$stylish3 = props.stylish) === null || _props$stylish3 === void 0 ? void 0 : _props$stylish3.sider
  }));
  if (menuRender) {
    return menuRender(props, defaultDom);
  }
  return defaultDom;
};
var defaultPageTitleRender = function defaultPageTitleRender2(pageProps, props) {
  var pageTitleRender = props.pageTitleRender;
  var pageTitleInfo = getPageTitleInfo(pageProps);
  if (pageTitleRender === false) {
    return {
      title: props.title || "",
      id: "",
      pageName: ""
    };
  }
  if (pageTitleRender) {
    var title = pageTitleRender(pageProps, pageTitleInfo.title, pageTitleInfo);
    if (typeof title === "string") {
      return getPageTitleInfo(_objectSpread2(_objectSpread2({}, pageTitleInfo), {}, {
        title
      }));
    }
    warning_default(typeof title === "string", "pro-layout: renderPageTitle return value should be a string");
  }
  return pageTitleInfo;
};
var getPaddingInlineStart = function getPaddingInlineStart2(hasLeftPadding, collapsed, siderWidth) {
  if (hasLeftPadding) {
    return collapsed ? 64 : siderWidth;
  }
  return 0;
};
var BaseProLayout = function BaseProLayout2(props) {
  var _props$prefixCls, _location$pathname, _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12;
  var _ref3 = props || {}, children = _ref3.children, propsOnCollapse = _ref3.onCollapse, _ref3$location = _ref3.location, location2 = _ref3$location === void 0 ? {
    pathname: "/"
  } : _ref3$location, contentStyle = _ref3.contentStyle, route = _ref3.route, defaultCollapsed = _ref3.defaultCollapsed, style = _ref3.style, propsSiderWidth = _ref3.siderWidth, menu = _ref3.menu, siderMenuType = _ref3.siderMenuType, propsIsChildrenLayout = _ref3.isChildrenLayout, menuDataRender = _ref3.menuDataRender, actionRef = _ref3.actionRef, bgLayoutImgList = _ref3.bgLayoutImgList, propsFormatMessage = _ref3.formatMessage, loading = _ref3.loading;
  var siderWidth = (0, import_react29.useMemo)(function() {
    if (propsSiderWidth)
      return propsSiderWidth;
    if (props.layout === "mix")
      return 215;
    return 256;
  }, [props.layout, propsSiderWidth]);
  var context = (0, import_react29.useContext)(config_provider_default.ConfigContext);
  var prefixCls = (_props$prefixCls = props.prefixCls) !== null && _props$prefixCls !== void 0 ? _props$prefixCls : context.getPrefixCls("pro");
  var _useMountMergeState = useMergedState(false, {
    value: menu === null || menu === void 0 ? void 0 : menu.loading,
    onChange: menu === null || menu === void 0 ? void 0 : menu.onLoadingChange
  }), _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2), menuLoading = _useMountMergeState2[0], setMenuLoading = _useMountMergeState2[1];
  var _useState = (0, import_react29.useState)(function() {
    layoutIndex += 1;
    return "pro-layout-".concat(layoutIndex);
  }), _useState2 = _slicedToArray(_useState, 1), defaultId = _useState2[0];
  var formatMessage = (0, import_react29.useCallback)(function(_ref4) {
    var id = _ref4.id, defaultMessage = _ref4.defaultMessage, restParams = _objectWithoutProperties(_ref4, _excluded9);
    if (propsFormatMessage) {
      return propsFormatMessage(_objectSpread2({
        id,
        defaultMessage
      }, restParams));
    }
    var locales2 = gLocaleObject();
    return locales2[id] ? locales2[id] : defaultMessage;
  }, [propsFormatMessage]);
  var _useSWR = useSWR([defaultId, menu === null || menu === void 0 ? void 0 : menu.params], function() {
    var _ref6 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(_ref5) {
      var _menu$request;
      var _ref7, params, menuDataItems;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              _ref7 = _slicedToArray(_ref5, 2), params = _ref7[1];
              setMenuLoading(true);
              _context.next = 4;
              return menu === null || menu === void 0 || (_menu$request = menu.request) === null || _menu$request === void 0 ? void 0 : _menu$request.call(menu, params || {}, (route === null || route === void 0 ? void 0 : route.children) || (route === null || route === void 0 ? void 0 : route.routes) || []);
            case 4:
              menuDataItems = _context.sent;
              setMenuLoading(false);
              return _context.abrupt("return", menuDataItems);
            case 7:
            case "end":
              return _context.stop();
          }
      }, _callee);
    }));
    return function(_x) {
      return _ref6.apply(this, arguments);
    };
  }(), {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
    revalidateOnReconnect: false
  }), data = _useSWR.data, mutate = _useSWR.mutate, isLoading = _useSWR.isLoading;
  (0, import_react29.useEffect)(function() {
    setMenuLoading(isLoading);
  }, [isLoading]);
  var _useSWRConfig = useSWRConfig(), cache = _useSWRConfig.cache;
  (0, import_react29.useEffect)(function() {
    return function() {
      if (cache instanceof Map)
        cache.delete(defaultId);
    };
  }, []);
  var menuInfoData = (0, import_react29.useMemo)(function() {
    return getMenuData(data || (route === null || route === void 0 ? void 0 : route.children) || (route === null || route === void 0 ? void 0 : route.routes) || [], menu, formatMessage, menuDataRender);
  }, [formatMessage, menu, menuDataRender, data, route === null || route === void 0 ? void 0 : route.children, route === null || route === void 0 ? void 0 : route.routes]);
  var _ref8 = menuInfoData || {}, breadcrumb = _ref8.breadcrumb, breadcrumbMap = _ref8.breadcrumbMap, _ref8$menuData = _ref8.menuData, menuData = _ref8$menuData === void 0 ? [] : _ref8$menuData;
  if (actionRef && menu !== null && menu !== void 0 && menu.request) {
    actionRef.current = {
      reload: function reload() {
        mutate();
      }
    };
  }
  var matchMenus = (0, import_react29.useMemo)(function() {
    return getMatchMenu_default(location2.pathname || "/", menuData || [], true);
  }, [location2.pathname, menuData]);
  var matchMenuKeys = (0, import_react29.useMemo)(function() {
    return Array.from(new Set(matchMenus.map(function(item) {
      return item.key || item.path || "";
    })));
  }, [matchMenus]);
  var currentMenu = matchMenus[matchMenus.length - 1] || {};
  var currentMenuLayoutProps = useCurrentMenuLayoutProps(currentMenu);
  var _props$currentMenuLay = _objectSpread2(_objectSpread2({}, props), currentMenuLayoutProps), fixSiderbar = _props$currentMenuLay.fixSiderbar, navTheme = _props$currentMenuLay.navTheme, propsLayout = _props$currentMenuLay.layout, rest = _objectWithoutProperties(_props$currentMenuLay, _excluded24);
  var colSize = useBreakpoint();
  var isMobile = (0, import_react29.useMemo)(function() {
    return (colSize === "sm" || colSize === "xs") && !props.disableMobile;
  }, [colSize, props.disableMobile]);
  var hasLeftPadding = propsLayout !== "top" && !isMobile;
  var _useMergedState = useMergedState(function() {
    if (defaultCollapsed !== void 0)
      return defaultCollapsed;
    if (false)
      return false;
    if (isMobile)
      return true;
    if (colSize === "md")
      return true;
    return false;
  }, {
    value: props.collapsed,
    onChange: propsOnCollapse
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), collapsed = _useMergedState2[0], onCollapse = _useMergedState2[1];
  var defaultProps = omit(_objectSpread2(_objectSpread2(_objectSpread2({
    prefixCls
  }, props), {}, {
    siderWidth
  }, currentMenuLayoutProps), {}, {
    formatMessage,
    breadcrumb,
    menu: _objectSpread2(_objectSpread2({}, menu), {}, {
      type: siderMenuType || (menu === null || menu === void 0 ? void 0 : menu.type),
      loading: menuLoading
    }),
    layout: propsLayout
  }), ["className", "style", "breadcrumbRender"]);
  var pageTitleInfo = defaultPageTitleRender(_objectSpread2(_objectSpread2({
    pathname: location2.pathname
  }, defaultProps), {}, {
    breadcrumbMap
  }), props);
  var breadcrumbProps = getBreadcrumbProps(_objectSpread2(_objectSpread2({}, defaultProps), {}, {
    breadcrumbRender: props.breadcrumbRender,
    breadcrumbMap
  }), props);
  var siderMenuDom = renderSiderMenu(_objectSpread2(_objectSpread2({}, defaultProps), {}, {
    menuData,
    onCollapse,
    isMobile,
    collapsed
  }), matchMenuKeys);
  var headerDom = headerRender(_objectSpread2(_objectSpread2({}, defaultProps), {}, {
    children: null,
    hasSiderMenu: !!siderMenuDom,
    menuData,
    isMobile,
    collapsed,
    onCollapse
  }), matchMenuKeys);
  var footerDom = footerRender(_objectSpread2({
    isMobile,
    collapsed
  }, defaultProps));
  var _useContext = (0, import_react29.useContext)(RouteContext), contextIsChildrenLayout = _useContext.isChildrenLayout;
  var isChildrenLayout = propsIsChildrenLayout !== void 0 ? propsIsChildrenLayout : contextIsChildrenLayout;
  var proLayoutClassName = "".concat(prefixCls, "-layout");
  var _useStyle = useStyle16(proLayoutClassName), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var className = (0, import_classnames18.default)(props.className, hashId, "ant-design-pro", proLayoutClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "screen-".concat(colSize), colSize), "".concat(proLayoutClassName, "-top-menu"), propsLayout === "top"), "".concat(proLayoutClassName, "-is-children"), isChildrenLayout), "".concat(proLayoutClassName, "-fix-siderbar"), fixSiderbar), "".concat(proLayoutClassName, "-").concat(propsLayout), propsLayout));
  var leftSiderWidth = getPaddingInlineStart(!!hasLeftPadding, collapsed, siderWidth);
  var genLayoutStyle = {
    position: "relative"
  };
  if (isChildrenLayout || contentStyle && contentStyle.minHeight) {
    genLayoutStyle.minHeight = 0;
  }
  (0, import_react29.useEffect)(function() {
    var _props$onPageChange;
    (_props$onPageChange = props.onPageChange) === null || _props$onPageChange === void 0 || _props$onPageChange.call(props, props.location);
  }, [location2.pathname, (_location$pathname = location2.pathname) === null || _location$pathname === void 0 ? void 0 : _location$pathname.search]);
  var _useState3 = (0, import_react29.useState)(false), _useState4 = _slicedToArray(_useState3, 2), hasFooterToolbar = _useState4[0], setHasFooterToolbar = _useState4[1];
  var _useState5 = (0, import_react29.useState)(0), _useState6 = _slicedToArray(_useState5, 2), hasPageContainer = _useState6[0], setHasPageContainer = _useState6[1];
  useDocumentTitle(pageTitleInfo, props.title || false);
  var _useContext2 = (0, import_react29.useContext)(ProProvider), token = _useContext2.token;
  var bgImgStyleList = (0, import_react29.useMemo)(function() {
    if (bgLayoutImgList && bgLayoutImgList.length > 0) {
      return bgLayoutImgList === null || bgLayoutImgList === void 0 ? void 0 : bgLayoutImgList.map(function(item, index) {
        return (0, import_jsx_runtime60.jsx)("img", {
          src: item.src,
          style: _objectSpread2({
            position: "absolute"
          }, item)
        }, index);
      });
    }
    return null;
  }, [bgLayoutImgList]);
  return wrapSSR((0, import_jsx_runtime60.jsx)(RouteContext.Provider, {
    value: _objectSpread2(_objectSpread2({}, defaultProps), {}, {
      breadcrumb: breadcrumbProps,
      menuData,
      isMobile,
      collapsed,
      hasPageContainer,
      setHasPageContainer,
      isChildrenLayout: true,
      title: pageTitleInfo.pageName,
      hasSiderMenu: !!siderMenuDom,
      hasHeader: !!headerDom,
      siderWidth: leftSiderWidth,
      hasFooter: !!footerDom,
      hasFooterToolbar,
      setHasFooterToolbar,
      pageTitleInfo,
      matchMenus,
      matchMenuKeys,
      currentMenu
    }),
    children: props.pure ? (0, import_jsx_runtime60.jsx)(import_jsx_runtime61.Fragment, {
      children
    }) : (0, import_jsx_runtime62.jsxs)("div", {
      className,
      children: [bgImgStyleList || (_token$layout = token.layout) !== null && _token$layout !== void 0 && _token$layout.bgLayout ? (0, import_jsx_runtime60.jsx)("div", {
        className: (0, import_classnames18.default)("".concat(proLayoutClassName, "-bg-list"), hashId),
        children: bgImgStyleList
      }) : null, (0, import_jsx_runtime62.jsxs)(layout_default, {
        style: _objectSpread2({
          minHeight: "100%",
          // hack style
          flexDirection: siderMenuDom ? "row" : void 0
        }, style),
        children: [(0, import_jsx_runtime60.jsx)(
          config_provider_default,
          {
            theme: {
              hashed: isNeedOpenHash(),
              token: {
                controlHeightLG: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.menuHeight) || (token === null || token === void 0 ? void 0 : token.controlHeightLG)
              },
              components: {
                Menu: coverToNewToken({
                  colorItemBg: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorMenuBackground) || "transparent",
                  colorSubItemBg: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorMenuBackground) || "transparent",
                  radiusItem: token.borderRadius,
                  colorItemBgSelected: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),
                  colorItemBgHover: ((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorBgMenuItemHover) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),
                  colorItemBgActive: ((_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorBgMenuItemActive) || (token === null || token === void 0 ? void 0 : token.colorBgTextActive),
                  colorItemBgSelectedHorizontal: ((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.sider) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),
                  colorActiveBarWidth: 0,
                  colorActiveBarHeight: 0,
                  colorActiveBarBorderSize: 0,
                  colorItemText: ((_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenu) || (token === null || token === void 0 ? void 0 : token.colorTextSecondary),
                  colorItemTextHover: ((_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuItemHover) || "rgba(0, 0, 0, 0.85)",
                  // 悬浮态
                  colorItemTextSelected: ((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.sider) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected) || "rgba(0, 0, 0, 1)",
                  popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,
                  subMenuItemBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,
                  darkSubMenuItemBg: "transparent",
                  darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated
                })
              }
            },
            children: siderMenuDom
          }
        ), (0, import_jsx_runtime62.jsxs)("div", {
          style: genLayoutStyle,
          className: "".concat(proLayoutClassName, "-container ").concat(hashId).trim(),
          children: [headerDom, (0, import_jsx_runtime60.jsx)(WrapContent, _objectSpread2(_objectSpread2({
            hasPageContainer,
            isChildrenLayout
          }, rest), {}, {
            hasHeader: !!headerDom,
            prefixCls: proLayoutClassName,
            style: contentStyle,
            children: loading ? (0, import_jsx_runtime60.jsx)(PageLoading, {}) : children
          })), footerDom, hasFooterToolbar && (0, import_jsx_runtime60.jsx)("div", {
            className: "".concat(proLayoutClassName, "-has-footer"),
            style: {
              height: 64,
              marginBlockStart: (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.pageContainer) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.paddingBlockPageContainerContent
            }
          })]
        })]
      })]
    })
  }));
};
var ProLayout = function ProLayout2(props) {
  var colorPrimary = props.colorPrimary;
  var darkProps = props.navTheme !== void 0 ? {
    dark: props.navTheme === "realDark"
  } : {};
  return (0, import_jsx_runtime60.jsx)(config_provider_default, {
    theme: colorPrimary ? {
      token: {
        colorPrimary
      }
    } : void 0,
    children: (0, import_jsx_runtime60.jsx)(ProConfigProvider, _objectSpread2(_objectSpread2({}, darkProps), {}, {
      token: props.token,
      prefixCls: props.prefixCls,
      children: (0, import_jsx_runtime60.jsx)(BaseProLayout, _objectSpread2(_objectSpread2({
        logo: (0, import_jsx_runtime60.jsx)(Logo, {})
      }, defaultSettings), {}, {
        location: isBrowser() ? window.location : void 0
      }, props))
    }))
  });
};

// node_modules/@ant-design/pro-layout/es/components/Help/index.js
var import_react37 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/Help/HelpProvide.js
var import_react30 = __toESM(require_react());
var ProHelpProvide = import_react30.default.createContext({
  dataSource: [],
  valueTypeMap: /* @__PURE__ */ new Map()
});

// node_modules/@ant-design/pro-layout/es/components/Help/Search.js
init_defineProperty();
var import_classnames19 = __toESM(require_classnames());
var import_react31 = __toESM(require_react());
var import_jsx_runtime63 = __toESM(require_jsx_runtime());
var import_jsx_runtime64 = __toESM(require_jsx_runtime());
var import_jsx_runtime65 = __toESM(require_jsx_runtime());
var _excluded10 = ["iconClassName"];
var Highlight = function Highlight2(_ref) {
  var label = _ref.label, words = _ref.words;
  var _useContext = (0, import_react31.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var lightCls = getPrefixCls("pro-help-search-list-item-content-light");
  var optionCls = getPrefixCls("pro-help-search-list-item-content");
  var _useStyle = useStyle("Highlight", function(token) {
    return _defineProperty(_defineProperty({}, ".".concat(lightCls), {
      color: token.colorPrimary
    }), ".".concat(optionCls), {
      flex: "auto",
      overflow: "hidden",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis"
    });
  }), wrapSSR = _useStyle.wrapSSR;
  if (words.length === 0)
    return (0, import_jsx_runtime64.jsx)(import_jsx_runtime63.Fragment, {
      children: label
    });
  var matchKeywordsRE = new RegExp(words.map(function(word) {
    return word.replace(/[-[\]/{}()*+?.\\^$|]/g, "\\$&");
  }).join("|"), "gi");
  var matchText = label;
  var elements = [];
  while (matchText.length) {
    var match3 = matchKeywordsRE.exec(matchText);
    if (!match3) {
      elements.push(matchText);
      break;
    }
    var start = match3.index;
    var matchLength = match3[0].length + start;
    elements.push(matchText.slice(0, start), import_react31.default.createElement("span", {
      className: lightCls
    }, matchText.slice(start, matchLength)));
    matchText = matchText.slice(matchLength);
  }
  return wrapSSR(import_react31.default.createElement.apply(import_react31.default, ["div", {
    title: label,
    className: optionCls
  }].concat(elements)));
};
var ProHelpSelect = function ProHelpSelect2(_ref3) {
  var iconClassName = _ref3.iconClassName, props = _objectWithoutProperties(_ref3, _excluded10);
  var _useContext2 = (0, import_react31.useContext)(ProHelpProvide), dataSource = _useContext2.dataSource;
  var _useState = (0, import_react31.useState)(""), _useState2 = _slicedToArray(_useState, 2), keyWord = _useState2[0], setKeyWork = _useState2[1];
  var _useContext3 = (0, import_react31.useContext)(ProProvider), hashId = _useContext3.hashId;
  var debounceSetKeyWork = useDebounceFn(function() {
    var _ref4 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(key) {
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              return _context.abrupt("return", setKeyWork(key));
            case 1:
            case "end":
              return _context.stop();
          }
      }, _callee);
    }));
    return function(_x) {
      return _ref4.apply(this, arguments);
    };
  }(), 20);
  var _useState3 = (0, import_react31.useState)(false), _useState4 = _slicedToArray(_useState3, 2), open = _useState4[0], setOpen = _useState4[1];
  return (0, import_jsx_runtime65.jsxs)(import_jsx_runtime63.Fragment, {
    children: [!open ? (0, import_jsx_runtime64.jsx)("div", {
      className: (0, import_classnames19.default)(iconClassName, hashId),
      children: (0, import_jsx_runtime64.jsx)(SearchOutlined_default, {
        title: "search panel",
        onClick: function onClick() {
          setOpen(true);
        }
      })
    }) : null, open ? (0, import_jsx_runtime64.jsx)(select_default, _objectSpread2(_objectSpread2(_objectSpread2({
      placeholder: "please input search text",
      showSearch: true
    }, compatibleBorder(false)), {}, {
      onBlur: function onBlur() {
        setOpen(false);
      },
      size: "small"
    }, props), {}, {
      onSearch: function onSearch(value) {
        debounceSetKeyWork.cancel();
        debounceSetKeyWork.run(value);
      },
      filterOption: function filterOption(input, option) {
        var _option$title;
        return ((_option$title = option === null || option === void 0 ? void 0 : option.title) !== null && _option$title !== void 0 ? _option$title : "").toLowerCase().includes(input.toLowerCase());
      },
      popupMatchSelectWidth: false,
      options: dataSource.map(function(item) {
        var _item$children;
        return {
          label: (0, import_jsx_runtime64.jsx)(Highlight, {
            label: item.title,
            words: [keyWord].filter(Boolean)
          }),
          title: item.title,
          value: item.key,
          options: (_item$children = item.children) === null || _item$children === void 0 ? void 0 : _item$children.map(function(sunItem) {
            return {
              label: (0, import_jsx_runtime64.jsx)(Highlight, {
                label: sunItem.title,
                words: [keyWord].filter(Boolean)
              }),
              title: sunItem.title,
              value: sunItem.key,
              dataItemKey: item.key
            };
          })
        };
      })
    })) : null]
  });
};

// node_modules/@ant-design/pro-layout/es/components/Help/index.js
var import_jsx_runtime75 = __toESM(require_jsx_runtime());

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpContentPanel.js
var import_classnames20 = __toESM(require_classnames());
var import_react35 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/Help/AsyncContentPanel.js
var import_react34 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/Help/RenderContentPanel.js
var import_react33 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpPanel.js
var import_react32 = __toESM(require_react());

// node_modules/@ant-design/pro-layout/es/components/Help/style.js
init_defineProperty();
var actionsInputAnimal = new Keyframes_default("actionsInputAnimal", {
  "0%": {
    width: "0px"
  },
  "30%": {
    width: "20px"
  },
  "100%": {
    width: "120px"
  }
});
var genProHelpStyle = function genProHelpStyle2(token) {
  return _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(token.componentCls, "-popover-text"), {
    color: token.colorPrimary,
    cursor: "pointer",
    boxSizing: "border-box"
  }), "".concat(token.componentCls, "-popover-content"), {
    maxWidth: 300,
    height: "600px",
    maxHeight: "calc(100vh - 200px)",
    overflow: "auto",
    paddingInline: 20,
    paddingBlockStart: 24,
    paddingBlockEnd: 32,
    boxSizing: "border-box"
  }), "".concat(token.componentCls, "-left-panel"), {
    overflow: "auto",
    boxSizing: "border-box",
    borderInlineEnd: "".concat(token === null || token === void 0 ? void 0 : token.lineWidth, "px solid ").concat(token === null || token === void 0 ? void 0 : token.colorBorderSecondary),
    minHeight: "648px",
    minWidth: 190,
    maxWidth: 190,
    "&-menu": {
      width: 190,
      boxSizing: "border-box",
      minWidth: 190,
      height: "calc(100% - 40px)",
      marginBlock: 20
    }
  }), "".concat(token.componentCls, "-content-panel"), {
    minWidth: "200px",
    overflow: "auto",
    flex: 1,
    display: "flex",
    flexDirection: "column",
    boxSizing: "border-box",
    minHeight: "648px",
    img: {
      width: "100%"
    }
  }), "".concat(token.componentCls, "-content-render"), {
    paddingBlock: 20,
    paddingInline: 24,
    flex: 1
  }), "".concat(token.componentCls, "-content-footer"), {
    padding: 8
  }), "".concat(token.componentCls, "-actions"), {
    display: "flex",
    boxSizing: "border-box",
    gap: token.marginSM,
    "&-item": {
      display: "flex",
      boxSizing: "border-box",
      alignItems: "center",
      justifyItems: "center",
      padding: 4,
      height: 24,
      minWidth: 24,
      cursor: "pointer",
      borderRadius: token.borderRadius,
      "&:hover": {
        backgroundColor: token.colorBgTextHover
      }
    },
    "&-input": {
      margin: 0,
      maxWidth: 120,
      padding: 0,
      width: "120px",
      boxSizing: "border-box",
      borderRadius: token.borderRadius,
      backgroundColor: token.colorBgTextHover,
      animationName: actionsInputAnimal,
      animationDuration: "0.1s",
      animationTimingFunction: "linear"
    }
  });
};
function useStyle17(prefixCls) {
  return useStyle("ProHelp", function(token) {
    var ProLayoutHeaderToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProHelpStyle(ProLayoutHeaderToken)];
  });
}

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpPanel.js
var import_jsx_runtime66 = __toESM(require_jsx_runtime());
var import_jsx_runtime67 = __toESM(require_jsx_runtime());
var import_jsx_runtime68 = __toESM(require_jsx_runtime());
var _excluded11 = ["title", "bordered", "onClose", "footer", "height", "extraRender"];
var SelectKeyProvide = import_react32.default.createContext({
  selectedKey: void 0,
  setSelectedKey: function setSelectedKey() {
  }
});
var ProHelpPanel = function ProHelpPanel2(_ref) {
  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5;
  var _ref$title = _ref.title, title = _ref$title === void 0 ? "帮助中心" : _ref$title, _ref$bordered = _ref.bordered, bordered = _ref$bordered === void 0 ? true : _ref$bordered, onClose = _ref.onClose, footer = _ref.footer, height = _ref.height, extraRender = _ref.extraRender, props = _objectWithoutProperties(_ref, _excluded11);
  var _useContext = (0, import_react32.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var className = getPrefixCls("pro-help");
  var _useStyle = useStyle17(className), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var _useContext2 = (0, import_react32.useContext)(ProHelpProvide), dataSource = _useContext2.dataSource;
  var _useMergedState = useMergedState(void 0, {
    defaultValue: props.defaultSelectedKey,
    value: props.selectedKey,
    onChange: props.onSelectedKeyChange
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), selectedKey = _useMergedState2[0], setSelectedKey2 = _useMergedState2[1];
  var _useState = (0, import_react32.useState)(""), _useState2 = _slicedToArray(_useState, 2), openKey = _useState2[0], setOpenKey = _useState2[1];
  var _useContext3 = (0, import_react32.useContext)(ProProvider), token = _useContext3.token;
  var _useMergedState3 = useMergedState(true, {
    value: props.showLeftPanel,
    onChange: props.onShowLeftPanelChange
  }), _useMergedState4 = _slicedToArray(_useMergedState3, 2), showLeftPanel = _useMergedState4[0], setShowLeftPanel = _useMergedState4[1];
  var dataSourceKeyMap = (0, import_react32.useMemo)(function() {
    var map = /* @__PURE__ */ new Map();
    dataSource.forEach(function(page) {
      var _page$children;
      map.set(page.key, page);
      (_page$children = page.children) === null || _page$children === void 0 || _page$children.forEach(function(item) {
        map.set(item.key || item.title, _objectSpread2({
          parentKey: page.key
        }, item));
      });
    });
    return map;
  }, [dataSource]);
  var parentKey = (0, import_react32.useMemo)(function() {
    var _dataSourceKeyMap$get;
    return (_dataSourceKeyMap$get = dataSourceKeyMap.get(selectedKey)) === null || _dataSourceKeyMap$get === void 0 ? void 0 : _dataSourceKeyMap$get.parentKey;
  }, [dataSourceKeyMap, selectedKey]);
  var defaultExtraActions = {
    collapsePanelAction: (0, import_jsx_runtime66.jsx)("div", {
      className: "".concat(className, "-actions-item ").concat(hashId).trim(),
      children: (0, import_jsx_runtime66.jsx)(ProfileOutlined_default, {
        title: "collapse panel",
        onClick: function onClick() {
          setShowLeftPanel(!showLeftPanel);
        }
      })
    }),
    helpSelectAction: (0, import_jsx_runtime66.jsx)(ProHelpSelect, {
      iconClassName: "".concat(className, "-actions-item"),
      className: "".concat(hashId, " ").concat(className, "-actions-input"),
      value: selectedKey,
      onChange: function onChange(value, item) {
        setSelectedKey2(value);
        setOpenKey(item === null || item === void 0 ? void 0 : item.dataItemKey);
      }
    }),
    closeAction: (0, import_jsx_runtime66.jsx)("div", {
      className: "".concat(className, "-actions-item ").concat(hashId).trim(),
      children: (0, import_jsx_runtime66.jsx)(CloseOutlined_default, {
        title: "close panel",
        onClick: function onClick() {
          onClose === null || onClose === void 0 || onClose();
        }
      })
    })
  };
  var extraDomList = function extraDomList2() {
    return (0, import_jsx_runtime66.jsx)("div", {
      className: "".concat(className, "-actions ").concat(hashId).trim(),
      children: extraRender ? extraRender(defaultExtraActions.collapsePanelAction, defaultExtraActions.helpSelectAction, defaultExtraActions.closeAction) : (0, import_jsx_runtime68.jsxs)(import_jsx_runtime67.Fragment, {
        children: [defaultExtraActions.collapsePanelAction, defaultExtraActions.helpSelectAction, onClose ? defaultExtraActions.closeAction : null]
      })
    });
  };
  return wrapSSR((0, import_jsx_runtime66.jsx)(SelectKeyProvide.Provider, {
    value: {
      selectedKey,
      setSelectedKey: setSelectedKey2
    },
    children: (0, import_jsx_runtime68.jsxs)(card_default, {
      bordered,
      title,
      bodyStyle: {
        display: "flex",
        padding: 0,
        margin: 0,
        height: "100%",
        width: "100%"
      },
      size: "small",
      extra: extraDomList(),
      children: [showLeftPanel ? (0, import_jsx_runtime66.jsx)("div", {
        className: "".concat(hashId, " ").concat(className, "-left-panel "),
        style: {
          height
        },
        children: (0, import_jsx_runtime66.jsx)(config_provider_default, {
          theme: {
            hashed: isNeedOpenHash(),
            token: {
              lineHeight: 1.2,
              fontSize: 12,
              controlHeightLG: 26
            },
            components: {
              Menu: coverToNewToken({
                radiusItem: token.borderRadius,
                colorActiveBarWidth: 0,
                colorActiveBarBorderSize: 0,
                colorItemBgSelected: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorBgMenuItemSelected) || "rgba(0, 0, 0, 0.04)",
                colorItemBgActive: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgMenuItemHover) || "rgba(0, 0, 0, 0.04)",
                colorItemText: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextMenu) || "rgba(0, 0, 0, 0.65)",
                colorItemTextHover: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuActive) || "rgba(0, 0, 0, 0.85)",
                colorItemTextSelected: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenuSelected) || "rgba(0, 0, 0, 1)",
                colorItemBg: "transparent",
                colorSubItemBg: "transparent",
                popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,
                darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated
              })
            }
          },
          children: (0, import_jsx_runtime66.jsx)(menu_default, {
            className: "".concat(hashId, " ").concat(className, "-left-panel-menu"),
            openKeys: [parentKey, openKey],
            onOpenChange: function onOpenChange(keys) {
              setOpenKey(keys.at(-1) || "");
            },
            selectedKeys: selectedKey ? [selectedKey] : [],
            onSelect: function onSelect(_ref2) {
              var selectedKeys = _ref2.selectedKeys;
              setSelectedKey2(selectedKeys.at(-1) || "");
            },
            mode: "inline",
            items: dataSource.map(function(item) {
              return {
                key: item.key,
                label: item.title,
                children: item.children.map(function(child) {
                  return {
                    key: child.key,
                    label: child.title
                  };
                })
              };
            })
          })
        })
      }) : null, (0, import_jsx_runtime68.jsxs)("div", {
        className: "".concat(hashId, " ").concat(className, "-content-panel"),
        style: {
          height
        },
        children: [selectedKey ? (0, import_jsx_runtime66.jsx)(ProHelpContentPanel, {
          parentItem: dataSourceKeyMap.get(parentKey),
          className: "".concat(className, "-content-render"),
          selectedKey,
          onScroll: function onScroll(key) {
            return setSelectedKey2(key);
          }
        }) : null, footer ? (0, import_jsx_runtime66.jsx)("div", {
          className: "".concat(hashId, " ").concat(className, "-footer"),
          children: footer
        }) : null]
      })]
    })
  }));
};

// node_modules/@ant-design/pro-layout/es/components/Help/RenderContentPanel.js
var import_jsx_runtime69 = __toESM(require_jsx_runtime());
var HTMLRender = function HTMLRender2(props) {
  var ref = (0, import_react33.useRef)(null);
  (0, import_react33.useEffect)(function() {
    if (ref.current)
      ref.current.innerHTML = props.children;
  }, [props.children]);
  return (0, import_jsx_runtime69.jsx)("div", {
    ref,
    className: props.className || "inner-html"
  });
};
var NavigationSwitch = function NavigationSwitch2(props) {
  var context = (0, import_react33.useContext)(SelectKeyProvide);
  return (0, import_jsx_runtime69.jsx)(typography_default.Text, {
    children: (0, import_jsx_runtime69.jsx)("a", {
      "data-testid": "navigation-switch",
      onClick: function onClick() {
        context.setSelectedKey(props.selectKey);
      },
      children: props.children
    })
  });
};
var RenderContentPanel = function RenderContentPanel2(_ref) {
  var dataSourceChildren = _ref.dataSourceChildren, onInit = _ref.onInit;
  var _useContext = (0, import_react33.useContext)(ProHelpProvide), valueTypeMap = _useContext.valueTypeMap;
  var divRef = (0, import_react33.useRef)(null);
  (0, import_react33.useEffect)(function() {
    onInit === null || onInit === void 0 || onInit(divRef.current);
  }, [dataSourceChildren]);
  var itemRender = function itemRender2(item, index) {
    if (valueTypeMap.has(item.valueType)) {
      var _valueTypeMap$get;
      return (0, import_jsx_runtime69.jsx)(import_react33.default.Fragment, {
        children: (_valueTypeMap$get = valueTypeMap.get(item.valueType)) === null || _valueTypeMap$get === void 0 ? void 0 : _valueTypeMap$get(item, index)
      }, index);
    }
    if (item.valueType === "html") {
      return (0, import_jsx_runtime69.jsx)(HTMLRender, _objectSpread2({}, item.children), index);
    }
    if (item.valueType === "h1") {
      return (0, import_jsx_runtime69.jsx)(typography_default.Title, {
        style: {
          marginTop: 0
        },
        level: 3,
        children: item.children
      }, index);
    }
    if (item.valueType === "h2") {
      return (0, import_jsx_runtime69.jsx)(typography_default.Title, {
        style: {
          marginTop: 20
        },
        level: 5,
        children: item.children
      }, index);
    }
    if (item.valueType === "image") {
      return (0, import_jsx_runtime69.jsx)("div", {
        style: {
          marginBlock: 12
        },
        children: (0, import_jsx_runtime69.jsx)(image_default, _objectSpread2({}, item.children))
      }, index);
    }
    if (item.valueType === "inlineLink") {
      return (0, import_jsx_runtime69.jsx)(typography_default.Text, {
        children: (0, import_jsx_runtime69.jsx)("a", _objectSpread2({}, item.children))
      }, index);
    }
    if (item.valueType === "link") {
      return (0, import_jsx_runtime69.jsx)("div", {
        children: (0, import_jsx_runtime69.jsx)(typography_default.Text, {
          children: (0, import_jsx_runtime69.jsx)("a", _objectSpread2({}, item.children))
        }, index)
      }, index);
    }
    if (item.valueType === "navigationSwitch") {
      return (0, import_jsx_runtime69.jsx)(NavigationSwitch, _objectSpread2({}, item.children), index);
    }
    return (0, import_jsx_runtime69.jsx)(typography_default.Text, {
      children: item.children
    }, index);
  };
  return (0, import_jsx_runtime69.jsx)("div", {
    ref: divRef,
    children: dataSourceChildren === null || dataSourceChildren === void 0 ? void 0 : dataSourceChildren.map(itemRender)
  });
};

// node_modules/@ant-design/pro-layout/es/components/Help/AsyncContentPanel.js
var import_jsx_runtime70 = __toESM(require_jsx_runtime());
var AsyncContentPanel = function AsyncContentPanel2(_ref) {
  var item = _ref.item, _onInit = _ref.onInit;
  var _useContext = (0, import_react34.useContext)(ProHelpProvide), onLoadContext = _useContext.onLoadContext;
  var _useState = (0, import_react34.useState)(false), _useState2 = _slicedToArray(_useState, 2), loading = _useState2[0], setLoading = _useState2[1];
  var _useState3 = (0, import_react34.useState)(), _useState4 = _slicedToArray(_useState3, 2), content = _useState4[0], setContent = _useState4[1];
  (0, import_react34.useEffect)(function() {
    if (!item.key)
      return;
    setLoading(true);
    onLoadContext === null || onLoadContext === void 0 || onLoadContext(item.key, item).then(function(res) {
      setLoading(false);
      setContent(res);
    });
  }, [item.key]);
  if (!item.key)
    return null;
  if (loading && item.key) {
    return (0, import_jsx_runtime70.jsx)("div", {
      style: {
        display: "flex",
        justifyContent: "center",
        width: "100%",
        boxSizing: "border-box",
        padding: 24
      },
      children: (0, import_jsx_runtime70.jsx)(spin_default, {})
    }, item.key);
  }
  return (0, import_jsx_runtime70.jsx)(RenderContentPanel, {
    onInit: function onInit(ref) {
      _onInit === null || _onInit === void 0 || _onInit(ref);
    },
    dataSourceChildren: content
  });
};

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpContentPanel.js
var import_jsx_runtime71 = __toESM(require_jsx_runtime());
var ProHelpContentPanel = function ProHelpContentPanel2(_ref) {
  var className = _ref.className, parentItem = _ref.parentItem, selectedKey = _ref.selectedKey, onScroll = _ref.onScroll;
  var _useContext = (0, import_react35.useContext)(ProHelpProvide), dataSource = _useContext.dataSource;
  var _useContext2 = (0, import_react35.useContext)(ProProvider), hashId = _useContext2.hashId;
  var scrollHeightMap = (0, import_react35.useRef)(/* @__PURE__ */ new Map());
  var divRef = (0, import_react35.useRef)(null);
  (0, import_react35.useEffect)(function() {
    if (!selectedKey || !(parentItem !== null && parentItem !== void 0 && parentItem.infiniteScrollFull))
      return;
    var div = scrollHeightMap.current.get(selectedKey);
    if (div !== null && div !== void 0 && div.offsetTop && divRef.current) {
      if (Math.abs(divRef.current.scrollTop - (div === null || div === void 0 ? void 0 : div.offsetTop) + 40) > (div === null || div === void 0 ? void 0 : div.clientHeight)) {
        divRef.current.scrollTop = (div === null || div === void 0 ? void 0 : div.offsetTop) - 40;
      }
    }
  }, [selectedKey]);
  var onScrollEvent = useDebounceFn(function() {
    var _ref2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(e) {
      var dom, list;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              dom = e === null || e === void 0 ? void 0 : e.target;
              list = Array.from(scrollHeightMap.current.entries()).find(function(_ref3) {
                var _ref4 = _slicedToArray(_ref3, 2), value = _ref4[1];
                if ((dom === null || dom === void 0 ? void 0 : dom.scrollTop) < value.offsetTop) {
                  return true;
                }
                return false;
              });
              if (list) {
                _context.next = 4;
                break;
              }
              return _context.abrupt("return");
            case 4:
              if (list.at(0) !== selectedKey) {
                onScroll === null || onScroll === void 0 || onScroll(list.at(0));
              }
            case 5:
            case "end":
              return _context.stop();
          }
      }, _callee);
    }));
    return function(_x) {
      return _ref2.apply(this, arguments);
    };
  }(), 200);
  (0, import_react35.useEffect)(function() {
    var _divRef$current;
    if (!(parentItem !== null && parentItem !== void 0 && parentItem.infiniteScrollFull))
      return;
    onScrollEvent.cancel();
    (_divRef$current = divRef.current) === null || _divRef$current === void 0 || _divRef$current.addEventListener("scroll", onScrollEvent.run, false);
    return function() {
      var _divRef$current2;
      onScrollEvent.cancel();
      (_divRef$current2 = divRef.current) === null || _divRef$current2 === void 0 || _divRef$current2.removeEventListener("scroll", onScrollEvent.run, false);
    };
  }, [parentItem === null || parentItem === void 0 ? void 0 : parentItem.infiniteScrollFull, selectedKey]);
  var dataSourceMap = (0, import_react35.useMemo)(function() {
    var map = /* @__PURE__ */ new Map();
    dataSource.forEach(function(page) {
      page.children.forEach(function(item) {
        map.set(item.key || item.title, _objectSpread2(_objectSpread2({}, item), {}, {
          parentKey: page.key
        }));
      });
    });
    return map;
  }, [dataSource]);
  var renderItem = function renderItem2(item) {
    if (item !== null && item !== void 0 && item.asyncLoad) {
      return (0, import_jsx_runtime71.jsx)("div", {
        className: (0, import_classnames20.default)(className, hashId),
        id: item.title,
        children: (0, import_jsx_runtime71.jsx)(AsyncContentPanel, {
          item,
          onInit: function onInit(ref) {
            if (!scrollHeightMap.current)
              return;
            scrollHeightMap.current.set(item.key, ref);
          }
        }, item === null || item === void 0 ? void 0 : item.key)
      });
    }
    return (0, import_jsx_runtime71.jsx)("div", {
      className: (0, import_classnames20.default)(className, hashId),
      id: item.title,
      children: (0, import_jsx_runtime71.jsx)(RenderContentPanel, {
        onInit: function onInit(ref) {
          if (!scrollHeightMap.current)
            return;
          scrollHeightMap.current.set(item.key, ref);
        },
        dataSourceChildren: (item === null || item === void 0 ? void 0 : item.children) || []
      })
    });
  };
  if (parentItem && parentItem.infiniteScrollFull) {
    var _parentItem$children;
    return (0, import_jsx_runtime71.jsx)("div", {
      ref: divRef,
      className: (0, import_classnames20.default)("".concat(className, "-infinite-scroll"), hashId),
      style: {
        overflow: "auto"
      },
      children: (_parentItem$children = parentItem.children) === null || _parentItem$children === void 0 ? void 0 : _parentItem$children.map(function(item) {
        return (0, import_jsx_runtime71.jsx)(import_react35.default.Fragment, {
          children: renderItem(item)
        }, item.key);
      })
    });
  }
  return renderItem(dataSourceMap.get(selectedKey));
};

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpDrawer.js
var import_jsx_runtime72 = __toESM(require_jsx_runtime());
var _excluded12 = ["drawerProps"];
var ProHelpDrawer = function ProHelpDrawer2(_ref) {
  var drawerProps = _ref.drawerProps, props = _objectWithoutProperties(_ref, _excluded12);
  var _useMergedState = useMergedState(false, {
    value: drawerProps.open,
    onChange: drawerProps.afterOpenChange
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), drawerOpen = _useMergedState2[0], setDrawerOpen = _useMergedState2[1];
  return (0, import_jsx_runtime72.jsx)(drawer_default, _objectSpread2(_objectSpread2({
    width: 720,
    closeIcon: null,
    styles: {
      header: {
        display: "none"
      },
      body: {
        padding: 0
      }
    },
    maskClosable: true
  }, drawerProps), {}, {
    open: drawerOpen,
    onClose: function onClose() {
      return setDrawerOpen(false);
    },
    afterOpenChange: function afterOpenChange(open) {
      setDrawerOpen(open);
    },
    children: (0, import_jsx_runtime72.jsx)(ProHelpPanel, _objectSpread2(_objectSpread2({}, props), {}, {
      onClose: function onClose() {
        return setDrawerOpen(false);
      },
      bordered: false
    }))
  }));
};

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpModal.js
var import_jsx_runtime73 = __toESM(require_jsx_runtime());
var _excluded13 = ["modalProps"];
var ProHelpModal = function ProHelpModal2(_ref) {
  var modalProps = _ref.modalProps, props = _objectWithoutProperties(_ref, _excluded13);
  var _useMergedState = useMergedState(false, {
    value: modalProps === null || modalProps === void 0 ? void 0 : modalProps.open,
    onChange: modalProps === null || modalProps === void 0 ? void 0 : modalProps.afterClose
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), modalOpen = _useMergedState2[0], setModalOpen = _useMergedState2[1];
  return (0, import_jsx_runtime73.jsx)(modal_default, _objectSpread2(_objectSpread2({
    onCancel: function onCancel() {
      setModalOpen(false);
    },
    styles: {
      body: {
        margin: -24
      }
    },
    centered: true,
    closable: false,
    footer: null,
    width: 720,
    open: modalOpen,
    maskClosable: true
  }, modalProps), {}, {
    children: (0, import_jsx_runtime73.jsx)(ProHelpPanel, _objectSpread2(_objectSpread2({
      height: 648
    }, props), {}, {
      onClose: function onClose() {
        return setModalOpen(false);
      }
    }))
  }));
};

// node_modules/@ant-design/pro-layout/es/components/Help/ProHelpPopover.js
var import_classnames21 = __toESM(require_classnames());
var import_react36 = __toESM(require_react());
var import_jsx_runtime74 = __toESM(require_jsx_runtime());
var ProHelpPopover = function ProHelpPopover2(props) {
  var _useContext = (0, import_react36.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var className = getPrefixCls("pro-help");
  var _useContext2 = (0, import_react36.useContext)(ProProvider), hashId = _useContext2.hashId;
  var _useStyle = useStyle17(className), wrapSSR = _useStyle.wrapSSR;
  return wrapSSR((0, import_jsx_runtime74.jsx)(popover_default, _objectSpread2(_objectSpread2({
    overlayInnerStyle: {
      padding: 0
    },
    content: (0, import_jsx_runtime74.jsx)("div", {
      className: (0, import_classnames21.default)("".concat(className, "-popover-content"), hashId, props.popoverContextClassName),
      children: (0, import_jsx_runtime74.jsx)(ProHelpContentPanel, {
        selectedKey: props.selectedKey
      })
    })
  }, props.popoverProps), {}, {
    children: (0, import_jsx_runtime74.jsx)("span", {
      className: (0, import_classnames21.default)("".concat(className, "-popover-text"), hashId, props.textClassName),
      children: props.children
    })
  })));
};

// node_modules/@ant-design/pro-layout/es/components/Help/index.js
var _excluded14 = ["dataSource", "valueTypeMap", "onLoadContext"];
var ProHelp = function ProHelp2(_ref) {
  var dataSource = _ref.dataSource, _ref$valueTypeMap = _ref.valueTypeMap, valueTypeMap = _ref$valueTypeMap === void 0 ? /* @__PURE__ */ new Map() : _ref$valueTypeMap, onLoadContext = _ref.onLoadContext, props = _objectWithoutProperties(_ref, _excluded14);
  return (0, import_jsx_runtime75.jsx)(ProHelpProvide.Provider, {
    value: {
      onLoadContext,
      dataSource,
      valueTypeMap
    },
    children: props.children
  });
};

// node_modules/@ant-design/pro-layout/es/index.js
var es_default2 = ProLayout;

export {
  FooterToolbar,
  RouteContext,
  GridContent,
  PageHeader,
  PageLoading,
  WaterMark,
  ProBreadcrumb,
  PageContainer,
  ProPageHeader,
  DefaultFooter,
  TopNavHeader,
  DefaultHeader,
  SettingDrawer,
  getPageTitle,
  getMenuData,
  ProLayout,
  ProHelpProvide,
  ProHelpSelect,
  SelectKeyProvide,
  ProHelpPanel,
  RenderContentPanel,
  ProHelpContentPanel,
  ProHelpDrawer,
  ProHelpModal,
  ProHelpPopover,
  ProHelp,
  es_default2 as es_default
};
//# sourceMappingURL=chunk-CS7PLJDG.js.map
