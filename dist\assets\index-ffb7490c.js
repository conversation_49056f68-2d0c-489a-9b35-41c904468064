import{m as l,F as d,j as p,P as x,a as e,b as R,B as v,c as P,M as T,S as h,I as m,d as C,e as D,C as M}from"./antd-vendor-31ba1b84.js";import{c as F,r as f,h as j,i as B,R as L,B as N}from"./react-vendor-0893a40c.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))c(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const u of a.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&c(u)}).observe(document,{childList:!0,subtree:!0});function i(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function c(r){if(r.ep)return;r.ep=!0;const a=i(r);fetch(r.href,a)}})();var y={},w=F;y.createRoot=w.createRoot,y.hydrateRoot=w.hydrateRoot;const S="/api/GlobalData";async function q(n){try{const t=await fetch(S,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!t.ok)throw new Error("创建数据失败");return await t.json()}catch(t){throw l.error("创建数据失败"),t}}async function G(n,t){try{const i=await fetch(`${S}/${n}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!i.ok)throw new Error("更新数据失败");return await i.json()}catch(i){throw l.error("更新数据失败"),i}}async function U(n){try{if(!(await fetch(`${S}/${n}`,{method:"DELETE"})).ok)throw new Error("删除数据失败")}catch(t){throw l.error("删除数据失败"),t}}const $=()=>{const n=f.useRef(),[t]=d.useForm(),[i,c]=f.useState(!1),[r,a]=f.useState(null),u=[{title:"ID",dataIndex:"id",hideInForm:!0},{title:"类型",dataIndex:"type",valueEnum:{SYSTEM:{text:"系统"},USER:{text:"用户"},BUSINESS:{text:"业务"}}},{title:"代码",dataIndex:"code"},{title:"名称",dataIndex:"name"},{title:"值",dataIndex:"value"},{title:"描述",dataIndex:"description",hideInTable:!0},{title:"操作",valueType:"option",render:(s,o)=>[e("a",{onClick:()=>I(o),children:"编辑"},"edit"),e(C,{title:"确定要删除这条数据吗？",onConfirm:()=>E(o.id),children:e("a",{children:"删除"})},"delete")]}],I=async s=>{a(s),t.setFieldsValue(s),c(!0)},E=async s=>{var o;try{await U(s),l.success("删除成功"),(o=n.current)==null||o.reload()}catch{l.error("删除失败")}};return p(x,{children:[e(R,{headerTitle:"全局数据列表",actionRef:n,rowKey:"id",search:{labelWidth:120},toolBarRender:()=>[e(v,{icon:e(P,{}),type:"primary",onClick:()=>{a(null),t.resetFields(),c(!0)},children:"新建"},"button")],request:async s=>{const{current:o,pageSize:b,...O}=s,g=await(await fetch(`/api/GlobalData?pageNumber=${o}&pageSize=${b}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(O)})).json();return{data:g.items,success:!0,total:g.totalCount}},columns:u}),e(T,{title:r?"编辑数据":"新建数据",open:i,onOk:async()=>{var s;try{const o=await t.validateFields();r?(await G(r.id,o),l.success("更新成功")):(await q(o),l.success("创建成功")),c(!1),t.resetFields(),a(null),(s=n.current)==null||s.reload()}catch{l.error("操作失败")}},onCancel:()=>{c(!1),t.resetFields(),a(null)},destroyOnClose:!0,children:p(d,{form:t,layout:"vertical",children:[e(d.Item,{name:"type",label:"类型",rules:[{required:!0,message:"请选择类型"}],children:p(h,{children:[e(h.Option,{value:"SYSTEM",children:"系统"}),e(h.Option,{value:"USER",children:"用户"}),e(h.Option,{value:"BUSINESS",children:"业务"})]})}),e(d.Item,{name:"code",label:"代码",rules:[{required:!0,message:"请输入代码"}],children:e(m,{})}),e(d.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:e(m,{})}),e(d.Item,{name:"value",label:"值",rules:[{required:!0,message:"请输入值"}],children:e(m,{})}),e(d.Item,{name:"description",label:"描述",children:e(m.TextArea,{})})]})})]})},A=()=>e(D,{title:"全局数据管理系统",logo:null,layout:"mix",navTheme:"realDark",fixedHeader:!0,fixSiderbar:!0,children:e(j,{children:e(B,{path:"/",element:e($,{})})})});y.createRoot(document.getElementById("root")).render(e(L.StrictMode,{children:e(M,{children:e(N,{children:e(A,{})})})}));
