import {
  _assertThisInitialized,
  _asyncToGenerator,
  _classCallCheck,
  _createClass,
  _createForOfIterator<PERSON>elper,
  _createSuper,
  _inherits,
  _regeneratorRuntime,
  _toArray,
  _toConsumableArray,
  badge_default,
  button_default,
  commonLocale,
  config_provider_default,
  createTheme,
  form_default,
  get,
  menu_default,
  message_default,
  popconfirm_default,
  popover_default,
  require_customParseFormat,
  require_dayjs_min,
  result_default,
  set,
  space_default,
  theme_default,
  tooltip_default,
  typography_default,
  useCacheToken,
  useLazyKVMap_default,
  useMergedState,
  useStyleRegister,
  version_default,
  zh_CN_default
} from "./chunk-26W7M2TV.js";
import {
  require_jsx_runtime
} from "./chunk-YUEMZWDV.js";
import {
  CloseCircleFilled_default,
  DownOutlined_default,
  InfoCircleOutlined_default,
  LoadingOutlined_default,
  _defineProperty,
  _objectSpread2,
  _objectWithoutProperties,
  _slicedToArray,
  _typeof,
  init_defineProperty,
  init_typeof,
  noteOnce,
  require_classnames
} from "./chunk-V74HSD5Q.js";
import {
  __commonJS,
  __export,
  __toESM,
  require_react
} from "./chunk-5A2KPB3J.js";

// node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js
var require_use_sync_external_store_shim_development = __commonJS({
  "node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"(exports2) {
    "use strict";
    (function() {
      function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
      }
      function useSyncExternalStore$2(subscribe, getSnapshot) {
        didWarnOld18Alpha || void 0 === React17.startTransition || (didWarnOld18Alpha = true, console.error(
          "You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."
        ));
        var value = getSnapshot();
        if (!didWarnUncachedGetSnapshot) {
          var cachedValue = getSnapshot();
          objectIs(value, cachedValue) || (console.error(
            "The result of getSnapshot should be cached to avoid an infinite loop"
          ), didWarnUncachedGetSnapshot = true);
        }
        cachedValue = useState9({
          inst: { value, getSnapshot }
        });
        var inst = cachedValue[0].inst, forceUpdate = cachedValue[1];
        useLayoutEffect3(
          function() {
            inst.value = value;
            inst.getSnapshot = getSnapshot;
            checkIfSnapshotChanged(inst) && forceUpdate({ inst });
          },
          [subscribe, value, getSnapshot]
        );
        useEffect13(
          function() {
            checkIfSnapshotChanged(inst) && forceUpdate({ inst });
            return subscribe(function() {
              checkIfSnapshotChanged(inst) && forceUpdate({ inst });
            });
          },
          [subscribe]
        );
        useDebugValue2(value);
        return value;
      }
      function checkIfSnapshotChanged(inst) {
        var latestGetSnapshot = inst.getSnapshot;
        inst = inst.value;
        try {
          var nextValue = latestGetSnapshot();
          return !objectIs(inst, nextValue);
        } catch (error2) {
          return true;
        }
      }
      function useSyncExternalStore$1(subscribe, getSnapshot) {
        return getSnapshot();
      }
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
      var React17 = require_react(), objectIs = "function" === typeof Object.is ? Object.is : is, useState9 = React17.useState, useEffect13 = React17.useEffect, useLayoutEffect3 = React17.useLayoutEffect, useDebugValue2 = React17.useDebugValue, didWarnOld18Alpha = false, didWarnUncachedGetSnapshot = false, shim = "undefined" === typeof window || "undefined" === typeof window.document || "undefined" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;
      exports2.useSyncExternalStore = void 0 !== React17.useSyncExternalStore ? React17.useSyncExternalStore : shim;
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
    })();
  }
});

// node_modules/use-sync-external-store/shim/index.js
var require_shim = __commonJS({
  "node_modules/use-sync-external-store/shim/index.js"(exports2, module2) {
    "use strict";
    if (false) {
      module2.exports = null;
    } else {
      module2.exports = require_use_sync_external_store_shim_development();
    }
  }
});

// node_modules/dayjs/locale/zh-cn.js
var require_zh_cn = __commonJS({
  "node_modules/dayjs/locale/zh-cn.js"(exports2, module2) {
    !function(e, _) {
      "object" == typeof exports2 && "undefined" != typeof module2 ? module2.exports = _(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], _) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_locale_zh_cn = _(e.dayjs);
    }(exports2, function(e) {
      "use strict";
      function _(e2) {
        return e2 && "object" == typeof e2 && "default" in e2 ? e2 : { default: e2 };
      }
      var t = _(e), d = { name: "zh-cn", weekdays: "星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"), weekdaysShort: "周日_周一_周二_周三_周四_周五_周六".split("_"), weekdaysMin: "日_一_二_三_四_五_六".split("_"), months: "一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"), monthsShort: "1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"), ordinal: function(e2, _2) {
        return "W" === _2 ? e2 + "周" : e2 + "日";
      }, weekStart: 1, yearStart: 4, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "YYYY/MM/DD", LL: "YYYY年M月D日", LLL: "YYYY年M月D日Ah点mm分", LLLL: "YYYY年M月D日ddddAh点mm分", l: "YYYY/M/D", ll: "YYYY年M月D日", lll: "YYYY年M月D日 HH:mm", llll: "YYYY年M月D日dddd HH:mm" }, relativeTime: { future: "%s内", past: "%s前", s: "几秒", m: "1 分钟", mm: "%d 分钟", h: "1 小时", hh: "%d 小时", d: "1 天", dd: "%d 天", M: "1 个月", MM: "%d 个月", y: "1 年", yy: "%d 年" }, meridiem: function(e2, _2) {
        var t2 = 100 * e2 + _2;
        return t2 < 600 ? "凌晨" : t2 < 900 ? "早上" : t2 < 1100 ? "上午" : t2 < 1300 ? "中午" : t2 < 1800 ? "下午" : "晚上";
      } };
      return t.default.locale(d, null, true), d;
    });
  }
});

// node_modules/dayjs/plugin/quarterOfYear.js
var require_quarterOfYear = __commonJS({
  "node_modules/dayjs/plugin/quarterOfYear.js"(exports2, module2) {
    !function(t, n) {
      "object" == typeof exports2 && "undefined" != typeof module2 ? module2.exports = n() : "function" == typeof define && define.amd ? define(n) : (t = "undefined" != typeof globalThis ? globalThis : t || self).dayjs_plugin_quarterOfYear = n();
    }(exports2, function() {
      "use strict";
      var t = "month", n = "quarter";
      return function(e, i) {
        var r = i.prototype;
        r.quarter = function(t2) {
          return this.$utils().u(t2) ? Math.ceil((this.month() + 1) / 3) : this.month(this.month() % 3 + 3 * (t2 - 1));
        };
        var s = r.add;
        r.add = function(e2, i2) {
          return e2 = Number(e2), this.$utils().p(i2) === n ? this.add(3 * e2, t) : s.bind(this)(e2, i2);
        };
        var u = r.startOf;
        r.startOf = function(e2, i2) {
          var r2 = this.$utils(), s2 = !!r2.u(i2) || i2;
          if (r2.p(e2) === n) {
            var o = this.quarter() - 1;
            return s2 ? this.month(3 * o).startOf(t).startOf("day") : this.month(3 * o + 2).endOf(t).endOf("day");
          }
          return u.bind(this)(e2, i2);
        };
      };
    });
  }
});

// node_modules/safe-stable-stringify/index.js
var require_safe_stable_stringify = __commonJS({
  "node_modules/safe-stable-stringify/index.js"(exports2, module2) {
    "use strict";
    var { hasOwnProperty: hasOwnProperty10 } = Object.prototype;
    var stringify2 = configure2();
    stringify2.configure = configure2;
    stringify2.stringify = stringify2;
    stringify2.default = stringify2;
    exports2.stringify = stringify2;
    exports2.configure = configure2;
    module2.exports = stringify2;
    var strEscapeSequencesRegExp = /[\u0000-\u001f\u0022\u005c\ud800-\udfff]/;
    function strEscape(str) {
      if (str.length < 5e3 && !strEscapeSequencesRegExp.test(str)) {
        return `"${str}"`;
      }
      return JSON.stringify(str);
    }
    function sort(array, comparator) {
      if (array.length > 200 || comparator) {
        return array.sort(comparator);
      }
      for (let i = 1; i < array.length; i++) {
        const currentValue = array[i];
        let position = i;
        while (position !== 0 && array[position - 1] > currentValue) {
          array[position] = array[position - 1];
          position--;
        }
        array[position] = currentValue;
      }
      return array;
    }
    var typedArrayPrototypeGetSymbolToStringTag = Object.getOwnPropertyDescriptor(
      Object.getPrototypeOf(
        Object.getPrototypeOf(
          new Int8Array()
        )
      ),
      Symbol.toStringTag
    ).get;
    function isTypedArrayWithEntries(value) {
      return typedArrayPrototypeGetSymbolToStringTag.call(value) !== void 0 && value.length !== 0;
    }
    function stringifyTypedArray(array, separator, maximumBreadth) {
      if (array.length < maximumBreadth) {
        maximumBreadth = array.length;
      }
      const whitespace = separator === "," ? "" : " ";
      let res = `"0":${whitespace}${array[0]}`;
      for (let i = 1; i < maximumBreadth; i++) {
        res += `${separator}"${i}":${whitespace}${array[i]}`;
      }
      return res;
    }
    function getCircularValueOption(options) {
      if (hasOwnProperty10.call(options, "circularValue")) {
        const circularValue = options.circularValue;
        if (typeof circularValue === "string") {
          return `"${circularValue}"`;
        }
        if (circularValue == null) {
          return circularValue;
        }
        if (circularValue === Error || circularValue === TypeError) {
          return {
            toString() {
              throw new TypeError("Converting circular structure to JSON");
            }
          };
        }
        throw new TypeError('The "circularValue" argument must be of type string or the value null or undefined');
      }
      return '"[Circular]"';
    }
    function getDeterministicOption(options) {
      let value;
      if (hasOwnProperty10.call(options, "deterministic")) {
        value = options.deterministic;
        if (typeof value !== "boolean" && typeof value !== "function") {
          throw new TypeError('The "deterministic" argument must be of type boolean or comparator function');
        }
      }
      return value === void 0 ? true : value;
    }
    function getBooleanOption(options, key) {
      let value;
      if (hasOwnProperty10.call(options, key)) {
        value = options[key];
        if (typeof value !== "boolean") {
          throw new TypeError(`The "${key}" argument must be of type boolean`);
        }
      }
      return value === void 0 ? true : value;
    }
    function getPositiveIntegerOption(options, key) {
      let value;
      if (hasOwnProperty10.call(options, key)) {
        value = options[key];
        if (typeof value !== "number") {
          throw new TypeError(`The "${key}" argument must be of type number`);
        }
        if (!Number.isInteger(value)) {
          throw new TypeError(`The "${key}" argument must be an integer`);
        }
        if (value < 1) {
          throw new RangeError(`The "${key}" argument must be >= 1`);
        }
      }
      return value === void 0 ? Infinity : value;
    }
    function getItemCount(number) {
      if (number === 1) {
        return "1 item";
      }
      return `${number} items`;
    }
    function getUniqueReplacerSet(replacerArray) {
      const replacerSet = /* @__PURE__ */ new Set();
      for (const value of replacerArray) {
        if (typeof value === "string" || typeof value === "number") {
          replacerSet.add(String(value));
        }
      }
      return replacerSet;
    }
    function getStrictOption(options) {
      if (hasOwnProperty10.call(options, "strict")) {
        const value = options.strict;
        if (typeof value !== "boolean") {
          throw new TypeError('The "strict" argument must be of type boolean');
        }
        if (value) {
          return (value2) => {
            let message = `Object can not safely be stringified. Received type ${typeof value2}`;
            if (typeof value2 !== "function")
              message += ` (${value2.toString()})`;
            throw new Error(message);
          };
        }
      }
    }
    function configure2(options) {
      options = { ...options };
      const fail = getStrictOption(options);
      if (fail) {
        if (options.bigint === void 0) {
          options.bigint = false;
        }
        if (!("circularValue" in options)) {
          options.circularValue = Error;
        }
      }
      const circularValue = getCircularValueOption(options);
      const bigint = getBooleanOption(options, "bigint");
      const deterministic = getDeterministicOption(options);
      const comparator = typeof deterministic === "function" ? deterministic : void 0;
      const maximumDepth = getPositiveIntegerOption(options, "maximumDepth");
      const maximumBreadth = getPositiveIntegerOption(options, "maximumBreadth");
      function stringifyFnReplacer(key, parent, stack, replacer, spacer, indentation) {
        let value = parent[key];
        if (typeof value === "object" && value !== null && typeof value.toJSON === "function") {
          value = value.toJSON(key);
        }
        value = replacer.call(parent, key, value);
        switch (typeof value) {
          case "string":
            return strEscape(value);
          case "object": {
            if (value === null) {
              return "null";
            }
            if (stack.indexOf(value) !== -1) {
              return circularValue;
            }
            let res = "";
            let join = ",";
            const originalIndentation = indentation;
            if (Array.isArray(value)) {
              if (value.length === 0) {
                return "[]";
              }
              if (maximumDepth < stack.length + 1) {
                return '"[Array]"';
              }
              stack.push(value);
              if (spacer !== "") {
                indentation += spacer;
                res += `
${indentation}`;
                join = `,
${indentation}`;
              }
              const maximumValuesToStringify = Math.min(value.length, maximumBreadth);
              let i = 0;
              for (; i < maximumValuesToStringify - 1; i++) {
                const tmp2 = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation);
                res += tmp2 !== void 0 ? tmp2 : "null";
                res += join;
              }
              const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation);
              res += tmp !== void 0 ? tmp : "null";
              if (value.length - 1 > maximumBreadth) {
                const removedKeys = value.length - maximumBreadth - 1;
                res += `${join}"... ${getItemCount(removedKeys)} not stringified"`;
              }
              if (spacer !== "") {
                res += `
${originalIndentation}`;
              }
              stack.pop();
              return `[${res}]`;
            }
            let keys = Object.keys(value);
            const keyLength = keys.length;
            if (keyLength === 0) {
              return "{}";
            }
            if (maximumDepth < stack.length + 1) {
              return '"[Object]"';
            }
            let whitespace = "";
            let separator = "";
            if (spacer !== "") {
              indentation += spacer;
              join = `,
${indentation}`;
              whitespace = " ";
            }
            const maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth);
            if (deterministic && !isTypedArrayWithEntries(value)) {
              keys = sort(keys, comparator);
            }
            stack.push(value);
            for (let i = 0; i < maximumPropertiesToStringify; i++) {
              const key2 = keys[i];
              const tmp = stringifyFnReplacer(key2, value, stack, replacer, spacer, indentation);
              if (tmp !== void 0) {
                res += `${separator}${strEscape(key2)}:${whitespace}${tmp}`;
                separator = join;
              }
            }
            if (keyLength > maximumBreadth) {
              const removedKeys = keyLength - maximumBreadth;
              res += `${separator}"...":${whitespace}"${getItemCount(removedKeys)} not stringified"`;
              separator = join;
            }
            if (spacer !== "" && separator.length > 1) {
              res = `
${indentation}${res}
${originalIndentation}`;
            }
            stack.pop();
            return `{${res}}`;
          }
          case "number":
            return isFinite(value) ? String(value) : fail ? fail(value) : "null";
          case "boolean":
            return value === true ? "true" : "false";
          case "undefined":
            return void 0;
          case "bigint":
            if (bigint) {
              return String(value);
            }
          default:
            return fail ? fail(value) : void 0;
        }
      }
      function stringifyArrayReplacer(key, value, stack, replacer, spacer, indentation) {
        if (typeof value === "object" && value !== null && typeof value.toJSON === "function") {
          value = value.toJSON(key);
        }
        switch (typeof value) {
          case "string":
            return strEscape(value);
          case "object": {
            if (value === null) {
              return "null";
            }
            if (stack.indexOf(value) !== -1) {
              return circularValue;
            }
            const originalIndentation = indentation;
            let res = "";
            let join = ",";
            if (Array.isArray(value)) {
              if (value.length === 0) {
                return "[]";
              }
              if (maximumDepth < stack.length + 1) {
                return '"[Array]"';
              }
              stack.push(value);
              if (spacer !== "") {
                indentation += spacer;
                res += `
${indentation}`;
                join = `,
${indentation}`;
              }
              const maximumValuesToStringify = Math.min(value.length, maximumBreadth);
              let i = 0;
              for (; i < maximumValuesToStringify - 1; i++) {
                const tmp2 = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation);
                res += tmp2 !== void 0 ? tmp2 : "null";
                res += join;
              }
              const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation);
              res += tmp !== void 0 ? tmp : "null";
              if (value.length - 1 > maximumBreadth) {
                const removedKeys = value.length - maximumBreadth - 1;
                res += `${join}"... ${getItemCount(removedKeys)} not stringified"`;
              }
              if (spacer !== "") {
                res += `
${originalIndentation}`;
              }
              stack.pop();
              return `[${res}]`;
            }
            stack.push(value);
            let whitespace = "";
            if (spacer !== "") {
              indentation += spacer;
              join = `,
${indentation}`;
              whitespace = " ";
            }
            let separator = "";
            for (const key2 of replacer) {
              const tmp = stringifyArrayReplacer(key2, value[key2], stack, replacer, spacer, indentation);
              if (tmp !== void 0) {
                res += `${separator}${strEscape(key2)}:${whitespace}${tmp}`;
                separator = join;
              }
            }
            if (spacer !== "" && separator.length > 1) {
              res = `
${indentation}${res}
${originalIndentation}`;
            }
            stack.pop();
            return `{${res}}`;
          }
          case "number":
            return isFinite(value) ? String(value) : fail ? fail(value) : "null";
          case "boolean":
            return value === true ? "true" : "false";
          case "undefined":
            return void 0;
          case "bigint":
            if (bigint) {
              return String(value);
            }
          default:
            return fail ? fail(value) : void 0;
        }
      }
      function stringifyIndent(key, value, stack, spacer, indentation) {
        switch (typeof value) {
          case "string":
            return strEscape(value);
          case "object": {
            if (value === null) {
              return "null";
            }
            if (typeof value.toJSON === "function") {
              value = value.toJSON(key);
              if (typeof value !== "object") {
                return stringifyIndent(key, value, stack, spacer, indentation);
              }
              if (value === null) {
                return "null";
              }
            }
            if (stack.indexOf(value) !== -1) {
              return circularValue;
            }
            const originalIndentation = indentation;
            if (Array.isArray(value)) {
              if (value.length === 0) {
                return "[]";
              }
              if (maximumDepth < stack.length + 1) {
                return '"[Array]"';
              }
              stack.push(value);
              indentation += spacer;
              let res2 = `
${indentation}`;
              const join2 = `,
${indentation}`;
              const maximumValuesToStringify = Math.min(value.length, maximumBreadth);
              let i = 0;
              for (; i < maximumValuesToStringify - 1; i++) {
                const tmp2 = stringifyIndent(String(i), value[i], stack, spacer, indentation);
                res2 += tmp2 !== void 0 ? tmp2 : "null";
                res2 += join2;
              }
              const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation);
              res2 += tmp !== void 0 ? tmp : "null";
              if (value.length - 1 > maximumBreadth) {
                const removedKeys = value.length - maximumBreadth - 1;
                res2 += `${join2}"... ${getItemCount(removedKeys)} not stringified"`;
              }
              res2 += `
${originalIndentation}`;
              stack.pop();
              return `[${res2}]`;
            }
            let keys = Object.keys(value);
            const keyLength = keys.length;
            if (keyLength === 0) {
              return "{}";
            }
            if (maximumDepth < stack.length + 1) {
              return '"[Object]"';
            }
            indentation += spacer;
            const join = `,
${indentation}`;
            let res = "";
            let separator = "";
            let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth);
            if (isTypedArrayWithEntries(value)) {
              res += stringifyTypedArray(value, join, maximumBreadth);
              keys = keys.slice(value.length);
              maximumPropertiesToStringify -= value.length;
              separator = join;
            }
            if (deterministic) {
              keys = sort(keys, comparator);
            }
            stack.push(value);
            for (let i = 0; i < maximumPropertiesToStringify; i++) {
              const key2 = keys[i];
              const tmp = stringifyIndent(key2, value[key2], stack, spacer, indentation);
              if (tmp !== void 0) {
                res += `${separator}${strEscape(key2)}: ${tmp}`;
                separator = join;
              }
            }
            if (keyLength > maximumBreadth) {
              const removedKeys = keyLength - maximumBreadth;
              res += `${separator}"...": "${getItemCount(removedKeys)} not stringified"`;
              separator = join;
            }
            if (separator !== "") {
              res = `
${indentation}${res}
${originalIndentation}`;
            }
            stack.pop();
            return `{${res}}`;
          }
          case "number":
            return isFinite(value) ? String(value) : fail ? fail(value) : "null";
          case "boolean":
            return value === true ? "true" : "false";
          case "undefined":
            return void 0;
          case "bigint":
            if (bigint) {
              return String(value);
            }
          default:
            return fail ? fail(value) : void 0;
        }
      }
      function stringifySimple(key, value, stack) {
        switch (typeof value) {
          case "string":
            return strEscape(value);
          case "object": {
            if (value === null) {
              return "null";
            }
            if (typeof value.toJSON === "function") {
              value = value.toJSON(key);
              if (typeof value !== "object") {
                return stringifySimple(key, value, stack);
              }
              if (value === null) {
                return "null";
              }
            }
            if (stack.indexOf(value) !== -1) {
              return circularValue;
            }
            let res = "";
            const hasLength = value.length !== void 0;
            if (hasLength && Array.isArray(value)) {
              if (value.length === 0) {
                return "[]";
              }
              if (maximumDepth < stack.length + 1) {
                return '"[Array]"';
              }
              stack.push(value);
              const maximumValuesToStringify = Math.min(value.length, maximumBreadth);
              let i = 0;
              for (; i < maximumValuesToStringify - 1; i++) {
                const tmp2 = stringifySimple(String(i), value[i], stack);
                res += tmp2 !== void 0 ? tmp2 : "null";
                res += ",";
              }
              const tmp = stringifySimple(String(i), value[i], stack);
              res += tmp !== void 0 ? tmp : "null";
              if (value.length - 1 > maximumBreadth) {
                const removedKeys = value.length - maximumBreadth - 1;
                res += `,"... ${getItemCount(removedKeys)} not stringified"`;
              }
              stack.pop();
              return `[${res}]`;
            }
            let keys = Object.keys(value);
            const keyLength = keys.length;
            if (keyLength === 0) {
              return "{}";
            }
            if (maximumDepth < stack.length + 1) {
              return '"[Object]"';
            }
            let separator = "";
            let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth);
            if (hasLength && isTypedArrayWithEntries(value)) {
              res += stringifyTypedArray(value, ",", maximumBreadth);
              keys = keys.slice(value.length);
              maximumPropertiesToStringify -= value.length;
              separator = ",";
            }
            if (deterministic) {
              keys = sort(keys, comparator);
            }
            stack.push(value);
            for (let i = 0; i < maximumPropertiesToStringify; i++) {
              const key2 = keys[i];
              const tmp = stringifySimple(key2, value[key2], stack);
              if (tmp !== void 0) {
                res += `${separator}${strEscape(key2)}:${tmp}`;
                separator = ",";
              }
            }
            if (keyLength > maximumBreadth) {
              const removedKeys = keyLength - maximumBreadth;
              res += `${separator}"...":"${getItemCount(removedKeys)} not stringified"`;
            }
            stack.pop();
            return `{${res}}`;
          }
          case "number":
            return isFinite(value) ? String(value) : fail ? fail(value) : "null";
          case "boolean":
            return value === true ? "true" : "false";
          case "undefined":
            return void 0;
          case "bigint":
            if (bigint) {
              return String(value);
            }
          default:
            return fail ? fail(value) : void 0;
        }
      }
      function stringify3(value, replacer, space) {
        if (arguments.length > 1) {
          let spacer = "";
          if (typeof space === "number") {
            spacer = " ".repeat(Math.min(space, 10));
          } else if (typeof space === "string") {
            spacer = space.slice(0, 10);
          }
          if (replacer != null) {
            if (typeof replacer === "function") {
              return stringifyFnReplacer("", { "": value }, [], replacer, spacer, "");
            }
            if (Array.isArray(replacer)) {
              return stringifyArrayReplacer("", value, [], getUniqueReplacerSet(replacer), spacer, "");
            }
          }
          if (spacer.length !== 0) {
            return stringifyIndent("", value, [], spacer, "");
          }
        }
        return stringifySimple("", value, []);
      }
      return stringify3;
    }
  }
});

// node_modules/@ant-design/pro-provider/es/locale/ar_EG.js
var ar_EG_default = {
  moneySymbol: "$",
  form: {
    lightFilter: {
      more: "المزيد",
      clear: "نظف",
      confirm: "تأكيد",
      itemUnit: "عناصر"
    }
  },
  tableForm: {
    search: "ابحث",
    reset: "إعادة تعيين",
    submit: "ارسال",
    collapsed: "مُقلص",
    expand: "مُوسع",
    inputPlaceholder: "الرجاء الإدخال",
    selectPlaceholder: "الرجاء الإختيار"
  },
  alert: {
    clear: "نظف",
    selected: "محدد",
    item: "عنصر"
  },
  pagination: {
    total: {
      range: " ",
      total: "من",
      item: "عناصر"
    }
  },
  tableToolBar: {
    leftPin: "ثبت على اليسار",
    rightPin: "ثبت على اليمين",
    noPin: "الغاء التثبيت",
    leftFixedTitle: "لصق على اليسار",
    rightFixedTitle: "لصق على اليمين",
    noFixedTitle: "إلغاء الإلصاق",
    reset: "إعادة تعيين",
    columnDisplay: "الأعمدة المعروضة",
    columnSetting: "الإعدادات",
    fullScreen: "وضع كامل الشاشة",
    exitFullScreen: "الخروج من وضع كامل الشاشة",
    reload: "تحديث",
    density: "الكثافة",
    densityDefault: "افتراضي",
    densityLarger: "أكبر",
    densityMiddle: "وسط",
    densitySmall: "مدمج"
  },
  stepsForm: {
    next: "التالي",
    prev: "السابق",
    submit: "أنهى"
  },
  loginForm: {
    submitText: "تسجيل الدخول"
  },
  editableTable: {
    action: {
      save: "أنقذ",
      cancel: "إلغاء الأمر",
      delete: "حذف",
      add: "إضافة صف من البيانات"
    }
  },
  switch: {
    open: "مفتوح",
    close: "غلق"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/ca_ES.js
var ca_ES_default = {
  moneySymbol: "€",
  form: {
    lightFilter: {
      more: "Més",
      clear: "Netejar",
      confirm: "Confirmar",
      itemUnit: "Elements"
    }
  },
  tableForm: {
    search: "Cercar",
    reset: "Netejar",
    submit: "Enviar",
    collapsed: "Expandir",
    expand: "Col·lapsar",
    inputPlaceholder: "Introduïu valor",
    selectPlaceholder: "Seleccioneu valor"
  },
  alert: {
    clear: "Netejar",
    selected: "Seleccionat",
    item: "Article"
  },
  pagination: {
    total: {
      range: " ",
      total: "de",
      item: "articles"
    }
  },
  tableToolBar: {
    leftPin: "Pin a l'esquerra",
    rightPin: "Pin a la dreta",
    noPin: "Sense Pin",
    leftFixedTitle: "Fixat a l'esquerra",
    rightFixedTitle: "Fixat a la dreta",
    noFixedTitle: "Sense fixar",
    reset: "Reiniciar",
    columnDisplay: "Mostrar Columna",
    columnSetting: "Configuració",
    fullScreen: "Pantalla Completa",
    exitFullScreen: "Sortir Pantalla Completa",
    reload: "Refrescar",
    density: "Densitat",
    densityDefault: "Per Defecte",
    densityLarger: "Llarg",
    densityMiddle: "Mitjà",
    densitySmall: "Compacte"
  },
  stepsForm: {
    next: "Següent",
    prev: "Anterior",
    submit: "Finalizar"
  },
  loginForm: {
    submitText: "Entrar"
  },
  editableTable: {
    action: {
      save: "Guardar",
      cancel: "Cancel·lar",
      delete: "Eliminar",
      add: "afegir una fila de dades"
    }
  },
  switch: {
    open: "obert",
    close: "tancat"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/cs_CZ.js
var cs_CZ_default = {
  moneySymbol: "Kč",
  deleteThisLine: "Smazat tento řádek",
  copyThisLine: "Kopírovat tento řádek",
  form: {
    lightFilter: {
      more: "Víc",
      clear: "Vymazat",
      confirm: "Potvrdit",
      itemUnit: "Položky"
    }
  },
  tableForm: {
    search: "Dotaz",
    reset: "Resetovat",
    submit: "Odeslat",
    collapsed: "Zvětšit",
    expand: "Zmenšit",
    inputPlaceholder: "Zadejte prosím",
    selectPlaceholder: "Vyberte prosím"
  },
  alert: {
    clear: "Vymazat",
    selected: "Vybraný",
    item: "Položka"
  },
  pagination: {
    total: {
      range: " ",
      total: "z",
      item: "položek"
    }
  },
  tableToolBar: {
    leftPin: "Připnout doleva",
    rightPin: "Připnout doprava",
    noPin: "Odepnuto",
    leftFixedTitle: "Fixováno nalevo",
    rightFixedTitle: "Fixováno napravo",
    noFixedTitle: "Neopraveno",
    reset: "Resetovat",
    columnDisplay: "Zobrazení sloupců",
    columnSetting: "Nastavení",
    fullScreen: "Celá obrazovka",
    exitFullScreen: "Ukončete celou obrazovku",
    reload: "Obnovit",
    density: "Hustota",
    densityDefault: "Výchozí",
    densityLarger: "Větší",
    densityMiddle: "Střední",
    densitySmall: "Kompaktní"
  },
  stepsForm: {
    next: "Další",
    prev: "Předchozí",
    submit: "Dokončit"
  },
  loginForm: {
    submitText: "Přihlásit se"
  },
  editableTable: {
    onlyOneLineEditor: "Upravit lze pouze jeden řádek",
    action: {
      save: "Uložit",
      cancel: "Zrušit",
      delete: "Vymazat",
      add: "přidat řádek dat"
    }
  },
  switch: {
    open: "otevřít",
    close: "zavřít"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/de_DE.js
var de_DE_default = {
  moneySymbol: "€",
  form: {
    lightFilter: {
      more: "Mehr",
      clear: "Zurücksetzen",
      confirm: "Bestätigen",
      itemUnit: "Einträge"
    }
  },
  tableForm: {
    search: "Suchen",
    reset: "Zurücksetzen",
    submit: "Absenden",
    collapsed: "Zeige mehr",
    expand: "Zeige weniger",
    inputPlaceholder: "Bitte eingeben",
    selectPlaceholder: "Bitte auswählen"
  },
  alert: {
    clear: "Zurücksetzen",
    selected: "Ausgewählt",
    item: "Eintrag"
  },
  pagination: {
    total: {
      range: " ",
      total: "von",
      item: "Einträgen"
    }
  },
  tableToolBar: {
    leftPin: "Links anheften",
    rightPin: "Rechts anheften",
    noPin: "Nicht angeheftet",
    leftFixedTitle: "Links fixiert",
    rightFixedTitle: "Rechts fixiert",
    noFixedTitle: "Nicht fixiert",
    reset: "Zurücksetzen",
    columnDisplay: "Angezeigte Reihen",
    columnSetting: "Einstellungen",
    fullScreen: "Vollbild",
    exitFullScreen: "Vollbild verlassen",
    reload: "Aktualisieren",
    density: "Abstand",
    densityDefault: "Standard",
    densityLarger: "Größer",
    densityMiddle: "Mittel",
    densitySmall: "Kompakt"
  },
  stepsForm: {
    next: "Weiter",
    prev: "Zurück",
    submit: "Abschließen"
  },
  loginForm: {
    submitText: "Anmelden"
  },
  editableTable: {
    action: {
      save: "Retten",
      cancel: "Abbrechen",
      delete: "Löschen",
      add: "Hinzufügen einer Datenzeile"
    }
  },
  switch: {
    open: "offen",
    close: "schließen"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/en_GB.js
var en_GB_default = {
  moneySymbol: "£",
  form: {
    lightFilter: {
      more: "More",
      clear: "Clear",
      confirm: "Confirm",
      itemUnit: "Items"
    }
  },
  tableForm: {
    search: "Query",
    reset: "Reset",
    submit: "Submit",
    collapsed: "Expand",
    expand: "Collapse",
    inputPlaceholder: "Please enter",
    selectPlaceholder: "Please select"
  },
  alert: {
    clear: "Clear",
    selected: "Selected",
    item: "Item"
  },
  pagination: {
    total: {
      range: " ",
      total: "of",
      item: "items"
    }
  },
  tableToolBar: {
    leftPin: "Pin to left",
    rightPin: "Pin to right",
    noPin: "Unpinned",
    leftFixedTitle: "Fixed to the left",
    rightFixedTitle: "Fixed to the right",
    noFixedTitle: "Not Fixed",
    reset: "Reset",
    columnDisplay: "Column Display",
    columnSetting: "Table Settings",
    fullScreen: "Full Screen",
    exitFullScreen: "Exit Full Screen",
    reload: "Refresh",
    density: "Density",
    densityDefault: "Default",
    densityLarger: "Larger",
    densityMiddle: "Middle",
    densitySmall: "Compact"
  },
  stepsForm: {
    next: "Next",
    prev: "Previous",
    submit: "Finish"
  },
  loginForm: {
    submitText: "Login"
  },
  editableTable: {
    onlyOneLineEditor: "Only one line can be edited",
    onlyAddOneLine: "Only one line can be added",
    action: {
      save: "Save",
      cancel: "Cancel",
      delete: "Delete",
      add: "add a row of data"
    }
  },
  switch: {
    open: "open",
    close: "close"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/en_US.js
var en_US_default = {
  moneySymbol: "$",
  deleteThisLine: "Delete this line",
  copyThisLine: "Copy this line",
  form: {
    lightFilter: {
      more: "More",
      clear: "Clear",
      confirm: "Confirm",
      itemUnit: "Items"
    }
  },
  tableForm: {
    search: "Query",
    reset: "Reset",
    submit: "Submit",
    collapsed: "Expand",
    expand: "Collapse",
    inputPlaceholder: "Please enter",
    selectPlaceholder: "Please select"
  },
  alert: {
    clear: "Clear",
    selected: "Selected",
    item: "Item"
  },
  pagination: {
    total: {
      range: " ",
      total: "of",
      item: "items"
    }
  },
  tableToolBar: {
    leftPin: "Pin to left",
    rightPin: "Pin to right",
    noPin: "Unpinned",
    leftFixedTitle: "Fixed to the left",
    rightFixedTitle: "Fixed to the right",
    noFixedTitle: "Not Fixed",
    reset: "Reset",
    columnDisplay: "Column Display",
    columnSetting: "Table Settings",
    fullScreen: "Full Screen",
    exitFullScreen: "Exit Full Screen",
    reload: "Refresh",
    density: "Density",
    densityDefault: "Default",
    densityLarger: "Larger",
    densityMiddle: "Middle",
    densitySmall: "Compact"
  },
  stepsForm: {
    next: "Next",
    prev: "Previous",
    submit: "Finish"
  },
  loginForm: {
    submitText: "Login"
  },
  editableTable: {
    onlyOneLineEditor: "Only one line can be edited",
    onlyAddOneLine: "Only one line can be added",
    action: {
      save: "Save",
      cancel: "Cancel",
      delete: "Delete",
      add: "add a row of data"
    }
  },
  switch: {
    open: "open",
    close: "close"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/es_ES.js
var es_ES_default = {
  moneySymbol: "€",
  form: {
    lightFilter: {
      more: "Más",
      clear: "Limpiar",
      confirm: "Confirmar",
      itemUnit: "artículos"
    }
  },
  tableForm: {
    search: "Buscar",
    reset: "Limpiar",
    submit: "Submit",
    collapsed: "Expandir",
    expand: "Colapsar",
    inputPlaceholder: "Ingrese valor",
    selectPlaceholder: "Seleccione valor"
  },
  alert: {
    clear: "Limpiar",
    selected: "Seleccionado",
    item: "Articulo"
  },
  pagination: {
    total: {
      range: " ",
      total: "de",
      item: "artículos"
    }
  },
  tableToolBar: {
    leftPin: "Pin a la izquierda",
    rightPin: "Pin a la derecha",
    noPin: "Sin Pin",
    leftFixedTitle: "Fijado a la izquierda",
    rightFixedTitle: "Fijado a la derecha",
    noFixedTitle: "Sin Fijar",
    reset: "Reiniciar",
    columnDisplay: "Mostrar Columna",
    columnSetting: "Configuración",
    fullScreen: "Pantalla Completa",
    exitFullScreen: "Salir Pantalla Completa",
    reload: "Refrescar",
    density: "Densidad",
    densityDefault: "Por Defecto",
    densityLarger: "Largo",
    densityMiddle: "Medio",
    densitySmall: "Compacto"
  },
  stepsForm: {
    next: "Siguiente",
    prev: "Anterior",
    submit: "Finalizar"
  },
  loginForm: {
    submitText: "Entrar"
  },
  editableTable: {
    action: {
      save: "Guardar",
      cancel: "Descartar",
      delete: "Borrar",
      add: "añadir una fila de datos"
    }
  },
  switch: {
    open: "abrir",
    close: "cerrar"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/fa_IR.js
var fa_IR_default = {
  moneySymbol: "تومان",
  form: {
    lightFilter: {
      more: "بیشتر",
      clear: "پاک کردن",
      confirm: "تایید",
      itemUnit: "مورد"
    }
  },
  tableForm: {
    search: "جستجو",
    reset: "بازنشانی",
    submit: "تایید",
    collapsed: "نمایش بیشتر",
    expand: "نمایش کمتر",
    inputPlaceholder: "پیدا کنید",
    selectPlaceholder: "انتخاب کنید"
  },
  alert: {
    clear: "پاک سازی",
    selected: "انتخاب",
    item: "مورد"
  },
  pagination: {
    total: {
      range: " ",
      total: "از",
      item: "مورد"
    }
  },
  tableToolBar: {
    leftPin: "سنجاق به چپ",
    rightPin: "سنجاق به راست",
    noPin: "سنجاق نشده",
    leftFixedTitle: "ثابت شده در چپ",
    rightFixedTitle: "ثابت شده در راست",
    noFixedTitle: "شناور",
    reset: "بازنشانی",
    columnDisplay: "نمایش همه",
    columnSetting: "تنظیمات",
    fullScreen: "تمام صفحه",
    exitFullScreen: "خروج از حالت تمام صفحه",
    reload: "تازه سازی",
    density: "تراکم",
    densityDefault: "پیش فرض",
    densityLarger: "بزرگ",
    densityMiddle: "متوسط",
    densitySmall: "کوچک"
  },
  stepsForm: {
    next: "بعدی",
    prev: "قبلی",
    submit: "اتمام"
  },
  loginForm: {
    submitText: "ورود"
  },
  editableTable: {
    action: {
      save: "ذخیره",
      cancel: "لغو",
      delete: "حذف",
      add: "یک ردیف داده اضافه کنید"
    }
  },
  switch: {
    open: "باز",
    close: "نزدیک"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/fr_FR.js
var fr_FR_default = {
  moneySymbol: "€",
  form: {
    lightFilter: {
      more: "Plus",
      clear: "Effacer",
      confirm: "Confirmer",
      itemUnit: "Items"
    }
  },
  tableForm: {
    search: "Rechercher",
    reset: "Réinitialiser",
    submit: "Envoyer",
    collapsed: "Agrandir",
    expand: "Réduire",
    inputPlaceholder: "Entrer une valeur",
    selectPlaceholder: "Sélectionner une valeur"
  },
  alert: {
    clear: "Réinitialiser",
    selected: "Sélectionné",
    item: "Item"
  },
  pagination: {
    total: {
      range: " ",
      total: "sur",
      item: "éléments"
    }
  },
  tableToolBar: {
    leftPin: "Épingler à gauche",
    rightPin: "Épingler à gauche",
    noPin: "Sans épingle",
    leftFixedTitle: "Fixer à gauche",
    rightFixedTitle: "Fixer à droite",
    noFixedTitle: "Non fixé",
    reset: "Réinitialiser",
    columnDisplay: "Affichage colonne",
    columnSetting: "Réglages",
    fullScreen: "Plein écran",
    exitFullScreen: "Quitter Plein écran",
    reload: "Rafraichir",
    density: "Densité",
    densityDefault: "Par défaut",
    densityLarger: "Larger",
    densityMiddle: "Moyenne",
    densitySmall: "Compacte"
  },
  stepsForm: {
    next: "Suivante",
    prev: "Précédente",
    submit: "Finaliser"
  },
  loginForm: {
    submitText: "Se connecter"
  },
  editableTable: {
    action: {
      save: "Sauvegarder",
      cancel: "Annuler",
      delete: "Supprimer",
      add: "ajouter une ligne de données"
    }
  },
  switch: {
    open: "ouvert",
    close: "près"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/he_IL.js
var he_IL_default = {
  moneySymbol: "₪",
  deleteThisLine: "מחק שורה זו",
  copyThisLine: "העתק שורה זו",
  form: {
    lightFilter: {
      more: "יותר",
      clear: "נקה",
      confirm: "אישור",
      itemUnit: "פריטים"
    }
  },
  tableForm: {
    search: "חיפוש",
    reset: "איפוס",
    submit: "שלח",
    collapsed: "הרחב",
    expand: "כווץ",
    inputPlaceholder: "אנא הכנס",
    selectPlaceholder: "אנא בחר"
  },
  alert: {
    clear: "נקה",
    selected: "נבחר",
    item: "פריט"
  },
  pagination: {
    total: {
      range: " ",
      total: "מתוך",
      item: "פריטים"
    }
  },
  tableToolBar: {
    leftPin: "הצמד לשמאל",
    rightPin: "הצמד לימין",
    noPin: "לא מצורף",
    leftFixedTitle: "מוצמד לשמאל",
    rightFixedTitle: "מוצמד לימין",
    noFixedTitle: "לא מוצמד",
    reset: "איפוס",
    columnDisplay: "תצוגת עמודות",
    columnSetting: "הגדרות",
    fullScreen: "מסך מלא",
    exitFullScreen: "צא ממסך מלא",
    reload: "רענן",
    density: "רזולוציה",
    densityDefault: "ברירת מחדל",
    densityLarger: "גדול",
    densityMiddle: "בינוני",
    densitySmall: "קטן"
  },
  stepsForm: {
    next: "הבא",
    prev: "קודם",
    submit: "סיום"
  },
  loginForm: {
    submitText: "כניסה"
  },
  editableTable: {
    onlyOneLineEditor: "ניתן לערוך רק שורה אחת",
    action: {
      save: "שמור",
      cancel: "ביטול",
      delete: "מחיקה",
      add: "הוסף שורת נתונים"
    }
  },
  switch: {
    open: "פתח",
    close: "סגור"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/hr_HR.js
var hr_HR_default = {
  moneySymbol: "kn",
  form: {
    lightFilter: {
      more: "Više",
      clear: "Očisti",
      confirm: "Potvrdi",
      itemUnit: "Stavke"
    }
  },
  tableForm: {
    search: "Pretraži",
    reset: "Poništi",
    submit: "Potvrdi",
    collapsed: "Raširi",
    expand: "Skupi",
    inputPlaceholder: "Unesite",
    selectPlaceholder: "Odaberite"
  },
  alert: {
    clear: "Očisti",
    selected: "Odaberi",
    item: "stavke"
  },
  pagination: {
    total: {
      range: " ",
      total: "od",
      item: "stavke"
    }
  },
  tableToolBar: {
    leftPin: "Prikači lijevo",
    rightPin: "Prikači desno",
    noPin: "Bez prikačenja",
    leftFixedTitle: "Fiksiraj lijevo",
    rightFixedTitle: "Fiksiraj desno",
    noFixedTitle: "Bez fiksiranja",
    reset: "Resetiraj",
    columnDisplay: "Prikaz stupaca",
    columnSetting: "Postavke",
    fullScreen: "Puni zaslon",
    exitFullScreen: "Izađi iz punog zaslona",
    reload: "Ponovno učitaj",
    density: "Veličina",
    densityDefault: "Zadano",
    densityLarger: "Veliko",
    densityMiddle: "Srednje",
    densitySmall: "Malo"
  },
  stepsForm: {
    next: "Sljedeći",
    prev: "Prethodni",
    submit: "Kraj"
  },
  loginForm: {
    submitText: "Prijava"
  },
  editableTable: {
    action: {
      save: "Spremi",
      cancel: "Odustani",
      delete: "Obriši",
      add: "dodajte red podataka"
    }
  },
  switch: {
    open: "otvori",
    close: "zatvori"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/id_ID.js
var id_ID_default = {
  moneySymbol: "RP",
  form: {
    lightFilter: {
      more: "Lebih",
      clear: "Hapus",
      confirm: "Konfirmasi",
      itemUnit: "Unit"
    }
  },
  tableForm: {
    search: "Cari",
    reset: "Atur ulang",
    submit: "Kirim",
    collapsed: "Lebih sedikit",
    expand: "Lebih banyak",
    inputPlaceholder: "Masukkan pencarian",
    selectPlaceholder: "Pilih"
  },
  alert: {
    clear: "Hapus",
    selected: "Dipilih",
    item: "Butir"
  },
  pagination: {
    total: {
      range: " ",
      total: "Dari",
      item: "Butir"
    }
  },
  tableToolBar: {
    leftPin: "Pin kiri",
    rightPin: "Pin kanan",
    noPin: "Tidak ada pin",
    leftFixedTitle: "Rata kiri",
    rightFixedTitle: "Rata kanan",
    noFixedTitle: "Tidak tetap",
    reset: "Atur ulang",
    columnDisplay: "Tampilan kolom",
    columnSetting: "Pengaturan",
    fullScreen: "Layar penuh",
    exitFullScreen: "Keluar layar penuh",
    reload: "Atur ulang",
    density: "Kerapatan",
    densityDefault: "Standar",
    densityLarger: "Lebih besar",
    densityMiddle: "Sedang",
    densitySmall: "Rapat"
  },
  stepsForm: {
    next: "Selanjutnya",
    prev: "Sebelumnya",
    submit: "Selesai"
  },
  loginForm: {
    submitText: "Login"
  },
  editableTable: {
    action: {
      save: "simpan",
      cancel: "batal",
      delete: "hapus",
      add: "Tambahkan baris data"
    }
  },
  switch: {
    open: "buka",
    close: "tutup"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/it_IT.js
var it_IT_default = {
  moneySymbol: "€",
  form: {
    lightFilter: {
      more: "più",
      clear: "pulisci",
      confirm: "conferma",
      itemUnit: "elementi"
    }
  },
  tableForm: {
    search: "Filtra",
    reset: "Pulisci",
    submit: "Invia",
    collapsed: "Espandi",
    expand: "Contrai",
    inputPlaceholder: "Digita",
    selectPlaceholder: "Seleziona"
  },
  alert: {
    clear: "Rimuovi",
    selected: "Selezionati",
    item: "elementi"
  },
  pagination: {
    total: {
      range: " ",
      total: "di",
      item: "elementi"
    }
  },
  tableToolBar: {
    leftPin: "Fissa a sinistra",
    rightPin: "Fissa a destra",
    noPin: "Ripristina posizione",
    leftFixedTitle: "Fissato a sinistra",
    rightFixedTitle: "Fissato a destra",
    noFixedTitle: "Non fissato",
    reset: "Ripristina",
    columnDisplay: "Disposizione colonne",
    columnSetting: "Impostazioni",
    fullScreen: "Modalità schermo intero",
    exitFullScreen: "Esci da modalità schermo intero",
    reload: "Ricarica",
    density: "Grandezza tabella",
    densityDefault: "predefinito",
    densityLarger: "Grande",
    densityMiddle: "Media",
    densitySmall: "Compatta"
  },
  stepsForm: {
    next: "successivo",
    prev: "precedente",
    submit: "finisci"
  },
  loginForm: {
    submitText: "Accedi"
  },
  editableTable: {
    action: {
      save: "salva",
      cancel: "annulla",
      delete: "Delete",
      add: "add a row of data"
    }
  },
  switch: {
    open: "open",
    close: "chiudi"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/ja_JP.js
var ja_JP_default = {
  moneySymbol: "¥",
  form: {
    lightFilter: {
      more: "更に",
      clear: "クリア",
      confirm: "確認",
      itemUnit: "アイテム"
    }
  },
  tableForm: {
    search: "検索",
    reset: "リセット",
    submit: "送信",
    collapsed: "拡大",
    expand: "折畳",
    inputPlaceholder: "入力してください",
    selectPlaceholder: "選択してください"
  },
  alert: {
    clear: "クリア",
    selected: "選択した",
    item: "アイテム"
  },
  pagination: {
    total: {
      range: "レコード",
      total: "/合計",
      item: " "
    }
  },
  tableToolBar: {
    leftPin: "左に固定",
    rightPin: "右に固定",
    noPin: "キャンセル",
    leftFixedTitle: "左に固定された項目",
    rightFixedTitle: "右に固定された項目",
    noFixedTitle: "固定されてない項目",
    reset: "リセット",
    columnDisplay: "表示列",
    columnSetting: "列表示設定",
    fullScreen: "フルスクリーン",
    exitFullScreen: "終了",
    reload: "更新",
    density: "行高",
    densityDefault: "デフォルト",
    densityLarger: "大",
    densityMiddle: "中",
    densitySmall: "小"
  },
  stepsForm: {
    next: "次へ",
    prev: "前へ",
    submit: "送信"
  },
  loginForm: {
    submitText: "ログイン"
  },
  editableTable: {
    action: {
      save: "保存",
      cancel: "キャンセル",
      delete: "削除",
      add: "追加"
    }
  },
  switch: {
    open: "開く",
    close: "閉じる"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/ko_KR.js
var ko_KR_default = {
  moneySymbol: "₩",
  form: {
    lightFilter: {
      more: "더보기",
      clear: "초기화",
      confirm: "확인",
      itemUnit: "건수"
    }
  },
  tableForm: {
    search: "조회",
    reset: "초기화",
    submit: "제출",
    collapsed: "확장",
    expand: "닫기",
    inputPlaceholder: "입력해 주세요",
    selectPlaceholder: "선택해 주세요"
  },
  alert: {
    clear: "취소",
    selected: "선택",
    item: "건"
  },
  pagination: {
    total: {
      range: " ",
      total: "/ 총",
      item: "건"
    }
  },
  tableToolBar: {
    leftPin: "왼쪽으로 핀",
    rightPin: "오른쪽으로 핀",
    noPin: "핀 제거",
    leftFixedTitle: "왼쪽으로 고정",
    rightFixedTitle: "오른쪽으로 고정",
    noFixedTitle: "비고정",
    reset: "초기화",
    columnDisplay: "컬럼 표시",
    columnSetting: "설정",
    fullScreen: "전체 화면",
    exitFullScreen: "전체 화면 취소",
    reload: "새로 고침",
    density: "여백",
    densityDefault: "기본",
    densityLarger: "많은 여백",
    densityMiddle: "중간 여백",
    densitySmall: "좁은 여백"
  },
  stepsForm: {
    next: "다음",
    prev: "이전",
    submit: "종료"
  },
  loginForm: {
    submitText: "로그인"
  },
  editableTable: {
    action: {
      save: "저장",
      cancel: "취소",
      delete: "삭제",
      add: "데이터 행 추가"
    }
  },
  switch: {
    open: "열",
    close: "가까 운"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/mn_MN.js
var mn_MN_default = {
  moneySymbol: "₮",
  form: {
    lightFilter: {
      more: "Илүү",
      clear: "Цэвэрлэх",
      confirm: "Баталгаажуулах",
      itemUnit: "Нэгжүүд"
    }
  },
  tableForm: {
    search: "Хайх",
    reset: "Шинэчлэх",
    submit: "Илгээх",
    collapsed: "Өргөтгөх",
    expand: "Хураах",
    inputPlaceholder: "Утга оруулна уу",
    selectPlaceholder: "Утга сонгоно уу"
  },
  alert: {
    clear: "Цэвэрлэх",
    selected: "Сонгогдсон",
    item: "Нэгж"
  },
  pagination: {
    total: {
      range: " ",
      total: "Нийт",
      item: "мөр"
    }
  },
  tableToolBar: {
    leftPin: "Зүүн тийш бэхлэх",
    rightPin: "Баруун тийш бэхлэх",
    noPin: "Бэхлэхгүй",
    leftFixedTitle: "Зүүн зэрэгцүүлэх",
    rightFixedTitle: "Баруун зэрэгцүүлэх",
    noFixedTitle: "Зэрэгцүүлэхгүй",
    reset: "Шинэчлэх",
    columnDisplay: "Баганаар харуулах",
    columnSetting: "Тохиргоо",
    fullScreen: "Бүтэн дэлгэцээр",
    exitFullScreen: "Бүтэн дэлгэц цуцлах",
    reload: "Шинэчлэх",
    density: "Хэмжээ",
    densityDefault: "Хэвийн",
    densityLarger: "Том",
    densityMiddle: "Дунд",
    densitySmall: "Жижиг"
  },
  stepsForm: {
    next: "Дараах",
    prev: "Өмнөх",
    submit: "Дуусгах"
  },
  loginForm: {
    submitText: "Нэвтрэх"
  },
  editableTable: {
    action: {
      save: "Хадгалах",
      cancel: "Цуцлах",
      delete: "Устгах",
      add: "Мөр нэмэх"
    }
  },
  switch: {
    open: "Нээх",
    close: "Хаах"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/ms_MY.js
var ms_MY_default = {
  moneySymbol: "RM",
  form: {
    lightFilter: {
      more: "Lebih banyak",
      clear: "Jelas",
      confirm: "Mengesahkan",
      itemUnit: "Item"
    }
  },
  tableForm: {
    search: "Cari",
    reset: "Menetapkan semula",
    submit: "Hantar",
    collapsed: "Kembang",
    expand: "Kuncup",
    inputPlaceholder: "Sila masuk",
    selectPlaceholder: "Sila pilih"
  },
  alert: {
    clear: "Padam",
    selected: "Dipilih",
    item: "Item"
  },
  pagination: {
    total: {
      range: " ",
      total: "daripada",
      item: "item"
    }
  },
  tableToolBar: {
    leftPin: "Pin ke kiri",
    rightPin: "Pin ke kanan",
    noPin: "Tidak pin",
    leftFixedTitle: "Tetap ke kiri",
    rightFixedTitle: "Tetap ke kanan",
    noFixedTitle: "Tidak Tetap",
    reset: "Menetapkan semula",
    columnDisplay: "Lajur",
    columnSetting: "Settings",
    fullScreen: "Full Screen",
    exitFullScreen: "Keluar Full Screen",
    reload: "Muat Semula",
    density: "Densiti",
    densityDefault: "Biasa",
    densityLarger: "Besar",
    densityMiddle: "Tengah",
    densitySmall: "Kecil"
  },
  stepsForm: {
    next: "Seterusnya",
    prev: "Sebelumnya",
    submit: "Selesai"
  },
  loginForm: {
    submitText: "Log Masuk"
  },
  editableTable: {
    action: {
      save: "Simpan",
      cancel: "Membatalkan",
      delete: "Menghapuskan",
      add: "tambah baris data"
    }
  },
  switch: {
    open: "Terbuka",
    close: "Tutup"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/nl_NL.js
var nl_NL_default = {
  moneySymbol: "€",
  deleteThisLine: "Verwijder deze regel",
  copyThisLine: "Kopieer deze regel",
  form: {
    lightFilter: {
      more: "Meer filters",
      clear: "Wissen",
      confirm: "Bevestigen",
      itemUnit: "item"
    }
  },
  tableForm: {
    search: "Zoeken",
    reset: "Resetten",
    submit: "Indienen",
    collapsed: "Uitvouwen",
    expand: "Inklappen",
    inputPlaceholder: "Voer in",
    selectPlaceholder: "Selecteer"
  },
  alert: {
    clear: "Selectie annuleren",
    selected: "Geselecteerd",
    item: "item"
  },
  pagination: {
    total: {
      range: "Van",
      total: "items/totaal",
      item: "items"
    }
  },
  tableToolBar: {
    leftPin: "Vastzetten aan begin",
    rightPin: "Vastzetten aan einde",
    noPin: "Niet vastzetten",
    leftFixedTitle: "Vastzetten aan de linkerkant",
    rightFixedTitle: "Vastzetten aan de rechterkant",
    noFixedTitle: "Niet vastzetten",
    reset: "Resetten",
    columnDisplay: "Kolomweergave",
    columnSetting: "Kolominstellingen",
    fullScreen: "Volledig scherm",
    exitFullScreen: "Verlaat volledig scherm",
    reload: "Vernieuwen",
    density: "Dichtheid",
    densityDefault: "Normaal",
    densityLarger: "Ruim",
    densityMiddle: "Gemiddeld",
    densitySmall: "Compact"
  },
  stepsForm: {
    next: "Volgende stap",
    prev: "Vorige stap",
    submit: "Indienen"
  },
  loginForm: {
    submitText: "Inloggen"
  },
  editableTable: {
    onlyOneLineEditor: "Slechts één regel tegelijk bewerken",
    action: {
      save: "Opslaan",
      cancel: "Annuleren",
      delete: "Verwijderen",
      add: "Een regel toevoegen"
    }
  },
  switch: {
    open: "Openen",
    close: "Sluiten"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/pl_PL.js
var pl_PL_default = {
  moneySymbol: "zł",
  form: {
    lightFilter: {
      more: "Więcej",
      clear: "Wyczyść",
      confirm: "Potwierdź",
      itemUnit: "Ilość"
    }
  },
  tableForm: {
    search: "Szukaj",
    reset: "Reset",
    submit: "Zatwierdź",
    collapsed: "Pokaż wiecej",
    expand: "Pokaż mniej",
    inputPlaceholder: "Proszę podać",
    selectPlaceholder: "Proszę wybrać"
  },
  alert: {
    clear: "Wyczyść",
    selected: "Wybrane",
    item: "Wpis"
  },
  pagination: {
    total: {
      range: " ",
      total: "z",
      item: "Wpisów"
    }
  },
  tableToolBar: {
    leftPin: "Przypnij do lewej",
    rightPin: "Przypnij do prawej",
    noPin: "Odepnij",
    leftFixedTitle: "Przypięte do lewej",
    rightFixedTitle: "Przypięte do prawej",
    noFixedTitle: "Nieprzypięte",
    reset: "Reset",
    columnDisplay: "Wyświetlane wiersze",
    columnSetting: "Ustawienia",
    fullScreen: "Pełen ekran",
    exitFullScreen: "Zamknij pełen ekran",
    reload: "Odśwież",
    density: "Odstęp",
    densityDefault: "Standard",
    densityLarger: "Wiekszy",
    densityMiddle: "Sredni",
    densitySmall: "Kompaktowy"
  },
  stepsForm: {
    next: "Weiter",
    prev: "Zurück",
    submit: "Abschließen"
  },
  loginForm: {
    submitText: "Zaloguj się"
  },
  editableTable: {
    action: {
      save: "Zapisać",
      cancel: "Anuluj",
      delete: "Usunąć",
      add: "dodawanie wiersza danych"
    }
  },
  switch: {
    open: "otwierać",
    close: "zamykać"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/pt_BR.js
var pt_BR_default = {
  moneySymbol: "R$",
  form: {
    lightFilter: {
      more: "Mais",
      clear: "Limpar",
      confirm: "Confirmar",
      itemUnit: "Itens"
    }
  },
  tableForm: {
    search: "Filtrar",
    reset: "Limpar",
    submit: "Confirmar",
    collapsed: "Expandir",
    expand: "Colapsar",
    inputPlaceholder: "Por favor insira",
    selectPlaceholder: "Por favor selecione"
  },
  alert: {
    clear: "Limpar",
    selected: "Selecionado(s)",
    item: "Item(s)"
  },
  pagination: {
    total: {
      range: " ",
      total: "de",
      item: "itens"
    }
  },
  tableToolBar: {
    leftPin: "Fixar à esquerda",
    rightPin: "Fixar à direita",
    noPin: "Desfixado",
    leftFixedTitle: "Fixado à esquerda",
    rightFixedTitle: "Fixado à direita",
    noFixedTitle: "Não fixado",
    reset: "Limpar",
    columnDisplay: "Mostrar Coluna",
    columnSetting: "Configurações",
    fullScreen: "Tela Cheia",
    exitFullScreen: "Sair da Tela Cheia",
    reload: "Atualizar",
    density: "Densidade",
    densityDefault: "Padrão",
    densityLarger: "Largo",
    densityMiddle: "Médio",
    densitySmall: "Compacto"
  },
  stepsForm: {
    next: "Próximo",
    prev: "Anterior",
    submit: "Enviar"
  },
  loginForm: {
    submitText: "Entrar"
  },
  editableTable: {
    action: {
      save: "Salvar",
      cancel: "Cancelar",
      delete: "Apagar",
      add: "adicionar uma linha de dados"
    }
  },
  switch: {
    open: "abrir",
    close: "fechar"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/ro_RO.js
var ro_RO_default = {
  moneySymbol: "RON",
  deleteThisLine: "Șterge acest rând",
  copyThisLine: "Copiază acest rând",
  form: {
    lightFilter: {
      more: "Mai multe filtre",
      clear: "Curăță",
      confirm: "Confirmă",
      itemUnit: "elemente"
    }
  },
  tableForm: {
    search: "Caută",
    reset: "Resetează",
    submit: "Trimite",
    collapsed: "Extinde",
    expand: "Restrânge",
    inputPlaceholder: "Introduceți",
    selectPlaceholder: "Selectați"
  },
  alert: {
    clear: "Anulează selecția",
    selected: "Selectat",
    item: "elemente"
  },
  pagination: {
    total: {
      range: "De la",
      total: "elemente/total",
      item: "elemente"
    }
  },
  tableToolBar: {
    leftPin: "Fixează la început",
    rightPin: "Fixează la sfârșit",
    noPin: "Nu fixa",
    leftFixedTitle: "Fixează în stânga",
    rightFixedTitle: "Fixează în dreapta",
    noFixedTitle: "Nu fixa",
    reset: "Resetează",
    columnDisplay: "Afișare coloane",
    columnSetting: "Setări coloane",
    fullScreen: "Ecran complet",
    exitFullScreen: "Ieși din ecran complet",
    reload: "Reîncarcă",
    density: "Densitate",
    densityDefault: "Normal",
    densityLarger: "Larg",
    densityMiddle: "Mediu",
    densitySmall: "Compact"
  },
  stepsForm: {
    next: "Pasul următor",
    prev: "Pasul anterior",
    submit: "Trimite"
  },
  loginForm: {
    submitText: "Autentificare"
  },
  editableTable: {
    onlyOneLineEditor: "Se poate edita doar un rând simultan",
    action: {
      save: "Salvează",
      cancel: "Anulează",
      delete: "Șterge",
      add: "Adaugă un rând"
    }
  },
  switch: {
    open: "Deschide",
    close: "Închide"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/ru_RU.js
var ru_RU_default = {
  moneySymbol: "₽",
  form: {
    lightFilter: {
      more: "Еще",
      clear: "Очистить",
      confirm: "ОК",
      itemUnit: "Позиции"
    }
  },
  tableForm: {
    search: "Найти",
    reset: "Сброс",
    submit: "Отправить",
    collapsed: "Развернуть",
    expand: "Свернуть",
    inputPlaceholder: "Введите значение",
    selectPlaceholder: "Выберите значение"
  },
  alert: {
    clear: "Очистить",
    selected: "Выбрано",
    item: "элементов"
  },
  pagination: {
    total: {
      range: " ",
      total: "из",
      item: "элементов"
    }
  },
  tableToolBar: {
    leftPin: "Закрепить слева",
    rightPin: "Закрепить справа",
    noPin: "Открепить",
    leftFixedTitle: "Закреплено слева",
    rightFixedTitle: "Закреплено справа",
    noFixedTitle: "Не закреплено",
    reset: "Сброс",
    columnDisplay: "Отображение столбца",
    columnSetting: "Настройки",
    fullScreen: "Полный экран",
    exitFullScreen: "Выйти из полноэкранного режима",
    reload: "Обновить",
    density: "Размер",
    densityDefault: "По умолчанию",
    densityLarger: "Большой",
    densityMiddle: "Средний",
    densitySmall: "Сжатый"
  },
  stepsForm: {
    next: "Следующий",
    prev: "Предыдущий",
    submit: "Завершить"
  },
  loginForm: {
    submitText: "Вход"
  },
  editableTable: {
    action: {
      save: "Сохранить",
      cancel: "Отменить",
      delete: "Удалить",
      add: "добавить ряд данных"
    }
  },
  switch: {
    open: "Открытый чемпионат мира по теннису",
    close: "По адресу:"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/sk_SK.js
var sk_SK_default = {
  moneySymbol: "€",
  deleteThisLine: "Odstrániť tento riadok",
  copyThisLine: "Skopírujte tento riadok",
  form: {
    lightFilter: {
      more: "Viac",
      clear: "Vyčistiť",
      confirm: "Potvrďte",
      itemUnit: "Položky"
    }
  },
  tableForm: {
    search: "Vyhladať",
    reset: "Resetovať",
    submit: "Odoslať",
    collapsed: "Rozbaliť",
    expand: "Zbaliť",
    inputPlaceholder: "Prosím, zadajte",
    selectPlaceholder: "Prosím, vyberte"
  },
  alert: {
    clear: "Vyčistiť",
    selected: "Vybraný",
    item: "Položka"
  },
  pagination: {
    total: {
      range: " ",
      total: "z",
      item: "položiek"
    }
  },
  tableToolBar: {
    leftPin: "Pripnúť vľavo",
    rightPin: "Pripnúť vpravo",
    noPin: "Odopnuté",
    leftFixedTitle: "Fixované na ľavo",
    rightFixedTitle: "Fixované na pravo",
    noFixedTitle: "Nefixované",
    reset: "Resetovať",
    columnDisplay: "Zobrazenie stĺpcov",
    columnSetting: "Nastavenia",
    fullScreen: "Celá obrazovka",
    exitFullScreen: "Ukončiť celú obrazovku",
    reload: "Obnoviť",
    density: "Hustota",
    densityDefault: "Predvolené",
    densityLarger: "Väčšie",
    densityMiddle: "Stredné",
    densitySmall: "Kompaktné"
  },
  stepsForm: {
    next: "Ďalšie",
    prev: "Predchádzajúce",
    submit: "Potvrdiť"
  },
  loginForm: {
    submitText: "Prihlásiť sa"
  },
  editableTable: {
    onlyOneLineEditor: "Upravovať možno iba jeden riadok",
    action: {
      save: "Uložiť",
      cancel: "Zrušiť",
      delete: "Odstrániť",
      add: "pridať riadok údajov"
    }
  },
  switch: {
    open: "otvoriť",
    close: "zavrieť"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/sr_RS.js
var sr_RS_default = {
  moneySymbol: "RSD",
  form: {
    lightFilter: {
      more: "Više",
      clear: "Očisti",
      confirm: "Potvrdi",
      itemUnit: "Stavke"
    }
  },
  tableForm: {
    search: "Pronađi",
    reset: "Resetuj",
    submit: "Pošalji",
    collapsed: "Proširi",
    expand: "Skupi",
    inputPlaceholder: "Molimo unesite",
    selectPlaceholder: "Molimo odaberite"
  },
  alert: {
    clear: "Očisti",
    selected: "Odabrano",
    item: "Stavka"
  },
  pagination: {
    total: {
      range: " ",
      total: "od",
      item: "stavki"
    }
  },
  tableToolBar: {
    leftPin: "Zakači levo",
    rightPin: "Zakači desno",
    noPin: "Nije zakačeno",
    leftFixedTitle: "Fiksirano levo",
    rightFixedTitle: "Fiksirano desno",
    noFixedTitle: "Nije fiksirano",
    reset: "Resetuj",
    columnDisplay: "Prikaz kolona",
    columnSetting: "Podešavanja",
    fullScreen: "Pun ekran",
    exitFullScreen: "Zatvori pun ekran",
    reload: "Osveži",
    density: "Veličina",
    densityDefault: "Podrazumevana",
    densityLarger: "Veća",
    densityMiddle: "Srednja",
    densitySmall: "Kompaktna"
  },
  stepsForm: {
    next: "Dalje",
    prev: "Nazad",
    submit: "Gotovo"
  },
  loginForm: {
    submitText: "Prijavi se"
  },
  editableTable: {
    action: {
      save: "Sačuvaj",
      cancel: "Poništi",
      delete: "Obriši",
      add: "dodajte red podataka"
    }
  },
  switch: {
    open: "Отворите",
    close: "Затворите"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/sv_SE.js
var sv_SE_default = {
  moneySymbol: "SEK",
  deleteThisLine: "Radera denna rad",
  copyThisLine: "Kopiera denna rad",
  form: {
    lightFilter: {
      more: "Fler filter",
      clear: "Rensa",
      confirm: "Bekräfta",
      itemUnit: "objekt"
    }
  },
  tableForm: {
    search: "Sök",
    reset: "Återställ",
    submit: "Skicka",
    collapsed: "Expandera",
    expand: "Fäll ihop",
    inputPlaceholder: "Vänligen ange",
    selectPlaceholder: "Vänligen välj"
  },
  alert: {
    clear: "Avbryt val",
    selected: "Vald",
    item: "objekt"
  },
  pagination: {
    total: {
      range: "Från",
      total: "objekt/totalt",
      item: "objekt"
    }
  },
  tableToolBar: {
    leftPin: "Fäst till vänster",
    rightPin: "Fäst till höger",
    noPin: "Inte fäst",
    leftFixedTitle: "Fäst till vänster",
    rightFixedTitle: "Fäst till höger",
    noFixedTitle: "Inte fäst",
    reset: "Återställ",
    columnDisplay: "Kolumnvisning",
    columnSetting: "Kolumninställningar",
    fullScreen: "Fullskärm",
    exitFullScreen: "Avsluta fullskärm",
    reload: "Ladda om",
    density: "Täthet",
    densityDefault: "Normal",
    densityLarger: "Lös",
    densityMiddle: "Medium",
    densitySmall: "Kompakt"
  },
  stepsForm: {
    next: "Nästa steg",
    prev: "Föregående steg",
    submit: "Skicka"
  },
  loginForm: {
    submitText: "Logga in"
  },
  editableTable: {
    onlyOneLineEditor: "Endast en rad kan redigeras åt gången",
    action: {
      save: "Spara",
      cancel: "Avbryt",
      delete: "Radera",
      add: "Lägg till en rad"
    }
  },
  switch: {
    open: "Öppna",
    close: "Stäng"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/th_TH.js
var th_TH_default = {
  moneySymbol: "฿",
  deleteThisLine: "ลบบรรทัดนี้",
  copyThisLine: "คัดลอกบรรทัดนี้",
  form: {
    lightFilter: {
      more: "มากกว่า",
      clear: "ชัดเจน",
      confirm: "ยืนยัน",
      itemUnit: "รายการ"
    }
  },
  tableForm: {
    search: "สอบถาม",
    reset: "รีเซ็ต",
    submit: "ส่ง",
    collapsed: "ขยาย",
    expand: "ทรุด",
    inputPlaceholder: "กรุณาป้อน",
    selectPlaceholder: "โปรดเลือก"
  },
  alert: {
    clear: "ชัดเจน",
    selected: "เลือกแล้ว",
    item: "รายการ"
  },
  pagination: {
    total: {
      range: " ",
      total: "ของ",
      item: "รายการ"
    }
  },
  tableToolBar: {
    leftPin: "ปักหมุดไปทางซ้าย",
    rightPin: "ปักหมุดไปทางขวา",
    noPin: "เลิกตรึงแล้ว",
    leftFixedTitle: "แก้ไขด้านซ้าย",
    rightFixedTitle: "แก้ไขด้านขวา",
    noFixedTitle: "ไม่คงที่",
    reset: "รีเซ็ต",
    columnDisplay: "การแสดงคอลัมน์",
    columnSetting: "การตั้งค่า",
    fullScreen: "เต็มจอ",
    exitFullScreen: "ออกจากโหมดเต็มหน้าจอ",
    reload: "รีเฟรช",
    density: "ความหนาแน่น",
    densityDefault: "ค่าเริ่มต้น",
    densityLarger: "ขนาดใหญ่ขึ้น",
    densityMiddle: "กลาง",
    densitySmall: "กะทัดรัด"
  },
  stepsForm: {
    next: "ถัดไป",
    prev: "ก่อนหน้า",
    submit: "เสร็จ"
  },
  loginForm: {
    submitText: "เข้าสู่ระบบ"
  },
  editableTable: {
    onlyOneLineEditor: "แก้ไขได้เพียงบรรทัดเดียวเท่านั้น",
    action: {
      save: "บันทึก",
      cancel: "ยกเลิก",
      delete: "ลบ",
      add: "เพิ่มแถวของข้อมูล"
    }
  },
  switch: {
    open: "เปิด",
    close: "ปิด"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/tr_TR.js
var tr_TR_default = {
  moneySymbol: "₺",
  form: {
    lightFilter: {
      more: "Daha Fazla",
      clear: "Temizle",
      confirm: "Onayla",
      itemUnit: "Öğeler"
    }
  },
  tableForm: {
    search: "Filtrele",
    reset: "Sıfırla",
    submit: "Gönder",
    collapsed: "Daha fazla",
    expand: "Daha az",
    inputPlaceholder: "Filtrelemek için bir değer girin",
    selectPlaceholder: "Filtrelemek için bir değer seçin"
  },
  alert: {
    clear: "Temizle",
    selected: "Seçili",
    item: "Öğe"
  },
  pagination: {
    total: {
      range: " ",
      total: "Toplam",
      item: "Öğe"
    }
  },
  tableToolBar: {
    leftPin: "Sola sabitle",
    rightPin: "Sağa sabitle",
    noPin: "Sabitlemeyi kaldır",
    leftFixedTitle: "Sola sabitlendi",
    rightFixedTitle: "Sağa sabitlendi",
    noFixedTitle: "Sabitlenmedi",
    reset: "Sıfırla",
    columnDisplay: "Kolon Görünümü",
    columnSetting: "Ayarlar",
    fullScreen: "Tam Ekran",
    exitFullScreen: "Tam Ekrandan Çık",
    reload: "Yenile",
    density: "Kalınlık",
    densityDefault: "Varsayılan",
    densityLarger: "Büyük",
    densityMiddle: "Orta",
    densitySmall: "Küçük"
  },
  stepsForm: {
    next: "Sıradaki",
    prev: "Önceki",
    submit: "Gönder"
  },
  loginForm: {
    submitText: "Giriş Yap"
  },
  editableTable: {
    action: {
      save: "Kaydet",
      cancel: "Vazgeç",
      delete: "Sil",
      add: "foegje in rige gegevens ta"
    }
  },
  switch: {
    open: "açık",
    close: "kapatmak"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/uk_UA.js
var uk_UA_default = {
  moneySymbol: "₴",
  deleteThisLine: "Видатили рядок",
  copyThisLine: "Скопіювати рядок",
  form: {
    lightFilter: {
      more: "Ще",
      clear: "Очистити",
      confirm: "Ок",
      itemUnit: "Позиції"
    }
  },
  tableForm: {
    search: "Пошук",
    reset: "Очистити",
    submit: "Відправити",
    collapsed: "Розгорнути",
    expand: "Згорнути",
    inputPlaceholder: "Введіть значення",
    selectPlaceholder: "Оберіть значення"
  },
  alert: {
    clear: "Очистити",
    selected: "Обрано",
    item: "елементів"
  },
  pagination: {
    total: {
      range: " ",
      total: "з",
      item: "елементів"
    }
  },
  tableToolBar: {
    leftPin: "Закріпити зліва",
    rightPin: "Закріпити справа",
    noPin: "Відкріпити",
    leftFixedTitle: "Закріплено зліва",
    rightFixedTitle: "Закріплено справа",
    noFixedTitle: "Не закріплено",
    reset: "Скинути",
    columnDisplay: "Відображення стовпців",
    columnSetting: "Налаштування",
    fullScreen: "Повноекранний режим",
    exitFullScreen: "Вийти з повноекранного режиму",
    reload: "Оновити",
    density: "Розмір",
    densityDefault: "За замовчуванням",
    densityLarger: "Великий",
    densityMiddle: "Середній",
    densitySmall: "Стислий"
  },
  stepsForm: {
    next: "Наступний",
    prev: "Попередній",
    submit: "Завершити"
  },
  loginForm: {
    submitText: "Вхіх"
  },
  editableTable: {
    onlyOneLineEditor: "Тільки один рядок може бути редагований одночасно",
    action: {
      save: "Зберегти",
      cancel: "Відмінити",
      delete: "Видалити",
      add: "додати рядок"
    }
  },
  switch: {
    open: "Відкрито",
    close: "Закрито"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/uz_UZ.js
var uz_UZ_default = {
  moneySymbol: "UZS",
  form: {
    lightFilter: {
      more: "Yana",
      clear: "Tozalash",
      confirm: "OK",
      itemUnit: "Pozitsiyalar"
    }
  },
  tableForm: {
    search: "Qidirish",
    reset: "Qayta tiklash",
    submit: "Yuborish",
    collapsed: "Yig‘ish",
    expand: "Kengaytirish",
    inputPlaceholder: "Qiymatni kiriting",
    selectPlaceholder: "Qiymatni tanlang"
  },
  alert: {
    clear: "Tozalash",
    selected: "Tanlangan",
    item: "elementlar"
  },
  pagination: {
    total: {
      range: " ",
      total: "dan",
      item: "elementlar"
    }
  },
  tableToolBar: {
    leftPin: "Chapga mahkamlash",
    rightPin: "O‘ngga mahkamlash",
    noPin: "Mahkamlashni olib tashlash",
    leftFixedTitle: "Chapga mahkamlangan",
    rightFixedTitle: "O‘ngga mahkamlangan",
    noFixedTitle: "Mahkamlashsiz",
    reset: "Qayta tiklash",
    columnDisplay: "Ustunni ko‘rsatish",
    columnSetting: "Sozlamalar",
    fullScreen: "To‘liq ekran",
    exitFullScreen: "To‘liq ekrandan chiqish",
    reload: "Yangilash",
    density: "O‘lcham",
    densityDefault: "Standart",
    densityLarger: "Katta",
    densityMiddle: "O‘rtacha",
    densitySmall: "Kichik"
  },
  stepsForm: {
    next: "Keyingi",
    prev: "Oldingi",
    submit: "Tugatish"
  },
  loginForm: {
    submitText: "Kirish"
  },
  editableTable: {
    action: {
      save: "Saqlash",
      cancel: "Bekor qilish",
      delete: "O‘chirish",
      add: "maʼlumotlar qatorini qo‘shish"
    }
  },
  switch: {
    open: "Ochish",
    close: "Yopish"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/vi_VN.js
var vi_VN_default = {
  moneySymbol: "₫",
  form: {
    lightFilter: {
      more: "Nhiều hơn",
      clear: "Trong",
      confirm: "Xác nhận",
      itemUnit: "Mục"
    }
  },
  tableForm: {
    search: "Tìm kiếm",
    reset: "Làm lại",
    submit: "Gửi đi",
    collapsed: "Mở rộng",
    expand: "Thu gọn",
    inputPlaceholder: "nhập dữ liệu",
    selectPlaceholder: "Vui lòng chọn"
  },
  alert: {
    clear: "Xóa",
    selected: "đã chọn",
    item: "mục"
  },
  pagination: {
    total: {
      range: " ",
      total: "trên",
      item: "mặt hàng"
    }
  },
  tableToolBar: {
    leftPin: "Ghim trái",
    rightPin: "Ghim phải",
    noPin: "Bỏ ghim",
    leftFixedTitle: "Cố định trái",
    rightFixedTitle: "Cố định phải",
    noFixedTitle: "Chưa cố định",
    reset: "Làm lại",
    columnDisplay: "Cột hiển thị",
    columnSetting: "Cấu hình",
    fullScreen: "Chế độ toàn màn hình",
    exitFullScreen: "Thoát chế độ toàn màn hình",
    reload: "Làm mới",
    density: "Mật độ hiển thị",
    densityDefault: "Mặc định",
    densityLarger: "Mặc định",
    densityMiddle: "Trung bình",
    densitySmall: "Chật"
  },
  stepsForm: {
    next: "Sau",
    prev: "Trước",
    submit: "Kết thúc"
  },
  loginForm: {
    submitText: "Đăng nhập"
  },
  editableTable: {
    action: {
      save: "Cứu",
      cancel: "Hủy",
      delete: "Xóa",
      add: "thêm một hàng dữ liệu"
    }
  },
  switch: {
    open: "mở",
    close: "đóng"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/zh_CN.js
var zh_CN_default2 = {
  moneySymbol: "¥",
  deleteThisLine: "删除此项",
  copyThisLine: "复制此项",
  form: {
    lightFilter: {
      more: "更多筛选",
      clear: "清除",
      confirm: "确认",
      itemUnit: "项"
    }
  },
  tableForm: {
    search: "查询",
    reset: "重置",
    submit: "提交",
    collapsed: "展开",
    expand: "收起",
    inputPlaceholder: "请输入",
    selectPlaceholder: "请选择"
  },
  alert: {
    clear: "取消选择",
    selected: "已选择",
    item: "项"
  },
  pagination: {
    total: {
      range: "第",
      total: "条/总共",
      item: "条"
    }
  },
  tableToolBar: {
    leftPin: "固定在列首",
    rightPin: "固定在列尾",
    noPin: "不固定",
    leftFixedTitle: "固定在左侧",
    rightFixedTitle: "固定在右侧",
    noFixedTitle: "不固定",
    reset: "重置",
    columnDisplay: "列展示",
    columnSetting: "列设置",
    fullScreen: "全屏",
    exitFullScreen: "退出全屏",
    reload: "刷新",
    density: "密度",
    densityDefault: "正常",
    densityLarger: "宽松",
    densityMiddle: "中等",
    densitySmall: "紧凑"
  },
  stepsForm: {
    next: "下一步",
    prev: "上一步",
    submit: "提交"
  },
  loginForm: {
    submitText: "登录"
  },
  editableTable: {
    onlyOneLineEditor: "只能同时编辑一行",
    action: {
      save: "保存",
      cancel: "取消",
      delete: "删除",
      add: "添加一行数据"
    }
  },
  switch: {
    open: "打开",
    close: "关闭"
  }
};

// node_modules/@ant-design/pro-provider/es/locale/zh_TW.js
var zh_TW_default = {
  moneySymbol: "NT$",
  deleteThisLine: "刪除此项",
  copyThisLine: "複製此项",
  form: {
    lightFilter: {
      more: "更多篩選",
      clear: "清除",
      confirm: "確認",
      itemUnit: "項"
    }
  },
  tableForm: {
    search: "查詢",
    reset: "重置",
    submit: "提交",
    collapsed: "展開",
    expand: "收起",
    inputPlaceholder: "請輸入",
    selectPlaceholder: "請選擇"
  },
  alert: {
    clear: "取消選擇",
    selected: "已選擇",
    item: "項"
  },
  pagination: {
    total: {
      range: "第",
      total: "條/總共",
      item: "條"
    }
  },
  tableToolBar: {
    leftPin: "固定到左邊",
    rightPin: "固定到右邊",
    noPin: "不固定",
    leftFixedTitle: "固定在左側",
    rightFixedTitle: "固定在右側",
    noFixedTitle: "不固定",
    reset: "重置",
    columnDisplay: "列展示",
    columnSetting: "列設置",
    fullScreen: "全屏",
    exitFullScreen: "退出全屏",
    reload: "刷新",
    density: "密度",
    densityDefault: "正常",
    densityLarger: "寬鬆",
    densityMiddle: "中等",
    densitySmall: "緊湊"
  },
  stepsForm: {
    next: "下一步",
    prev: "上一步",
    submit: "完成"
  },
  loginForm: {
    submitText: "登入"
  },
  editableTable: {
    onlyOneLineEditor: "只能同時編輯一行",
    action: {
      save: "保存",
      cancel: "取消",
      delete: "刪除",
      add: "新增一行資料"
    }
  },
  switch: {
    open: "打開",
    close: "關閉"
  }
};

// node_modules/@ant-design/pro-provider/es/intl.js
var createIntl = function createIntl2(locale4, localeMap) {
  return {
    getMessage: function getMessage(id, defaultMessage) {
      var msg = get(localeMap, id.replace(/\[(\d+)\]/g, ".$1").split(".")) || "";
      if (msg)
        return msg;
      var localKey = locale4.replace("_", "-");
      if (localKey === "zh-CN") {
        return defaultMessage;
      }
      var intl = intlMap["zh-CN"];
      return intl ? intl.getMessage(id, defaultMessage) : defaultMessage;
    },
    locale: locale4
  };
};
var mnMNIntl = createIntl("mn_MN", mn_MN_default);
var arEGIntl = createIntl("ar_EG", ar_EG_default);
var zhCNIntl = createIntl("zh_CN", zh_CN_default2);
var enUSIntl = createIntl("en_US", en_US_default);
var enGBIntl = createIntl("en_GB", en_GB_default);
var viVNIntl = createIntl("vi_VN", vi_VN_default);
var itITIntl = createIntl("it_IT", it_IT_default);
var jaJPIntl = createIntl("ja_JP", ja_JP_default);
var esESIntl = createIntl("es_ES", es_ES_default);
var caESIntl = createIntl("ca_ES", ca_ES_default);
var ruRUIntl = createIntl("ru_RU", ru_RU_default);
var srRSIntl = createIntl("sr_RS", sr_RS_default);
var msMYIntl = createIntl("ms_MY", ms_MY_default);
var zhTWIntl = createIntl("zh_TW", zh_TW_default);
var frFRIntl = createIntl("fr_FR", fr_FR_default);
var ptBRIntl = createIntl("pt_BR", pt_BR_default);
var koKRIntl = createIntl("ko_KR", ko_KR_default);
var idIDIntl = createIntl("id_ID", id_ID_default);
var deDEIntl = createIntl("de_DE", de_DE_default);
var faIRIntl = createIntl("fa_IR", fa_IR_default);
var trTRIntl = createIntl("tr_TR", tr_TR_default);
var plPLIntl = createIntl("pl_PL", pl_PL_default);
var hrHRIntl = createIntl("hr_", hr_HR_default);
var thTHIntl = createIntl("th_TH", th_TH_default);
var csCZIntl = createIntl("cs_cz", cs_CZ_default);
var skSKIntl = createIntl("sk_SK", sk_SK_default);
var heILIntl = createIntl("he_IL", he_IL_default);
var ukUAIntl = createIntl("uk_UA", uk_UA_default);
var uzUZIntl = createIntl("uz_UZ", uz_UZ_default);
var nlNLIntl = createIntl("nl_NL", nl_NL_default);
var roROIntl = createIntl("ro_RO", ro_RO_default);
var svSEIntl = createIntl("sv_SE", sv_SE_default);
var intlMap = {
  "mn-MN": mnMNIntl,
  "ar-EG": arEGIntl,
  "zh-CN": zhCNIntl,
  "en-US": enUSIntl,
  "en-GB": enGBIntl,
  "vi-VN": viVNIntl,
  "it-IT": itITIntl,
  "ja-JP": jaJPIntl,
  "es-ES": esESIntl,
  "ca-ES": caESIntl,
  "ru-RU": ruRUIntl,
  "sr-RS": srRSIntl,
  "ms-MY": msMYIntl,
  "zh-TW": zhTWIntl,
  "fr-FR": frFRIntl,
  "pt-BR": ptBRIntl,
  "ko-KR": koKRIntl,
  "id-ID": idIDIntl,
  "de-DE": deDEIntl,
  "fa-IR": faIRIntl,
  "tr-TR": trTRIntl,
  "pl-PL": plPLIntl,
  "hr-HR": hrHRIntl,
  "th-TH": thTHIntl,
  "cs-CZ": csCZIntl,
  "sk-SK": skSKIntl,
  "he-IL": heILIntl,
  "uk-UA": ukUAIntl,
  "uz-UZ": uzUZIntl,
  "nl-NL": nlNLIntl,
  "ro-RO": roROIntl,
  "sv-SE": svSEIntl
};
var intlMapKeys = Object.keys(intlMap);
var findIntlKeyByAntdLocaleKey = function findIntlKeyByAntdLocaleKey2(localeKey) {
  var localeName = (localeKey || "zh-CN").toLocaleLowerCase();
  return intlMapKeys.find(function(intlKey) {
    var LowerCaseKey = intlKey.toLocaleLowerCase();
    return LowerCaseKey.includes(localeName);
  });
};

// node_modules/@ctrl/tinycolor/dist/module/util.js
function bound01(n, max) {
  if (isOnePointZero(n)) {
    n = "100%";
  }
  var isPercent = isPercentage(n);
  n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));
  if (isPercent) {
    n = parseInt(String(n * max), 10) / 100;
  }
  if (Math.abs(n - max) < 1e-6) {
    return 1;
  }
  if (max === 360) {
    n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));
  } else {
    n = n % max / parseFloat(String(max));
  }
  return n;
}
function clamp01(val) {
  return Math.min(1, Math.max(0, val));
}
function isOnePointZero(n) {
  return typeof n === "string" && n.indexOf(".") !== -1 && parseFloat(n) === 1;
}
function isPercentage(n) {
  return typeof n === "string" && n.indexOf("%") !== -1;
}
function boundAlpha(a) {
  a = parseFloat(a);
  if (isNaN(a) || a < 0 || a > 1) {
    a = 1;
  }
  return a;
}
function convertToPercentage(n) {
  if (n <= 1) {
    return "".concat(Number(n) * 100, "%");
  }
  return n;
}
function pad2(c) {
  return c.length === 1 ? "0" + c : String(c);
}

// node_modules/@ctrl/tinycolor/dist/module/conversion.js
function rgbToRgb(r, g, b) {
  return {
    r: bound01(r, 255) * 255,
    g: bound01(g, 255) * 255,
    b: bound01(b, 255) * 255
  };
}
function rgbToHsl(r, g, b) {
  r = bound01(r, 255);
  g = bound01(g, 255);
  b = bound01(b, 255);
  var max = Math.max(r, g, b);
  var min = Math.min(r, g, b);
  var h = 0;
  var s = 0;
  var l = (max + min) / 2;
  if (max === min) {
    s = 0;
    h = 0;
  } else {
    var d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
      default:
        break;
    }
    h /= 6;
  }
  return { h, s, l };
}
function hue2rgb(p, q, t) {
  if (t < 0) {
    t += 1;
  }
  if (t > 1) {
    t -= 1;
  }
  if (t < 1 / 6) {
    return p + (q - p) * (6 * t);
  }
  if (t < 1 / 2) {
    return q;
  }
  if (t < 2 / 3) {
    return p + (q - p) * (2 / 3 - t) * 6;
  }
  return p;
}
function hslToRgb(h, s, l) {
  var r;
  var g;
  var b;
  h = bound01(h, 360);
  s = bound01(s, 100);
  l = bound01(l, 100);
  if (s === 0) {
    g = l;
    b = l;
    r = l;
  } else {
    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    var p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }
  return { r: r * 255, g: g * 255, b: b * 255 };
}
function rgbToHsv(r, g, b) {
  r = bound01(r, 255);
  g = bound01(g, 255);
  b = bound01(b, 255);
  var max = Math.max(r, g, b);
  var min = Math.min(r, g, b);
  var h = 0;
  var v = max;
  var d = max - min;
  var s = max === 0 ? 0 : d / max;
  if (max === min) {
    h = 0;
  } else {
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
      default:
        break;
    }
    h /= 6;
  }
  return { h, s, v };
}
function hsvToRgb(h, s, v) {
  h = bound01(h, 360) * 6;
  s = bound01(s, 100);
  v = bound01(v, 100);
  var i = Math.floor(h);
  var f = h - i;
  var p = v * (1 - s);
  var q = v * (1 - f * s);
  var t = v * (1 - (1 - f) * s);
  var mod = i % 6;
  var r = [v, q, p, p, t, v][mod];
  var g = [t, v, v, q, p, p][mod];
  var b = [p, p, t, v, v, q][mod];
  return { r: r * 255, g: g * 255, b: b * 255 };
}
function rgbToHex(r, g, b, allow3Char) {
  var hex = [
    pad2(Math.round(r).toString(16)),
    pad2(Math.round(g).toString(16)),
    pad2(Math.round(b).toString(16))
  ];
  if (allow3Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1))) {
    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);
  }
  return hex.join("");
}
function rgbaToHex(r, g, b, a, allow4Char) {
  var hex = [
    pad2(Math.round(r).toString(16)),
    pad2(Math.round(g).toString(16)),
    pad2(Math.round(b).toString(16)),
    pad2(convertDecimalToHex(a))
  ];
  if (allow4Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1)) && hex[3].startsWith(hex[3].charAt(1))) {
    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);
  }
  return hex.join("");
}
function convertDecimalToHex(d) {
  return Math.round(parseFloat(d) * 255).toString(16);
}
function convertHexToDecimal(h) {
  return parseIntFromHex(h) / 255;
}
function parseIntFromHex(val) {
  return parseInt(val, 16);
}
function numberInputToObject(color) {
  return {
    r: color >> 16,
    g: (color & 65280) >> 8,
    b: color & 255
  };
}

// node_modules/@ctrl/tinycolor/dist/module/css-color-names.js
var names = {
  aliceblue: "#f0f8ff",
  antiquewhite: "#faebd7",
  aqua: "#00ffff",
  aquamarine: "#7fffd4",
  azure: "#f0ffff",
  beige: "#f5f5dc",
  bisque: "#ffe4c4",
  black: "#000000",
  blanchedalmond: "#ffebcd",
  blue: "#0000ff",
  blueviolet: "#8a2be2",
  brown: "#a52a2a",
  burlywood: "#deb887",
  cadetblue: "#5f9ea0",
  chartreuse: "#7fff00",
  chocolate: "#d2691e",
  coral: "#ff7f50",
  cornflowerblue: "#6495ed",
  cornsilk: "#fff8dc",
  crimson: "#dc143c",
  cyan: "#00ffff",
  darkblue: "#00008b",
  darkcyan: "#008b8b",
  darkgoldenrod: "#b8860b",
  darkgray: "#a9a9a9",
  darkgreen: "#006400",
  darkgrey: "#a9a9a9",
  darkkhaki: "#bdb76b",
  darkmagenta: "#8b008b",
  darkolivegreen: "#556b2f",
  darkorange: "#ff8c00",
  darkorchid: "#9932cc",
  darkred: "#8b0000",
  darksalmon: "#e9967a",
  darkseagreen: "#8fbc8f",
  darkslateblue: "#483d8b",
  darkslategray: "#2f4f4f",
  darkslategrey: "#2f4f4f",
  darkturquoise: "#00ced1",
  darkviolet: "#9400d3",
  deeppink: "#ff1493",
  deepskyblue: "#00bfff",
  dimgray: "#696969",
  dimgrey: "#696969",
  dodgerblue: "#1e90ff",
  firebrick: "#b22222",
  floralwhite: "#fffaf0",
  forestgreen: "#228b22",
  fuchsia: "#ff00ff",
  gainsboro: "#dcdcdc",
  ghostwhite: "#f8f8ff",
  goldenrod: "#daa520",
  gold: "#ffd700",
  gray: "#808080",
  green: "#008000",
  greenyellow: "#adff2f",
  grey: "#808080",
  honeydew: "#f0fff0",
  hotpink: "#ff69b4",
  indianred: "#cd5c5c",
  indigo: "#4b0082",
  ivory: "#fffff0",
  khaki: "#f0e68c",
  lavenderblush: "#fff0f5",
  lavender: "#e6e6fa",
  lawngreen: "#7cfc00",
  lemonchiffon: "#fffacd",
  lightblue: "#add8e6",
  lightcoral: "#f08080",
  lightcyan: "#e0ffff",
  lightgoldenrodyellow: "#fafad2",
  lightgray: "#d3d3d3",
  lightgreen: "#90ee90",
  lightgrey: "#d3d3d3",
  lightpink: "#ffb6c1",
  lightsalmon: "#ffa07a",
  lightseagreen: "#20b2aa",
  lightskyblue: "#87cefa",
  lightslategray: "#778899",
  lightslategrey: "#778899",
  lightsteelblue: "#b0c4de",
  lightyellow: "#ffffe0",
  lime: "#00ff00",
  limegreen: "#32cd32",
  linen: "#faf0e6",
  magenta: "#ff00ff",
  maroon: "#800000",
  mediumaquamarine: "#66cdaa",
  mediumblue: "#0000cd",
  mediumorchid: "#ba55d3",
  mediumpurple: "#9370db",
  mediumseagreen: "#3cb371",
  mediumslateblue: "#7b68ee",
  mediumspringgreen: "#00fa9a",
  mediumturquoise: "#48d1cc",
  mediumvioletred: "#c71585",
  midnightblue: "#191970",
  mintcream: "#f5fffa",
  mistyrose: "#ffe4e1",
  moccasin: "#ffe4b5",
  navajowhite: "#ffdead",
  navy: "#000080",
  oldlace: "#fdf5e6",
  olive: "#808000",
  olivedrab: "#6b8e23",
  orange: "#ffa500",
  orangered: "#ff4500",
  orchid: "#da70d6",
  palegoldenrod: "#eee8aa",
  palegreen: "#98fb98",
  paleturquoise: "#afeeee",
  palevioletred: "#db7093",
  papayawhip: "#ffefd5",
  peachpuff: "#ffdab9",
  peru: "#cd853f",
  pink: "#ffc0cb",
  plum: "#dda0dd",
  powderblue: "#b0e0e6",
  purple: "#800080",
  rebeccapurple: "#663399",
  red: "#ff0000",
  rosybrown: "#bc8f8f",
  royalblue: "#4169e1",
  saddlebrown: "#8b4513",
  salmon: "#fa8072",
  sandybrown: "#f4a460",
  seagreen: "#2e8b57",
  seashell: "#fff5ee",
  sienna: "#a0522d",
  silver: "#c0c0c0",
  skyblue: "#87ceeb",
  slateblue: "#6a5acd",
  slategray: "#708090",
  slategrey: "#708090",
  snow: "#fffafa",
  springgreen: "#00ff7f",
  steelblue: "#4682b4",
  tan: "#d2b48c",
  teal: "#008080",
  thistle: "#d8bfd8",
  tomato: "#ff6347",
  turquoise: "#40e0d0",
  violet: "#ee82ee",
  wheat: "#f5deb3",
  white: "#ffffff",
  whitesmoke: "#f5f5f5",
  yellow: "#ffff00",
  yellowgreen: "#9acd32"
};

// node_modules/@ctrl/tinycolor/dist/module/format-input.js
function inputToRGB(color) {
  var rgb = { r: 0, g: 0, b: 0 };
  var a = 1;
  var s = null;
  var v = null;
  var l = null;
  var ok = false;
  var format = false;
  if (typeof color === "string") {
    color = stringInputToObject(color);
  }
  if (typeof color === "object") {
    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {
      rgb = rgbToRgb(color.r, color.g, color.b);
      ok = true;
      format = String(color.r).substr(-1) === "%" ? "prgb" : "rgb";
    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {
      s = convertToPercentage(color.s);
      v = convertToPercentage(color.v);
      rgb = hsvToRgb(color.h, s, v);
      ok = true;
      format = "hsv";
    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {
      s = convertToPercentage(color.s);
      l = convertToPercentage(color.l);
      rgb = hslToRgb(color.h, s, l);
      ok = true;
      format = "hsl";
    }
    if (Object.prototype.hasOwnProperty.call(color, "a")) {
      a = color.a;
    }
  }
  a = boundAlpha(a);
  return {
    ok,
    format: color.format || format,
    r: Math.min(255, Math.max(rgb.r, 0)),
    g: Math.min(255, Math.max(rgb.g, 0)),
    b: Math.min(255, Math.max(rgb.b, 0)),
    a
  };
}
var CSS_INTEGER = "[-\\+]?\\d+%?";
var CSS_NUMBER = "[-\\+]?\\d*\\.\\d+%?";
var CSS_UNIT = "(?:".concat(CSS_NUMBER, ")|(?:").concat(CSS_INTEGER, ")");
var PERMISSIVE_MATCH3 = "[\\s|\\(]+(".concat(CSS_UNIT, ")[,|\\s]+(").concat(CSS_UNIT, ")[,|\\s]+(").concat(CSS_UNIT, ")\\s*\\)?");
var PERMISSIVE_MATCH4 = "[\\s|\\(]+(".concat(CSS_UNIT, ")[,|\\s]+(").concat(CSS_UNIT, ")[,|\\s]+(").concat(CSS_UNIT, ")[,|\\s]+(").concat(CSS_UNIT, ")\\s*\\)?");
var matchers = {
  CSS_UNIT: new RegExp(CSS_UNIT),
  rgb: new RegExp("rgb" + PERMISSIVE_MATCH3),
  rgba: new RegExp("rgba" + PERMISSIVE_MATCH4),
  hsl: new RegExp("hsl" + PERMISSIVE_MATCH3),
  hsla: new RegExp("hsla" + PERMISSIVE_MATCH4),
  hsv: new RegExp("hsv" + PERMISSIVE_MATCH3),
  hsva: new RegExp("hsva" + PERMISSIVE_MATCH4),
  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
};
function stringInputToObject(color) {
  color = color.trim().toLowerCase();
  if (color.length === 0) {
    return false;
  }
  var named = false;
  if (names[color]) {
    color = names[color];
    named = true;
  } else if (color === "transparent") {
    return { r: 0, g: 0, b: 0, a: 0, format: "name" };
  }
  var match = matchers.rgb.exec(color);
  if (match) {
    return { r: match[1], g: match[2], b: match[3] };
  }
  match = matchers.rgba.exec(color);
  if (match) {
    return { r: match[1], g: match[2], b: match[3], a: match[4] };
  }
  match = matchers.hsl.exec(color);
  if (match) {
    return { h: match[1], s: match[2], l: match[3] };
  }
  match = matchers.hsla.exec(color);
  if (match) {
    return { h: match[1], s: match[2], l: match[3], a: match[4] };
  }
  match = matchers.hsv.exec(color);
  if (match) {
    return { h: match[1], s: match[2], v: match[3] };
  }
  match = matchers.hsva.exec(color);
  if (match) {
    return { h: match[1], s: match[2], v: match[3], a: match[4] };
  }
  match = matchers.hex8.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1]),
      g: parseIntFromHex(match[2]),
      b: parseIntFromHex(match[3]),
      a: convertHexToDecimal(match[4]),
      format: named ? "name" : "hex8"
    };
  }
  match = matchers.hex6.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1]),
      g: parseIntFromHex(match[2]),
      b: parseIntFromHex(match[3]),
      format: named ? "name" : "hex"
    };
  }
  match = matchers.hex4.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1] + match[1]),
      g: parseIntFromHex(match[2] + match[2]),
      b: parseIntFromHex(match[3] + match[3]),
      a: convertHexToDecimal(match[4] + match[4]),
      format: named ? "name" : "hex8"
    };
  }
  match = matchers.hex3.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1] + match[1]),
      g: parseIntFromHex(match[2] + match[2]),
      b: parseIntFromHex(match[3] + match[3]),
      format: named ? "name" : "hex"
    };
  }
  return false;
}
function isValidCSSUnit(color) {
  return Boolean(matchers.CSS_UNIT.exec(String(color)));
}

// node_modules/@ctrl/tinycolor/dist/module/index.js
var TinyColor = (
  /** @class */
  function() {
    function TinyColor2(color, opts) {
      if (color === void 0) {
        color = "";
      }
      if (opts === void 0) {
        opts = {};
      }
      var _a;
      if (color instanceof TinyColor2) {
        return color;
      }
      if (typeof color === "number") {
        color = numberInputToObject(color);
      }
      this.originalInput = color;
      var rgb = inputToRGB(color);
      this.originalInput = color;
      this.r = rgb.r;
      this.g = rgb.g;
      this.b = rgb.b;
      this.a = rgb.a;
      this.roundA = Math.round(100 * this.a) / 100;
      this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;
      this.gradientType = opts.gradientType;
      if (this.r < 1) {
        this.r = Math.round(this.r);
      }
      if (this.g < 1) {
        this.g = Math.round(this.g);
      }
      if (this.b < 1) {
        this.b = Math.round(this.b);
      }
      this.isValid = rgb.ok;
    }
    TinyColor2.prototype.isDark = function() {
      return this.getBrightness() < 128;
    };
    TinyColor2.prototype.isLight = function() {
      return !this.isDark();
    };
    TinyColor2.prototype.getBrightness = function() {
      var rgb = this.toRgb();
      return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1e3;
    };
    TinyColor2.prototype.getLuminance = function() {
      var rgb = this.toRgb();
      var R;
      var G;
      var B;
      var RsRGB = rgb.r / 255;
      var GsRGB = rgb.g / 255;
      var BsRGB = rgb.b / 255;
      if (RsRGB <= 0.03928) {
        R = RsRGB / 12.92;
      } else {
        R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);
      }
      if (GsRGB <= 0.03928) {
        G = GsRGB / 12.92;
      } else {
        G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);
      }
      if (BsRGB <= 0.03928) {
        B = BsRGB / 12.92;
      } else {
        B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);
      }
      return 0.2126 * R + 0.7152 * G + 0.0722 * B;
    };
    TinyColor2.prototype.getAlpha = function() {
      return this.a;
    };
    TinyColor2.prototype.setAlpha = function(alpha) {
      this.a = boundAlpha(alpha);
      this.roundA = Math.round(100 * this.a) / 100;
      return this;
    };
    TinyColor2.prototype.isMonochrome = function() {
      var s = this.toHsl().s;
      return s === 0;
    };
    TinyColor2.prototype.toHsv = function() {
      var hsv = rgbToHsv(this.r, this.g, this.b);
      return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };
    };
    TinyColor2.prototype.toHsvString = function() {
      var hsv = rgbToHsv(this.r, this.g, this.b);
      var h = Math.round(hsv.h * 360);
      var s = Math.round(hsv.s * 100);
      var v = Math.round(hsv.v * 100);
      return this.a === 1 ? "hsv(".concat(h, ", ").concat(s, "%, ").concat(v, "%)") : "hsva(".concat(h, ", ").concat(s, "%, ").concat(v, "%, ").concat(this.roundA, ")");
    };
    TinyColor2.prototype.toHsl = function() {
      var hsl = rgbToHsl(this.r, this.g, this.b);
      return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };
    };
    TinyColor2.prototype.toHslString = function() {
      var hsl = rgbToHsl(this.r, this.g, this.b);
      var h = Math.round(hsl.h * 360);
      var s = Math.round(hsl.s * 100);
      var l = Math.round(hsl.l * 100);
      return this.a === 1 ? "hsl(".concat(h, ", ").concat(s, "%, ").concat(l, "%)") : "hsla(".concat(h, ", ").concat(s, "%, ").concat(l, "%, ").concat(this.roundA, ")");
    };
    TinyColor2.prototype.toHex = function(allow3Char) {
      if (allow3Char === void 0) {
        allow3Char = false;
      }
      return rgbToHex(this.r, this.g, this.b, allow3Char);
    };
    TinyColor2.prototype.toHexString = function(allow3Char) {
      if (allow3Char === void 0) {
        allow3Char = false;
      }
      return "#" + this.toHex(allow3Char);
    };
    TinyColor2.prototype.toHex8 = function(allow4Char) {
      if (allow4Char === void 0) {
        allow4Char = false;
      }
      return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);
    };
    TinyColor2.prototype.toHex8String = function(allow4Char) {
      if (allow4Char === void 0) {
        allow4Char = false;
      }
      return "#" + this.toHex8(allow4Char);
    };
    TinyColor2.prototype.toHexShortString = function(allowShortChar) {
      if (allowShortChar === void 0) {
        allowShortChar = false;
      }
      return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);
    };
    TinyColor2.prototype.toRgb = function() {
      return {
        r: Math.round(this.r),
        g: Math.round(this.g),
        b: Math.round(this.b),
        a: this.a
      };
    };
    TinyColor2.prototype.toRgbString = function() {
      var r = Math.round(this.r);
      var g = Math.round(this.g);
      var b = Math.round(this.b);
      return this.a === 1 ? "rgb(".concat(r, ", ").concat(g, ", ").concat(b, ")") : "rgba(".concat(r, ", ").concat(g, ", ").concat(b, ", ").concat(this.roundA, ")");
    };
    TinyColor2.prototype.toPercentageRgb = function() {
      var fmt = function(x) {
        return "".concat(Math.round(bound01(x, 255) * 100), "%");
      };
      return {
        r: fmt(this.r),
        g: fmt(this.g),
        b: fmt(this.b),
        a: this.a
      };
    };
    TinyColor2.prototype.toPercentageRgbString = function() {
      var rnd = function(x) {
        return Math.round(bound01(x, 255) * 100);
      };
      return this.a === 1 ? "rgb(".concat(rnd(this.r), "%, ").concat(rnd(this.g), "%, ").concat(rnd(this.b), "%)") : "rgba(".concat(rnd(this.r), "%, ").concat(rnd(this.g), "%, ").concat(rnd(this.b), "%, ").concat(this.roundA, ")");
    };
    TinyColor2.prototype.toName = function() {
      if (this.a === 0) {
        return "transparent";
      }
      if (this.a < 1) {
        return false;
      }
      var hex = "#" + rgbToHex(this.r, this.g, this.b, false);
      for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {
        var _b = _a[_i], key = _b[0], value = _b[1];
        if (hex === value) {
          return key;
        }
      }
      return false;
    };
    TinyColor2.prototype.toString = function(format) {
      var formatSet = Boolean(format);
      format = format !== null && format !== void 0 ? format : this.format;
      var formattedString = false;
      var hasAlpha = this.a < 1 && this.a >= 0;
      var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith("hex") || format === "name");
      if (needsAlphaFormat) {
        if (format === "name" && this.a === 0) {
          return this.toName();
        }
        return this.toRgbString();
      }
      if (format === "rgb") {
        formattedString = this.toRgbString();
      }
      if (format === "prgb") {
        formattedString = this.toPercentageRgbString();
      }
      if (format === "hex" || format === "hex6") {
        formattedString = this.toHexString();
      }
      if (format === "hex3") {
        formattedString = this.toHexString(true);
      }
      if (format === "hex4") {
        formattedString = this.toHex8String(true);
      }
      if (format === "hex8") {
        formattedString = this.toHex8String();
      }
      if (format === "name") {
        formattedString = this.toName();
      }
      if (format === "hsl") {
        formattedString = this.toHslString();
      }
      if (format === "hsv") {
        formattedString = this.toHsvString();
      }
      return formattedString || this.toHexString();
    };
    TinyColor2.prototype.toNumber = function() {
      return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);
    };
    TinyColor2.prototype.clone = function() {
      return new TinyColor2(this.toString());
    };
    TinyColor2.prototype.lighten = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      var hsl = this.toHsl();
      hsl.l += amount / 100;
      hsl.l = clamp01(hsl.l);
      return new TinyColor2(hsl);
    };
    TinyColor2.prototype.brighten = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      var rgb = this.toRgb();
      rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));
      rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));
      rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));
      return new TinyColor2(rgb);
    };
    TinyColor2.prototype.darken = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      var hsl = this.toHsl();
      hsl.l -= amount / 100;
      hsl.l = clamp01(hsl.l);
      return new TinyColor2(hsl);
    };
    TinyColor2.prototype.tint = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      return this.mix("white", amount);
    };
    TinyColor2.prototype.shade = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      return this.mix("black", amount);
    };
    TinyColor2.prototype.desaturate = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      var hsl = this.toHsl();
      hsl.s -= amount / 100;
      hsl.s = clamp01(hsl.s);
      return new TinyColor2(hsl);
    };
    TinyColor2.prototype.saturate = function(amount) {
      if (amount === void 0) {
        amount = 10;
      }
      var hsl = this.toHsl();
      hsl.s += amount / 100;
      hsl.s = clamp01(hsl.s);
      return new TinyColor2(hsl);
    };
    TinyColor2.prototype.greyscale = function() {
      return this.desaturate(100);
    };
    TinyColor2.prototype.spin = function(amount) {
      var hsl = this.toHsl();
      var hue = (hsl.h + amount) % 360;
      hsl.h = hue < 0 ? 360 + hue : hue;
      return new TinyColor2(hsl);
    };
    TinyColor2.prototype.mix = function(color, amount) {
      if (amount === void 0) {
        amount = 50;
      }
      var rgb1 = this.toRgb();
      var rgb2 = new TinyColor2(color).toRgb();
      var p = amount / 100;
      var rgba = {
        r: (rgb2.r - rgb1.r) * p + rgb1.r,
        g: (rgb2.g - rgb1.g) * p + rgb1.g,
        b: (rgb2.b - rgb1.b) * p + rgb1.b,
        a: (rgb2.a - rgb1.a) * p + rgb1.a
      };
      return new TinyColor2(rgba);
    };
    TinyColor2.prototype.analogous = function(results, slices) {
      if (results === void 0) {
        results = 6;
      }
      if (slices === void 0) {
        slices = 30;
      }
      var hsl = this.toHsl();
      var part = 360 / slices;
      var ret = [this];
      for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results; ) {
        hsl.h = (hsl.h + part) % 360;
        ret.push(new TinyColor2(hsl));
      }
      return ret;
    };
    TinyColor2.prototype.complement = function() {
      var hsl = this.toHsl();
      hsl.h = (hsl.h + 180) % 360;
      return new TinyColor2(hsl);
    };
    TinyColor2.prototype.monochromatic = function(results) {
      if (results === void 0) {
        results = 6;
      }
      var hsv = this.toHsv();
      var h = hsv.h;
      var s = hsv.s;
      var v = hsv.v;
      var res = [];
      var modification = 1 / results;
      while (results--) {
        res.push(new TinyColor2({ h, s, v }));
        v = (v + modification) % 1;
      }
      return res;
    };
    TinyColor2.prototype.splitcomplement = function() {
      var hsl = this.toHsl();
      var h = hsl.h;
      return [
        this,
        new TinyColor2({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),
        new TinyColor2({ h: (h + 216) % 360, s: hsl.s, l: hsl.l })
      ];
    };
    TinyColor2.prototype.onBackground = function(background) {
      var fg = this.toRgb();
      var bg = new TinyColor2(background).toRgb();
      var alpha = fg.a + bg.a * (1 - fg.a);
      return new TinyColor2({
        r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,
        g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,
        b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,
        a: alpha
      });
    };
    TinyColor2.prototype.triad = function() {
      return this.polyad(3);
    };
    TinyColor2.prototype.tetrad = function() {
      return this.polyad(4);
    };
    TinyColor2.prototype.polyad = function(n) {
      var hsl = this.toHsl();
      var h = hsl.h;
      var result = [this];
      var increment = 360 / n;
      for (var i = 1; i < n; i++) {
        result.push(new TinyColor2({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));
      }
      return result;
    };
    TinyColor2.prototype.equals = function(color) {
      return this.toRgbString() === new TinyColor2(color).toRgbString();
    };
    return TinyColor2;
  }()
);

// node_modules/@ant-design/pro-provider/es/useStyle/index.js
var import_react5 = __toESM(require_react());

// node_modules/rc-picker/es/locale/zh_CN.js
var locale = _objectSpread2(_objectSpread2({}, commonLocale), {}, {
  locale: "zh_CN",
  today: "今天",
  now: "此刻",
  backToToday: "返回今天",
  ok: "确定",
  timeSelect: "选择时间",
  dateSelect: "选择日期",
  weekSelect: "选择周",
  clear: "清除",
  week: "周",
  month: "月",
  year: "年",
  previousMonth: "上个月 (翻页上键)",
  nextMonth: "下个月 (翻页下键)",
  monthSelect: "选择月份",
  yearSelect: "选择年份",
  decadeSelect: "选择年代",
  previousYear: "上一年 (Control键加左方向键)",
  nextYear: "下一年 (Control键加右方向键)",
  previousDecade: "上一年代",
  nextDecade: "下一年代",
  previousCentury: "上一世纪",
  nextCentury: "下一世纪",
  yearFormat: "YYYY年",
  cellDateFormat: "D",
  monthBeforeYear: false
});
var zh_CN_default3 = locale;

// node_modules/antd/es/time-picker/locale/zh_CN.js
var locale2 = {
  placeholder: "请选择时间",
  rangePlaceholder: ["开始时间", "结束时间"]
};
var zh_CN_default4 = locale2;

// node_modules/antd/es/date-picker/locale/zh_CN.js
var locale3 = {
  lang: Object.assign({
    placeholder: "请选择日期",
    yearPlaceholder: "请选择年份",
    quarterPlaceholder: "请选择季度",
    monthPlaceholder: "请选择月份",
    weekPlaceholder: "请选择周",
    rangePlaceholder: ["开始日期", "结束日期"],
    rangeYearPlaceholder: ["开始年份", "结束年份"],
    rangeMonthPlaceholder: ["开始月份", "结束月份"],
    rangeQuarterPlaceholder: ["开始季度", "结束季度"],
    rangeWeekPlaceholder: ["开始周", "结束周"]
  }, zh_CN_default3),
  timePickerLocale: Object.assign({}, zh_CN_default4)
};
locale3.lang.ok = "确定";
var zh_CN_default5 = locale3;

// node_modules/antd/es/calendar/locale/zh_CN.js
var zh_CN_default6 = zh_CN_default5;

// node_modules/antd/es/locale/zh_CN.js
var typeTemplate = "${label}不是一个有效的${type}";
var localeValues = {
  locale: "zh-cn",
  Pagination: zh_CN_default,
  DatePicker: zh_CN_default5,
  TimePicker: zh_CN_default4,
  Calendar: zh_CN_default6,
  // locales for all components
  global: {
    placeholder: "请选择",
    close: "关闭"
  },
  Table: {
    filterTitle: "筛选",
    filterConfirm: "确定",
    filterReset: "重置",
    filterEmptyText: "无筛选项",
    filterCheckAll: "全选",
    filterSearchPlaceholder: "在筛选项中搜索",
    emptyText: "暂无数据",
    selectAll: "全选当页",
    selectInvert: "反选当页",
    selectNone: "清空所有",
    selectionAll: "全选所有",
    sortTitle: "排序",
    expand: "展开行",
    collapse: "关闭行",
    triggerDesc: "点击降序",
    triggerAsc: "点击升序",
    cancelSort: "取消排序"
  },
  Modal: {
    okText: "确定",
    cancelText: "取消",
    justOkText: "知道了"
  },
  Tour: {
    Next: "下一步",
    Previous: "上一步",
    Finish: "结束导览"
  },
  Popconfirm: {
    cancelText: "取消",
    okText: "确定"
  },
  Transfer: {
    titles: ["", ""],
    searchPlaceholder: "请输入搜索内容",
    itemUnit: "项",
    itemsUnit: "项",
    remove: "删除",
    selectCurrent: "全选当页",
    removeCurrent: "删除当页",
    selectAll: "全选所有",
    deselectAll: "取消全选",
    removeAll: "删除全部",
    selectInvert: "反选当页"
  },
  Upload: {
    uploading: "文件上传中",
    removeFile: "删除文件",
    uploadError: "上传错误",
    previewFile: "预览文件",
    downloadFile: "下载文件"
  },
  Empty: {
    description: "暂无数据"
  },
  Icon: {
    icon: "图标"
  },
  Text: {
    edit: "编辑",
    copy: "复制",
    copied: "复制成功",
    expand: "展开",
    collapse: "收起"
  },
  Form: {
    optional: "（可选）",
    defaultValidateMessages: {
      default: "字段验证错误${label}",
      required: "请输入${label}",
      enum: "${label}必须是其中一个[${enum}]",
      whitespace: "${label}不能为空字符",
      date: {
        format: "${label}日期格式无效",
        parse: "${label}不能转换为日期",
        invalid: "${label}是一个无效日期"
      },
      types: {
        string: typeTemplate,
        method: typeTemplate,
        array: typeTemplate,
        object: typeTemplate,
        number: typeTemplate,
        date: typeTemplate,
        boolean: typeTemplate,
        integer: typeTemplate,
        float: typeTemplate,
        regexp: typeTemplate,
        email: typeTemplate,
        url: typeTemplate,
        hex: typeTemplate
      },
      string: {
        len: "${label}须为${len}个字符",
        min: "${label}最少${min}个字符",
        max: "${label}最多${max}个字符",
        range: "${label}须在${min}-${max}字符之间"
      },
      number: {
        len: "${label}必须等于${len}",
        min: "${label}最小值为${min}",
        max: "${label}最大值为${max}",
        range: "${label}须在${min}-${max}之间"
      },
      array: {
        len: "须为${len}个${label}",
        min: "最少${min}个${label}",
        max: "最多${max}个${label}",
        range: "${label}数量须在${min}-${max}之间"
      },
      pattern: {
        mismatch: "${label}与模式不匹配${pattern}"
      }
    }
  },
  Image: {
    preview: "预览"
  },
  QRCode: {
    expired: "二维码过期",
    refresh: "点击刷新",
    scanned: "已扫描"
  },
  ColorPicker: {
    presetEmpty: "暂无",
    transparent: "无色",
    singleColor: "单色",
    gradientColor: "渐变色"
  }
};
var zh_CN_default7 = localeValues;

// node_modules/@ant-design/pro-provider/es/index.js
var import_react4 = __toESM(require_react());

// node_modules/swr/dist/index/index.mjs
var import_react3 = __toESM(require_react(), 1);
var import_shim = __toESM(require_shim(), 1);

// node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs
var import_react = __toESM(require_react(), 1);

// node_modules/swr/dist/_internal/events.mjs
var events_exports = {};
__export(events_exports, {
  ERROR_REVALIDATE_EVENT: () => ERROR_REVALIDATE_EVENT,
  FOCUS_EVENT: () => FOCUS_EVENT,
  MUTATE_EVENT: () => MUTATE_EVENT,
  RECONNECT_EVENT: () => RECONNECT_EVENT
});
var FOCUS_EVENT = 0;
var RECONNECT_EVENT = 1;
var MUTATE_EVENT = 2;
var ERROR_REVALIDATE_EVENT = 3;

// node_modules/dequal/lite/index.mjs
var has = Object.prototype.hasOwnProperty;
function dequal(foo, bar) {
  var ctor, len;
  if (foo === bar)
    return true;
  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {
    if (ctor === Date)
      return foo.getTime() === bar.getTime();
    if (ctor === RegExp)
      return foo.toString() === bar.toString();
    if (ctor === Array) {
      if ((len = foo.length) === bar.length) {
        while (len-- && dequal(foo[len], bar[len]))
          ;
      }
      return len === -1;
    }
    if (!ctor || typeof foo === "object") {
      len = 0;
      for (ctor in foo) {
        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor))
          return false;
        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor]))
          return false;
      }
      return Object.keys(bar).length === len;
    }
  }
  return foo !== foo && bar !== bar;
}

// node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs
var SWRGlobalState = /* @__PURE__ */ new WeakMap();
var noop = () => {
};
var UNDEFINED = (
  /*#__NOINLINE__*/
  noop()
);
var OBJECT = Object;
var isUndefined = (v) => v === UNDEFINED;
var isFunction = (v) => typeof v == "function";
var mergeObjects = (a, b) => ({
  ...a,
  ...b
});
var isPromiseLike = (x) => isFunction(x.then);
var EMPTY_CACHE = {};
var INITIAL_CACHE = {};
var STR_UNDEFINED = "undefined";
var isWindowDefined = typeof window != STR_UNDEFINED;
var isDocumentDefined = typeof document != STR_UNDEFINED;
var isLegacyDeno = isWindowDefined && "Deno" in window;
var hasRequestAnimationFrame = () => isWindowDefined && typeof window["requestAnimationFrame"] != STR_UNDEFINED;
var createCacheHelper = (cache2, key) => {
  const state = SWRGlobalState.get(cache2);
  return [
    // Getter
    () => !isUndefined(key) && cache2.get(key) || EMPTY_CACHE,
    // Setter
    (info) => {
      if (!isUndefined(key)) {
        const prev = cache2.get(key);
        if (!(key in INITIAL_CACHE)) {
          INITIAL_CACHE[key] = prev;
        }
        state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);
      }
    },
    // Subscriber
    state[6],
    // Get server cache snapshot
    () => {
      if (!isUndefined(key)) {
        if (key in INITIAL_CACHE)
          return INITIAL_CACHE[key];
      }
      return !isUndefined(key) && cache2.get(key) || EMPTY_CACHE;
    }
  ];
};
var online = true;
var isOnline = () => online;
var [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [
  window.addEventListener.bind(window),
  window.removeEventListener.bind(window)
] : [
  noop,
  noop
];
var isVisible = () => {
  const visibilityState = isDocumentDefined && document.visibilityState;
  return isUndefined(visibilityState) || visibilityState !== "hidden";
};
var initFocus = (callback) => {
  if (isDocumentDefined) {
    document.addEventListener("visibilitychange", callback);
  }
  onWindowEvent("focus", callback);
  return () => {
    if (isDocumentDefined) {
      document.removeEventListener("visibilitychange", callback);
    }
    offWindowEvent("focus", callback);
  };
};
var initReconnect = (callback) => {
  const onOnline = () => {
    online = true;
    callback();
  };
  const onOffline = () => {
    online = false;
  };
  onWindowEvent("online", onOnline);
  onWindowEvent("offline", onOffline);
  return () => {
    offWindowEvent("online", onOnline);
    offWindowEvent("offline", onOffline);
  };
};
var preset = {
  isOnline,
  isVisible
};
var defaultConfigOptions = {
  initFocus,
  initReconnect
};
var IS_REACT_LEGACY = !import_react.default.useId;
var IS_SERVER = !isWindowDefined || isLegacyDeno;
var rAF = (f) => hasRequestAnimationFrame() ? window["requestAnimationFrame"](f) : setTimeout(f, 1);
var useIsomorphicLayoutEffect = IS_SERVER ? import_react.useEffect : import_react.useLayoutEffect;
var navigatorConnection = typeof navigator !== "undefined" && navigator.connection;
var slowConnection = !IS_SERVER && navigatorConnection && ([
  "slow-2g",
  "2g"
].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);
var table = /* @__PURE__ */ new WeakMap();
var isObjectType = (value, type) => OBJECT.prototype.toString.call(value) === `[object ${type}]`;
var counter = 0;
var stableHash = (arg) => {
  const type = typeof arg;
  const isDate = isObjectType(arg, "Date");
  const isRegex = isObjectType(arg, "RegExp");
  const isPlainObject3 = isObjectType(arg, "Object");
  let result;
  let index2;
  if (OBJECT(arg) === arg && !isDate && !isRegex) {
    result = table.get(arg);
    if (result)
      return result;
    result = ++counter + "~";
    table.set(arg, result);
    if (Array.isArray(arg)) {
      result = "@";
      for (index2 = 0; index2 < arg.length; index2++) {
        result += stableHash(arg[index2]) + ",";
      }
      table.set(arg, result);
    }
    if (isPlainObject3) {
      result = "#";
      const keys = OBJECT.keys(arg).sort();
      while (!isUndefined(index2 = keys.pop())) {
        if (!isUndefined(arg[index2])) {
          result += index2 + ":" + stableHash(arg[index2]) + ",";
        }
      }
      table.set(arg, result);
    }
  } else {
    result = isDate ? arg.toJSON() : type == "symbol" ? arg.toString() : type == "string" ? JSON.stringify(arg) : "" + arg;
  }
  return result;
};
var serialize = (key) => {
  if (isFunction(key)) {
    try {
      key = key();
    } catch (err) {
      key = "";
    }
  }
  const args = key;
  key = typeof key == "string" ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : "";
  return [
    key,
    args
  ];
};
var __timestamp = 0;
var getTimestamp = () => ++__timestamp;
async function internalMutate(...args) {
  const [cache2, _key, _data, _opts] = args;
  const options = mergeObjects({
    populateCache: true,
    throwOnError: true
  }, typeof _opts === "boolean" ? {
    revalidate: _opts
  } : _opts || {});
  let populateCache = options.populateCache;
  const rollbackOnErrorOption = options.rollbackOnError;
  let optimisticData = options.optimisticData;
  const rollbackOnError = (error2) => {
    return typeof rollbackOnErrorOption === "function" ? rollbackOnErrorOption(error2) : rollbackOnErrorOption !== false;
  };
  const throwOnError = options.throwOnError;
  if (isFunction(_key)) {
    const keyFilter = _key;
    const matchedKeys = [];
    const it = cache2.keys();
    for (const key of it) {
      if (
        // Skip the special useSWRInfinite and useSWRSubscription keys.
        !/^\$(inf|sub)\$/.test(key) && keyFilter(cache2.get(key)._k)
      ) {
        matchedKeys.push(key);
      }
    }
    return Promise.all(matchedKeys.map(mutateByKey));
  }
  return mutateByKey(_key);
  async function mutateByKey(_k) {
    const [key] = serialize(_k);
    if (!key)
      return;
    const [get2, set2] = createCacheHelper(cache2, key);
    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache2);
    const startRevalidate = () => {
      const revalidators = EVENT_REVALIDATORS[key];
      const revalidate = isFunction(options.revalidate) ? options.revalidate(get2().data, _k) : options.revalidate !== false;
      if (revalidate) {
        delete FETCH[key];
        delete PRELOAD[key];
        if (revalidators && revalidators[0]) {
          return revalidators[0](MUTATE_EVENT).then(() => get2().data);
        }
      }
      return get2().data;
    };
    if (args.length < 3) {
      return startRevalidate();
    }
    let data = _data;
    let error2;
    const beforeMutationTs = getTimestamp();
    MUTATION[key] = [
      beforeMutationTs,
      0
    ];
    const hasOptimisticData = !isUndefined(optimisticData);
    const state = get2();
    const displayedData = state.data;
    const currentData = state._c;
    const committedData = isUndefined(currentData) ? displayedData : currentData;
    if (hasOptimisticData) {
      optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;
      set2({
        data: optimisticData,
        _c: committedData
      });
    }
    if (isFunction(data)) {
      try {
        data = data(committedData);
      } catch (err) {
        error2 = err;
      }
    }
    if (data && isPromiseLike(data)) {
      data = await data.catch((err) => {
        error2 = err;
      });
      if (beforeMutationTs !== MUTATION[key][0]) {
        if (error2)
          throw error2;
        return data;
      } else if (error2 && hasOptimisticData && rollbackOnError(error2)) {
        populateCache = true;
        set2({
          data: committedData,
          _c: UNDEFINED
        });
      }
    }
    if (populateCache) {
      if (!error2) {
        if (isFunction(populateCache)) {
          const populateCachedData = populateCache(data, committedData);
          set2({
            data: populateCachedData,
            error: UNDEFINED,
            _c: UNDEFINED
          });
        } else {
          set2({
            data,
            error: UNDEFINED,
            _c: UNDEFINED
          });
        }
      }
    }
    MUTATION[key][1] = getTimestamp();
    Promise.resolve(startRevalidate()).then(() => {
      set2({
        _c: UNDEFINED
      });
    });
    if (error2) {
      if (throwOnError)
        throw error2;
      return;
    }
    return data;
  }
}
var revalidateAllKeys = (revalidators, type) => {
  for (const key in revalidators) {
    if (revalidators[key][0])
      revalidators[key][0](type);
  }
};
var initCache = (provider, options) => {
  if (!SWRGlobalState.has(provider)) {
    const opts = mergeObjects(defaultConfigOptions, options);
    const EVENT_REVALIDATORS = /* @__PURE__ */ Object.create(null);
    const mutate2 = internalMutate.bind(UNDEFINED, provider);
    let unmount = noop;
    const subscriptions = /* @__PURE__ */ Object.create(null);
    const subscribe = (key, callback) => {
      const subs = subscriptions[key] || [];
      subscriptions[key] = subs;
      subs.push(callback);
      return () => subs.splice(subs.indexOf(callback), 1);
    };
    const setter = (key, value, prev) => {
      provider.set(key, value);
      const subs = subscriptions[key];
      if (subs) {
        for (const fn of subs) {
          fn(value, prev);
        }
      }
    };
    const initProvider = () => {
      if (!SWRGlobalState.has(provider)) {
        SWRGlobalState.set(provider, [
          EVENT_REVALIDATORS,
          /* @__PURE__ */ Object.create(null),
          /* @__PURE__ */ Object.create(null),
          /* @__PURE__ */ Object.create(null),
          mutate2,
          setter,
          subscribe
        ]);
        if (!IS_SERVER) {
          const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));
          const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));
          unmount = () => {
            releaseFocus && releaseFocus();
            releaseReconnect && releaseReconnect();
            SWRGlobalState.delete(provider);
          };
        }
      }
    };
    initProvider();
    return [
      provider,
      mutate2,
      initProvider,
      unmount
    ];
  }
  return [
    provider,
    SWRGlobalState.get(provider)[4]
  ];
};
var onErrorRetry = (_, __, config, revalidate, opts) => {
  const maxRetryCount = config.errorRetryCount;
  const currentRetryCount = opts.retryCount;
  const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;
  if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {
    return;
  }
  setTimeout(revalidate, timeout, opts);
};
var compare = dequal;
var [cache, mutate] = initCache(/* @__PURE__ */ new Map());
var defaultConfig = mergeObjects(
  {
    // events
    onLoadingSlow: noop,
    onSuccess: noop,
    onError: noop,
    onErrorRetry,
    onDiscarded: noop,
    // switches
    revalidateOnFocus: true,
    revalidateOnReconnect: true,
    revalidateIfStale: true,
    shouldRetryOnError: true,
    // timeouts
    errorRetryInterval: slowConnection ? 1e4 : 5e3,
    focusThrottleInterval: 5 * 1e3,
    dedupingInterval: 2 * 1e3,
    loadingTimeout: slowConnection ? 5e3 : 3e3,
    // providers
    compare,
    isPaused: () => false,
    cache,
    mutate,
    fallback: {}
  },
  // use web preset by default
  preset
);
var mergeConfigs = (a, b) => {
  const v = mergeObjects(a, b);
  if (b) {
    const { use: u1, fallback: f1 } = a;
    const { use: u2, fallback: f2 } = b;
    if (u1 && u2) {
      v.use = u1.concat(u2);
    }
    if (f1 && f2) {
      v.fallback = mergeObjects(f1, f2);
    }
  }
  return v;
};
var SWRConfigContext = (0, import_react.createContext)({});
var SWRConfig = (props) => {
  const { value } = props;
  const parentConfig = (0, import_react.useContext)(SWRConfigContext);
  const isFunctionalConfig = isFunction(value);
  const config = (0, import_react.useMemo)(() => isFunctionalConfig ? value(parentConfig) : value, [
    isFunctionalConfig,
    parentConfig,
    value
  ]);
  const extendedConfig = (0, import_react.useMemo)(() => isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [
    isFunctionalConfig,
    parentConfig,
    config
  ]);
  const provider = config && config.provider;
  const cacheContextRef = (0, import_react.useRef)(UNDEFINED);
  if (provider && !cacheContextRef.current) {
    cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);
  }
  const cacheContext = cacheContextRef.current;
  if (cacheContext) {
    extendedConfig.cache = cacheContext[0];
    extendedConfig.mutate = cacheContext[1];
  }
  useIsomorphicLayoutEffect(() => {
    if (cacheContext) {
      cacheContext[2] && cacheContext[2]();
      return cacheContext[3];
    }
  }, []);
  return (0, import_react.createElement)(SWRConfigContext.Provider, mergeObjects(props, {
    value: extendedConfig
  }));
};

// node_modules/swr/dist/_internal/constants.mjs
var INFINITE_PREFIX = "$inf$";

// node_modules/swr/dist/_internal/index.mjs
var import_react2 = __toESM(require_react(), 1);
var enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;
var use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];
var setupDevTools = () => {
  if (enableDevtools) {
    window.__SWR_DEVTOOLS_REACT__ = import_react2.default;
  }
};
var normalize = (args) => {
  return isFunction(args[1]) ? [
    args[0],
    args[1],
    args[2] || {}
  ] : [
    args[0],
    null,
    (args[1] === null ? args[2] : args[1]) || {}
  ];
};
var useSWRConfig = () => {
  return mergeObjects(defaultConfig, (0, import_react2.useContext)(SWRConfigContext));
};
var middleware = (useSWRNext) => (key_, fetcher_, config) => {
  const fetcher = fetcher_ && ((...args) => {
    const [key] = serialize(key_);
    const [, , , PRELOAD] = SWRGlobalState.get(cache);
    if (key.startsWith(INFINITE_PREFIX)) {
      return fetcher_(...args);
    }
    const req = PRELOAD[key];
    if (isUndefined(req))
      return fetcher_(...args);
    delete PRELOAD[key];
    return req;
  });
  return useSWRNext(key_, fetcher, config);
};
var BUILT_IN_MIDDLEWARE = use.concat(middleware);
var withArgs = (hook) => {
  return function useSWRArgs(...args) {
    const fallbackConfig = useSWRConfig();
    const [key, fn, _config] = normalize(args);
    const config = mergeConfigs(fallbackConfig, _config);
    let next = hook;
    const { use: use3 } = config;
    const middleware2 = (use3 || []).concat(BUILT_IN_MIDDLEWARE);
    for (let i = middleware2.length; i--; ) {
      next = middleware2[i](next);
    }
    return next(key, fn || config.fetcher || null, config);
  };
};
var subscribeCallback = (key, callbacks, callback) => {
  const keyedRevalidators = callbacks[key] || (callbacks[key] = []);
  keyedRevalidators.push(callback);
  return () => {
    const index2 = keyedRevalidators.indexOf(callback);
    if (index2 >= 0) {
      keyedRevalidators[index2] = keyedRevalidators[keyedRevalidators.length - 1];
      keyedRevalidators.pop();
    }
  };
};
setupDevTools();

// node_modules/swr/dist/index/index.mjs
var noop2 = () => {
};
var UNDEFINED2 = (
  /*#__NOINLINE__*/
  noop2()
);
var use2 = import_react3.default.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax
// and emitting an error.
// We assume that this is only for the `use(thenable)` case, not `use(context)`.
// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45
((thenable) => {
  switch (thenable.status) {
    case "pending":
      throw thenable;
    case "fulfilled":
      return thenable.value;
    case "rejected":
      throw thenable.reason;
    default:
      thenable.status = "pending";
      thenable.then((v) => {
        thenable.status = "fulfilled";
        thenable.value = v;
      }, (e) => {
        thenable.status = "rejected";
        thenable.reason = e;
      });
      throw thenable;
  }
});
var WITH_DEDUPE = {
  dedupe: true
};
var useSWRHandler = (_key, fetcher, config) => {
  const { cache: cache2, compare: compare2, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;
  const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache2);
  const [key, fnArg] = serialize(_key);
  const initialMountedRef = (0, import_react3.useRef)(false);
  const unmountedRef = (0, import_react3.useRef)(false);
  const keyRef = (0, import_react3.useRef)(key);
  const fetcherRef = (0, import_react3.useRef)(fetcher);
  const configRef = (0, import_react3.useRef)(config);
  const getConfig = () => configRef.current;
  const isActive = () => getConfig().isVisible() && getConfig().isOnline();
  const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache2, key);
  const stateDependencies = (0, import_react3.useRef)({}).current;
  const fallback = isUndefined(fallbackData) ? isUndefined(config.fallback) ? UNDEFINED : config.fallback[key] : fallbackData;
  const isEqual = (prev, current) => {
    for (const _ in stateDependencies) {
      const t = _;
      if (t === "data") {
        if (!compare2(prev[t], current[t])) {
          if (!isUndefined(prev[t])) {
            return false;
          }
          if (!compare2(returnedData, current[t])) {
            return false;
          }
        }
      } else {
        if (current[t] !== prev[t]) {
          return false;
        }
      }
    }
    return true;
  };
  const getSnapshot = (0, import_react3.useMemo)(() => {
    const shouldStartRequest = (() => {
      if (!key)
        return false;
      if (!fetcher)
        return false;
      if (!isUndefined(revalidateOnMount))
        return revalidateOnMount;
      if (getConfig().isPaused())
        return false;
      if (suspense)
        return false;
      return revalidateIfStale !== false;
    })();
    const getSelectedCache = (state) => {
      const snapshot = mergeObjects(state);
      delete snapshot._k;
      if (!shouldStartRequest) {
        return snapshot;
      }
      return {
        isValidating: true,
        isLoading: true,
        ...snapshot
      };
    };
    const cachedData2 = getCache();
    const initialData = getInitialCache();
    const clientSnapshot = getSelectedCache(cachedData2);
    const serverSnapshot = cachedData2 === initialData ? clientSnapshot : getSelectedCache(initialData);
    let memorizedSnapshot = clientSnapshot;
    return [
      () => {
        const newSnapshot = getSelectedCache(getCache());
        const compareResult = isEqual(newSnapshot, memorizedSnapshot);
        if (compareResult) {
          memorizedSnapshot.data = newSnapshot.data;
          memorizedSnapshot.isLoading = newSnapshot.isLoading;
          memorizedSnapshot.isValidating = newSnapshot.isValidating;
          memorizedSnapshot.error = newSnapshot.error;
          return memorizedSnapshot;
        } else {
          memorizedSnapshot = newSnapshot;
          return newSnapshot;
        }
      },
      () => serverSnapshot
    ];
  }, [
    cache2,
    key
  ]);
  const cached = (0, import_shim.useSyncExternalStore)((0, import_react3.useCallback)(
    (callback) => subscribeCache(key, (current, prev) => {
      if (!isEqual(prev, current))
        callback();
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      cache2,
      key
    ]
  ), getSnapshot[0], getSnapshot[1]);
  const isInitialMount = !initialMountedRef.current;
  const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;
  const cachedData = cached.data;
  const data = isUndefined(cachedData) ? fallback && isPromiseLike(fallback) ? use2(fallback) : fallback : cachedData;
  const error2 = cached.error;
  const laggyDataRef = (0, import_react3.useRef)(data);
  const returnedData = keepPreviousData ? isUndefined(cachedData) ? isUndefined(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;
  const shouldDoInitialRevalidation = (() => {
    if (hasRevalidator && !isUndefined(error2))
      return false;
    if (isInitialMount && !isUndefined(revalidateOnMount))
      return revalidateOnMount;
    if (getConfig().isPaused())
      return false;
    if (suspense)
      return isUndefined(data) ? false : revalidateIfStale;
    return isUndefined(data) || revalidateIfStale;
  })();
  const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);
  const isValidating = isUndefined(cached.isValidating) ? defaultValidatingState : cached.isValidating;
  const isLoading = isUndefined(cached.isLoading) ? defaultValidatingState : cached.isLoading;
  const revalidate = (0, import_react3.useCallback)(
    async (revalidateOpts) => {
      const currentFetcher = fetcherRef.current;
      if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {
        return false;
      }
      let newData;
      let startAt;
      let loading = true;
      const opts = revalidateOpts || {};
      const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;
      const callbackSafeguard = () => {
        if (IS_REACT_LEGACY) {
          return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;
        }
        return key === keyRef.current;
      };
      const finalState = {
        isValidating: false,
        isLoading: false
      };
      const finishRequestAndUpdateState = () => {
        setCache(finalState);
      };
      const cleanupState = () => {
        const requestInfo = FETCH[key];
        if (requestInfo && requestInfo[1] === startAt) {
          delete FETCH[key];
        }
      };
      const initialState = {
        isValidating: true
      };
      if (isUndefined(getCache().data)) {
        initialState.isLoading = true;
      }
      try {
        if (shouldStartNewRequest) {
          setCache(initialState);
          if (config.loadingTimeout && isUndefined(getCache().data)) {
            setTimeout(() => {
              if (loading && callbackSafeguard()) {
                getConfig().onLoadingSlow(key, config);
              }
            }, config.loadingTimeout);
          }
          FETCH[key] = [
            currentFetcher(fnArg),
            getTimestamp()
          ];
        }
        ;
        [newData, startAt] = FETCH[key];
        newData = await newData;
        if (shouldStartNewRequest) {
          setTimeout(cleanupState, config.dedupingInterval);
        }
        if (!FETCH[key] || FETCH[key][1] !== startAt) {
          if (shouldStartNewRequest) {
            if (callbackSafeguard()) {
              getConfig().onDiscarded(key);
            }
          }
          return false;
        }
        finalState.error = UNDEFINED;
        const mutationInfo = MUTATION[key];
        if (!isUndefined(mutationInfo) && // case 1
        (startAt <= mutationInfo[0] || // case 2
        startAt <= mutationInfo[1] || // case 3
        mutationInfo[1] === 0)) {
          finishRequestAndUpdateState();
          if (shouldStartNewRequest) {
            if (callbackSafeguard()) {
              getConfig().onDiscarded(key);
            }
          }
          return false;
        }
        const cacheData = getCache().data;
        finalState.data = compare2(cacheData, newData) ? cacheData : newData;
        if (shouldStartNewRequest) {
          if (callbackSafeguard()) {
            getConfig().onSuccess(newData, key, config);
          }
        }
      } catch (err) {
        cleanupState();
        const currentConfig = getConfig();
        const { shouldRetryOnError } = currentConfig;
        if (!currentConfig.isPaused()) {
          finalState.error = err;
          if (shouldStartNewRequest && callbackSafeguard()) {
            currentConfig.onError(err, key, currentConfig);
            if (shouldRetryOnError === true || isFunction(shouldRetryOnError) && shouldRetryOnError(err)) {
              if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {
                currentConfig.onErrorRetry(err, key, currentConfig, (_opts) => {
                  const revalidators = EVENT_REVALIDATORS[key];
                  if (revalidators && revalidators[0]) {
                    revalidators[0](events_exports.ERROR_REVALIDATE_EVENT, _opts);
                  }
                }, {
                  retryCount: (opts.retryCount || 0) + 1,
                  dedupe: true
                });
              }
            }
          }
        }
      }
      loading = false;
      finishRequestAndUpdateState();
      return true;
    },
    // `setState` is immutable, and `eventsCallback`, `fnArg`, and
    // `keyValidating` are depending on `key`, so we can exclude them from
    // the deps array.
    //
    // FIXME:
    // `fn` and `config` might be changed during the lifecycle,
    // but they might be changed every render like this.
    // `useSWR('key', () => fetch('/api/'), { suspense: true })`
    // So we omit the values from the deps array
    // even though it might cause unexpected behaviors.
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      key,
      cache2
    ]
  );
  const boundMutate = (0, import_react3.useCallback)(
    // Use callback to make sure `keyRef.current` returns latest result every time
    (...args) => {
      return internalMutate(cache2, keyRef.current, ...args);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  useIsomorphicLayoutEffect(() => {
    fetcherRef.current = fetcher;
    configRef.current = config;
    if (!isUndefined(cachedData)) {
      laggyDataRef.current = cachedData;
    }
  });
  useIsomorphicLayoutEffect(() => {
    if (!key)
      return;
    const softRevalidate = revalidate.bind(UNDEFINED, WITH_DEDUPE);
    let nextFocusRevalidatedAt = 0;
    if (getConfig().revalidateOnFocus) {
      const initNow = Date.now();
      nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;
    }
    const onRevalidate = (type, opts = {}) => {
      if (type == events_exports.FOCUS_EVENT) {
        const now = Date.now();
        if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {
          nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;
          softRevalidate();
        }
      } else if (type == events_exports.RECONNECT_EVENT) {
        if (getConfig().revalidateOnReconnect && isActive()) {
          softRevalidate();
        }
      } else if (type == events_exports.MUTATE_EVENT) {
        return revalidate();
      } else if (type == events_exports.ERROR_REVALIDATE_EVENT) {
        return revalidate(opts);
      }
      return;
    };
    const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);
    unmountedRef.current = false;
    keyRef.current = key;
    initialMountedRef.current = true;
    setCache({
      _k: fnArg
    });
    if (shouldDoInitialRevalidation) {
      if (isUndefined(data) || IS_SERVER) {
        softRevalidate();
      } else {
        rAF(softRevalidate);
      }
    }
    return () => {
      unmountedRef.current = true;
      unsubEvents();
    };
  }, [
    key
  ]);
  useIsomorphicLayoutEffect(() => {
    let timer;
    function next() {
      const interval = isFunction(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;
      if (interval && timer !== -1) {
        timer = setTimeout(execute, interval);
      }
    }
    function execute() {
      if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {
        revalidate(WITH_DEDUPE).then(next);
      } else {
        next();
      }
    }
    next();
    return () => {
      if (timer) {
        clearTimeout(timer);
        timer = -1;
      }
    };
  }, [
    refreshInterval,
    refreshWhenHidden,
    refreshWhenOffline,
    key
  ]);
  (0, import_react3.useDebugValue)(returnedData);
  if (suspense && isUndefined(data) && key) {
    if (!IS_REACT_LEGACY && IS_SERVER) {
      throw new Error("Fallback data is required when using Suspense in SSR.");
    }
    fetcherRef.current = fetcher;
    configRef.current = config;
    unmountedRef.current = false;
    const req = PRELOAD[key];
    if (!isUndefined(req)) {
      const promise = boundMutate(req);
      use2(promise);
    }
    if (isUndefined(error2)) {
      const promise = revalidate(WITH_DEDUPE);
      if (!isUndefined(returnedData)) {
        promise.status = "fulfilled";
        promise.value = true;
      }
      use2(promise);
    } else {
      throw error2;
    }
  }
  const swrResponse = {
    mutate: boundMutate,
    get data() {
      stateDependencies.data = true;
      return returnedData;
    },
    get error() {
      stateDependencies.error = true;
      return error2;
    },
    get isValidating() {
      stateDependencies.isValidating = true;
      return isValidating;
    },
    get isLoading() {
      stateDependencies.isLoading = true;
      return isLoading;
    }
  };
  return swrResponse;
};
var SWRConfig2 = OBJECT.defineProperty(SWRConfig, "defaultValue", {
  value: defaultConfig
});
var useSWR = withArgs(useSWRHandler);

// node_modules/@ant-design/pro-provider/es/index.js
var import_dayjs = __toESM(require_dayjs_min());

// node_modules/@ant-design/pro-provider/es/typing/layoutToken.js
var getLayoutDesignToken = function getLayoutDesignToken2(designTokens, antdToken) {
  var _finalDesignTokens$si, _finalDesignTokens$he, _finalDesignTokens$he2, _finalDesignTokens$pa, _finalDesignTokens$pa2;
  var finalDesignTokens = _objectSpread2({}, designTokens);
  return _objectSpread2(_objectSpread2({
    bgLayout: "linear-gradient(".concat(antdToken.colorBgContainer, ", ").concat(antdToken.colorBgLayout, " 28%)"),
    colorTextAppListIcon: antdToken.colorTextSecondary,
    appListIconHoverBgColor: finalDesignTokens === null || finalDesignTokens === void 0 || (_finalDesignTokens$si = finalDesignTokens.sider) === null || _finalDesignTokens$si === void 0 ? void 0 : _finalDesignTokens$si.colorBgMenuItemSelected,
    colorBgAppListIconHover: setAlpha(antdToken.colorTextBase, 0.04),
    colorTextAppListIconHover: antdToken.colorTextBase
  }, finalDesignTokens), {}, {
    header: _objectSpread2({
      colorBgHeader: setAlpha(antdToken.colorBgElevated, 0.6),
      colorBgScrollHeader: setAlpha(antdToken.colorBgElevated, 0.8),
      colorHeaderTitle: antdToken.colorText,
      colorBgMenuItemHover: setAlpha(antdToken.colorTextBase, 0.03),
      colorBgMenuItemSelected: "transparent",
      colorBgMenuElevated: (finalDesignTokens === null || finalDesignTokens === void 0 || (_finalDesignTokens$he = finalDesignTokens.header) === null || _finalDesignTokens$he === void 0 ? void 0 : _finalDesignTokens$he.colorBgHeader) !== "rgba(255, 255, 255, 0.6)" ? (_finalDesignTokens$he2 = finalDesignTokens.header) === null || _finalDesignTokens$he2 === void 0 ? void 0 : _finalDesignTokens$he2.colorBgHeader : antdToken.colorBgElevated,
      colorTextMenuSelected: setAlpha(antdToken.colorTextBase, 0.95),
      colorBgRightActionsItemHover: setAlpha(antdToken.colorTextBase, 0.03),
      colorTextRightActionsItem: antdToken.colorTextTertiary,
      heightLayoutHeader: 56,
      colorTextMenu: antdToken.colorTextSecondary,
      colorTextMenuSecondary: antdToken.colorTextTertiary,
      colorTextMenuTitle: antdToken.colorText,
      colorTextMenuActive: antdToken.colorText
    }, finalDesignTokens.header),
    sider: _objectSpread2({
      paddingInlineLayoutMenu: 8,
      paddingBlockLayoutMenu: 0,
      colorBgCollapsedButton: antdToken.colorBgElevated,
      colorTextCollapsedButtonHover: antdToken.colorTextSecondary,
      colorTextCollapsedButton: setAlpha(antdToken.colorTextBase, 0.25),
      colorMenuBackground: "transparent",
      colorMenuItemDivider: setAlpha(antdToken.colorTextBase, 0.06),
      colorBgMenuItemHover: setAlpha(antdToken.colorTextBase, 0.03),
      colorBgMenuItemSelected: setAlpha(antdToken.colorTextBase, 0.04),
      colorTextMenuItemHover: antdToken.colorText,
      colorTextMenuSelected: setAlpha(antdToken.colorTextBase, 0.95),
      colorTextMenuActive: antdToken.colorText,
      colorTextMenu: antdToken.colorTextSecondary,
      colorTextMenuSecondary: antdToken.colorTextTertiary,
      colorTextMenuTitle: antdToken.colorText,
      colorTextSubMenuSelected: setAlpha(antdToken.colorTextBase, 0.95)
    }, finalDesignTokens.sider),
    pageContainer: _objectSpread2({
      colorBgPageContainer: "transparent",
      paddingInlinePageContainerContent: ((_finalDesignTokens$pa = finalDesignTokens.pageContainer) === null || _finalDesignTokens$pa === void 0 ? void 0 : _finalDesignTokens$pa.marginInlinePageContainerContent) || 40,
      paddingBlockPageContainerContent: ((_finalDesignTokens$pa2 = finalDesignTokens.pageContainer) === null || _finalDesignTokens$pa2 === void 0 ? void 0 : _finalDesignTokens$pa2.marginBlockPageContainerContent) || 32,
      colorBgPageContainerFixed: antdToken.colorBgElevated
    }, finalDesignTokens.pageContainer)
  });
};

// node_modules/@ant-design/pro-provider/es/useStyle/token.js
var token_exports = {};
__export(token_exports, {
  defaultToken: () => defaultToken,
  emptyTheme: () => emptyTheme,
  hashCode: () => hashCode,
  token: () => token,
  useToken: () => useToken
});
var _theme$defaultAlgorit;
var defaultToken = {
  blue: "#1677ff",
  purple: "#722ED1",
  cyan: "#13C2C2",
  green: "#52C41A",
  magenta: "#EB2F96",
  pink: "#eb2f96",
  red: "#F5222D",
  orange: "#FA8C16",
  yellow: "#FADB14",
  volcano: "#FA541C",
  geekblue: "#2F54EB",
  gold: "#FAAD14",
  lime: "#A0D911",
  colorPrimary: "#1677ff",
  colorSuccess: "#52c41a",
  colorWarning: "#faad14",
  colorError: "#ff7875",
  colorInfo: "#1677ff",
  colorTextBase: "#000",
  colorBgBase: "#fff",
  fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",
  fontSize: 14,
  lineWidth: 1,
  lineType: "solid",
  motionUnit: 0.1,
  motionBase: 0,
  motionEaseOutCirc: "cubic-bezier(0.08, 0.82, 0.17, 1)",
  motionEaseInOutCirc: "cubic-bezier(0.78, 0.14, 0.15, 0.86)",
  motionEaseOut: "cubic-bezier(0.215, 0.61, 0.355, 1)",
  motionEaseInOut: "cubic-bezier(0.645, 0.045, 0.355, 1)",
  motionEaseOutBack: "cubic-bezier(0.12, 0.4, 0.29, 1.46)",
  motionEaseInQuint: "cubic-bezier(0.645, 0.045, 0.355, 1)",
  motionEaseOutQuint: "cubic-bezier(0.23, 1, 0.32, 1)",
  borderRadius: 4,
  sizeUnit: 4,
  sizeStep: 4,
  sizePopupArrow: 16,
  controlHeight: 32,
  zIndexBase: 0,
  zIndexPopupBase: 1e3,
  opacityImage: 1,
  wireframe: false,
  "blue-1": "#e6f4ff",
  "blue-2": "#bae0ff",
  "blue-3": "#91caff",
  "blue-4": "#69b1ff",
  "blue-5": "#4096ff",
  "blue-6": "#1677ff",
  "blue-7": "#0958d9",
  "blue-8": "#003eb3",
  "blue-9": "#002c8c",
  "blue-10": "#001d66",
  "purple-1": "#f9f0ff",
  "purple-2": "#efdbff",
  "purple-3": "#d3adf7",
  "purple-4": "#b37feb",
  "purple-5": "#9254de",
  "purple-6": "#722ed1",
  "purple-7": "#531dab",
  "purple-8": "#391085",
  "purple-9": "#22075e",
  "purple-10": "#120338",
  "cyan-1": "#e6fffb",
  "cyan-2": "#b5f5ec",
  "cyan-3": "#87e8de",
  "cyan-4": "#5cdbd3",
  "cyan-5": "#36cfc9",
  "cyan-6": "#13c2c2",
  "cyan-7": "#08979c",
  "cyan-8": "#006d75",
  "cyan-9": "#00474f",
  "cyan-10": "#002329",
  "green-1": "#f6ffed",
  "green-2": "#d9f7be",
  "green-3": "#b7eb8f",
  "green-4": "#95de64",
  "green-5": "#73d13d",
  "green-6": "#52c41a",
  "green-7": "#389e0d",
  "green-8": "#237804",
  "green-9": "#135200",
  "green-10": "#092b00",
  "magenta-1": "#fff0f6",
  "magenta-2": "#ffd6e7",
  "magenta-3": "#ffadd2",
  "magenta-4": "#ff85c0",
  "magenta-5": "#f759ab",
  "magenta-6": "#eb2f96",
  "magenta-7": "#c41d7f",
  "magenta-8": "#9e1068",
  "magenta-9": "#780650",
  "magenta-10": "#520339",
  "pink-1": "#fff0f6",
  "pink-2": "#ffd6e7",
  "pink-3": "#ffadd2",
  "pink-4": "#ff85c0",
  "pink-5": "#f759ab",
  "pink-6": "#eb2f96",
  "pink-7": "#c41d7f",
  "pink-8": "#9e1068",
  "pink-9": "#780650",
  "pink-10": "#520339",
  "red-1": "#fff1f0",
  "red-2": "#ffccc7",
  "red-3": "#ffa39e",
  "red-4": "#ff7875",
  "red-5": "#ff4d4f",
  "red-6": "#f5222d",
  "red-7": "#cf1322",
  "red-8": "#a8071a",
  "red-9": "#820014",
  "red-10": "#5c0011",
  "orange-1": "#fff7e6",
  "orange-2": "#ffe7ba",
  "orange-3": "#ffd591",
  "orange-4": "#ffc069",
  "orange-5": "#ffa940",
  "orange-6": "#fa8c16",
  "orange-7": "#d46b08",
  "orange-8": "#ad4e00",
  "orange-9": "#873800",
  "orange-10": "#612500",
  "yellow-1": "#feffe6",
  "yellow-2": "#ffffb8",
  "yellow-3": "#fffb8f",
  "yellow-4": "#fff566",
  "yellow-5": "#ffec3d",
  "yellow-6": "#fadb14",
  "yellow-7": "#d4b106",
  "yellow-8": "#ad8b00",
  "yellow-9": "#876800",
  "yellow-10": "#614700",
  "volcano-1": "#fff2e8",
  "volcano-2": "#ffd8bf",
  "volcano-3": "#ffbb96",
  "volcano-4": "#ff9c6e",
  "volcano-5": "#ff7a45",
  "volcano-6": "#fa541c",
  "volcano-7": "#d4380d",
  "volcano-8": "#ad2102",
  "volcano-9": "#871400",
  "volcano-10": "#610b00",
  "geekblue-1": "#f0f5ff",
  "geekblue-2": "#d6e4ff",
  "geekblue-3": "#adc6ff",
  "geekblue-4": "#85a5ff",
  "geekblue-5": "#597ef7",
  "geekblue-6": "#2f54eb",
  "geekblue-7": "#1d39c4",
  "geekblue-8": "#10239e",
  "geekblue-9": "#061178",
  "geekblue-10": "#030852",
  "gold-1": "#fffbe6",
  "gold-2": "#fff1b8",
  "gold-3": "#ffe58f",
  "gold-4": "#ffd666",
  "gold-5": "#ffc53d",
  "gold-6": "#faad14",
  "gold-7": "#d48806",
  "gold-8": "#ad6800",
  "gold-9": "#874d00",
  "gold-10": "#613400",
  "lime-1": "#fcffe6",
  "lime-2": "#f4ffb8",
  "lime-3": "#eaff8f",
  "lime-4": "#d3f261",
  "lime-5": "#bae637",
  "lime-6": "#a0d911",
  "lime-7": "#7cb305",
  "lime-8": "#5b8c00",
  "lime-9": "#3f6600",
  "lime-10": "#254000",
  colorText: "rgba(0, 0, 0, 0.88)",
  colorTextSecondary: "rgba(0, 0, 0, 0.65)",
  colorTextTertiary: "rgba(0, 0, 0, 0.45)",
  colorTextQuaternary: "rgba(0, 0, 0, 0.25)",
  colorFill: "rgba(0, 0, 0, 0.15)",
  colorFillSecondary: "rgba(0, 0, 0, 0.06)",
  colorFillTertiary: "rgba(0, 0, 0, 0.04)",
  colorFillQuaternary: "rgba(0, 0, 0, 0.02)",
  colorBgLayout: "hsl(220,23%,97%)",
  colorBgContainer: "#ffffff",
  colorBgElevated: "#ffffff",
  colorBgSpotlight: "rgba(0, 0, 0, 0.85)",
  colorBorder: "#d9d9d9",
  colorBorderSecondary: "#f0f0f0",
  colorPrimaryBg: "#e6f4ff",
  colorPrimaryBgHover: "#bae0ff",
  colorPrimaryBorder: "#91caff",
  colorPrimaryBorderHover: "#69b1ff",
  colorPrimaryHover: "#4096ff",
  colorPrimaryActive: "#0958d9",
  colorPrimaryTextHover: "#4096ff",
  colorPrimaryText: "#1677ff",
  colorPrimaryTextActive: "#0958d9",
  colorSuccessBg: "#f6ffed",
  colorSuccessBgHover: "#d9f7be",
  colorSuccessBorder: "#b7eb8f",
  colorSuccessBorderHover: "#95de64",
  colorSuccessHover: "#95de64",
  colorSuccessActive: "#389e0d",
  colorSuccessTextHover: "#73d13d",
  colorSuccessText: "#52c41a",
  colorSuccessTextActive: "#389e0d",
  colorErrorBg: "#fff2f0",
  colorErrorBgHover: "#fff1f0",
  colorErrorBorder: "#ffccc7",
  colorErrorBorderHover: "#ffa39e",
  colorErrorHover: "#ffa39e",
  colorErrorActive: "#d9363e",
  colorErrorTextHover: "#ff7875",
  colorErrorText: "#ff4d4f",
  colorErrorTextActive: "#d9363e",
  colorWarningBg: "#fffbe6",
  colorWarningBgHover: "#fff1b8",
  colorWarningBorder: "#ffe58f",
  colorWarningBorderHover: "#ffd666",
  colorWarningHover: "#ffd666",
  colorWarningActive: "#d48806",
  colorWarningTextHover: "#ffc53d",
  colorWarningText: "#faad14",
  colorWarningTextActive: "#d48806",
  colorInfoBg: "#e6f4ff",
  colorInfoBgHover: "#bae0ff",
  colorInfoBorder: "#91caff",
  colorInfoBorderHover: "#69b1ff",
  colorInfoHover: "#69b1ff",
  colorInfoActive: "#0958d9",
  colorInfoTextHover: "#4096ff",
  colorInfoText: "#1677ff",
  colorInfoTextActive: "#0958d9",
  colorBgMask: "rgba(0, 0, 0, 0.45)",
  colorWhite: "#fff",
  sizeXXL: 48,
  sizeXL: 32,
  sizeLG: 24,
  sizeMD: 20,
  sizeMS: 16,
  size: 16,
  sizeSM: 12,
  sizeXS: 8,
  sizeXXS: 4,
  controlHeightSM: 24,
  controlHeightXS: 16,
  controlHeightLG: 40,
  motionDurationFast: "0.1s",
  motionDurationMid: "0.2s",
  motionDurationSlow: "0.3s",
  fontSizes: [12, 14, 16, 20, 24, 30, 38, 46, 56, 68],
  lineHeights: [1.6666666666666667, 1.5714285714285714, 1.5, 1.4, 1.3333333333333333, 1.2666666666666666, 1.2105263157894737, 1.173913043478261, 1.1428571428571428, 1.1176470588235294],
  lineWidthBold: 2,
  borderRadiusXS: 1,
  borderRadiusSM: 4,
  borderRadiusLG: 8,
  borderRadiusOuter: 4,
  colorLink: "#1677ff",
  colorLinkHover: "#69b1ff",
  colorLinkActive: "#0958d9",
  colorFillContent: "rgba(0, 0, 0, 0.06)",
  colorFillContentHover: "rgba(0, 0, 0, 0.15)",
  colorFillAlter: "rgba(0, 0, 0, 0.02)",
  colorBgContainerDisabled: "rgba(0, 0, 0, 0.04)",
  colorBorderBg: "#ffffff",
  colorSplit: "rgba(5, 5, 5, 0.06)",
  colorTextPlaceholder: "rgba(0, 0, 0, 0.25)",
  colorTextDisabled: "rgba(0, 0, 0, 0.25)",
  colorTextHeading: "rgba(0, 0, 0, 0.88)",
  colorTextLabel: "rgba(0, 0, 0, 0.65)",
  colorTextDescription: "rgba(0, 0, 0, 0.45)",
  colorTextLightSolid: "#fff",
  colorHighlight: "#ff7875",
  colorBgTextHover: "rgba(0, 0, 0, 0.06)",
  colorBgTextActive: "rgba(0, 0, 0, 0.15)",
  colorIcon: "rgba(0, 0, 0, 0.45)",
  colorIconHover: "rgba(0, 0, 0, 0.88)",
  colorErrorOutline: "rgba(255, 38, 5, 0.06)",
  colorWarningOutline: "rgba(255, 215, 5, 0.1)",
  fontSizeSM: 12,
  fontSizeLG: 16,
  fontSizeXL: 20,
  fontSizeHeading1: 38,
  fontSizeHeading2: 30,
  fontSizeHeading3: 24,
  fontSizeHeading4: 20,
  fontSizeHeading5: 16,
  fontSizeIcon: 12,
  lineHeight: 1.5714285714285714,
  lineHeightLG: 1.5,
  lineHeightSM: 1.6666666666666667,
  lineHeightHeading1: 1.2105263157894737,
  lineHeightHeading2: 1.2666666666666666,
  lineHeightHeading3: 1.3333333333333333,
  lineHeightHeading4: 1.4,
  lineHeightHeading5: 1.5,
  controlOutlineWidth: 2,
  controlInteractiveSize: 16,
  controlItemBgHover: "rgba(0, 0, 0, 0.04)",
  controlItemBgActive: "#e6f4ff",
  controlItemBgActiveHover: "#bae0ff",
  controlItemBgActiveDisabled: "rgba(0, 0, 0, 0.15)",
  controlTmpOutline: "rgba(0, 0, 0, 0.02)",
  controlOutline: "rgba(5, 145, 255, 0.1)",
  fontWeightStrong: 600,
  opacityLoading: 0.65,
  linkDecoration: "none",
  linkHoverDecoration: "none",
  linkFocusDecoration: "none",
  controlPaddingHorizontal: 12,
  controlPaddingHorizontalSM: 8,
  paddingXXS: 4,
  paddingXS: 8,
  paddingSM: 12,
  padding: 16,
  paddingMD: 20,
  paddingLG: 24,
  paddingXL: 32,
  paddingContentHorizontalLG: 24,
  paddingContentVerticalLG: 16,
  paddingContentHorizontal: 16,
  paddingContentVertical: 12,
  paddingContentHorizontalSM: 16,
  paddingContentVerticalSM: 8,
  marginXXS: 4,
  marginXS: 8,
  marginSM: 12,
  margin: 16,
  marginMD: 20,
  marginLG: 24,
  marginXL: 32,
  marginXXL: 48,
  boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)",
  boxShadowSecondary: "0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",
  screenXS: 480,
  screenXSMin: 480,
  screenXSMax: 479,
  screenSM: 576,
  screenSMMin: 576,
  screenSMMax: 575,
  screenMD: 768,
  screenMDMin: 768,
  screenMDMax: 767,
  screenLG: 992,
  screenLGMin: 992,
  screenLGMax: 991,
  screenXL: 1200,
  screenXLMin: 1200,
  screenXLMax: 1199,
  screenXXL: 1600,
  screenXXLMin: 1600,
  screenXXLMax: 1599,
  boxShadowPopoverArrow: "3px 3px 7px rgba(0, 0, 0, 0.1)",
  boxShadowCard: "0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",
  boxShadowDrawerRight: "-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)",
  boxShadowDrawerLeft: "6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)",
  boxShadowDrawerUp: "0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",
  boxShadowDrawerDown: "0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)",
  boxShadowTabsOverflowLeft: "inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",
  boxShadowTabsOverflowRight: "inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",
  boxShadowTabsOverflowTop: "inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",
  boxShadowTabsOverflowBottom: "inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",
  _tokenKey: "19w80ff",
  _hashId: "css-dev-only-do-not-override-i2zu9q"
};
var hashCode = function hashCode2(str) {
  var seed = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;
  var h1 = 3735928559 ^ seed, h2 = 1103547991 ^ seed;
  for (var i = 0, ch; i < str.length; i++) {
    ch = str.charCodeAt(i);
    h1 = Math.imul(h1 ^ ch, 2654435761);
    h2 = Math.imul(h2 ^ ch, 1597334677);
  }
  h1 = Math.imul(h1 ^ h1 >>> 16, 2246822507) ^ Math.imul(h2 ^ h2 >>> 13, 3266489909);
  h2 = Math.imul(h2 ^ h2 >>> 16, 2246822507) ^ Math.imul(h1 ^ h1 >>> 13, 3266489909);
  return 4294967296 * (2097151 & h2) + (h1 >>> 0);
};
var emptyTheme = createTheme(function(token2) {
  return token2;
});
var token = {
  theme: emptyTheme,
  token: _objectSpread2(_objectSpread2({}, defaultToken), theme_default === null || theme_default === void 0 || (_theme$defaultAlgorit = theme_default.defaultAlgorithm) === null || _theme$defaultAlgorit === void 0 ? void 0 : _theme$defaultAlgorit.call(theme_default, theme_default === null || theme_default === void 0 ? void 0 : theme_default.defaultSeed)),
  hashId: "pro-".concat(hashCode(JSON.stringify(defaultToken)))
};
var useToken = function useToken2() {
  return token;
};

// node_modules/@ant-design/pro-provider/es/utils/merge.js
init_typeof();
var merge = function merge2() {
  var obj = {};
  for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
    rest[_key] = arguments[_key];
  }
  var il = rest.length;
  var key;
  var i = 0;
  for (; i < il; i += 1) {
    for (key in rest[i]) {
      if (rest[i].hasOwnProperty(key)) {
        if (_typeof(obj[key]) === "object" && _typeof(rest[i][key]) === "object" && obj[key] !== void 0 && obj[key] !== null && !Array.isArray(obj[key]) && !Array.isArray(rest[i][key])) {
          obj[key] = _objectSpread2(_objectSpread2({}, obj[key]), rest[i][key]);
        } else {
          obj[key] = rest[i][key];
        }
      }
    }
  }
  return obj;
};

// node_modules/@ant-design/pro-provider/es/index.js
var import_zh_cn = __toESM(require_zh_cn());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var _excluded = ["locale", "getPrefixCls"];
var _excluded2 = ["locale", "theme"];
var omitUndefined = function omitUndefined2(obj) {
  var newObj = {};
  Object.keys(obj || {}).forEach(function(key) {
    if (obj[key] !== void 0) {
      newObj[key] = obj[key];
    }
  });
  if (Object.keys(newObj).length < 1) {
    return void 0;
  }
  return newObj;
};
var isNeedOpenHash = function isNeedOpenHash2() {
  var _process$env$NODE_ENV, _process$env$NODE_ENV2;
  if (typeof process !== "undefined" && (((_process$env$NODE_ENV = "development") === null || _process$env$NODE_ENV === void 0 ? void 0 : _process$env$NODE_ENV.toUpperCase()) === "TEST" || ((_process$env$NODE_ENV2 = "development") === null || _process$env$NODE_ENV2 === void 0 ? void 0 : _process$env$NODE_ENV2.toUpperCase()) === "DEV")) {
    return false;
  }
  return true;
};
var ProConfigContext = import_react4.default.createContext({
  intl: _objectSpread2(_objectSpread2({}, zhCNIntl), {}, {
    locale: "default"
  }),
  valueTypeMap: {},
  theme: emptyTheme,
  hashed: true,
  dark: false,
  token: defaultToken
});
var ConfigConsumer = ProConfigContext.Consumer;
var CacheClean = function CacheClean2() {
  var _useSWRConfig = useSWRConfig(), cache2 = _useSWRConfig.cache;
  (0, import_react4.useEffect)(function() {
    return function() {
      cache2.clear();
    };
  }, []);
  return null;
};
var ConfigProviderContainer = function ConfigProviderContainer2(props) {
  var _proTheme$useToken;
  var children = props.children, dark = props.dark, valueTypeMap = props.valueTypeMap, _props$autoClearCache = props.autoClearCache, autoClearCache = _props$autoClearCache === void 0 ? false : _props$autoClearCache, propsToken = props.token, prefixCls = props.prefixCls, intl = props.intl;
  var _useContext = (0, import_react4.useContext)(config_provider_default.ConfigContext), locale4 = _useContext.locale, getPrefixCls = _useContext.getPrefixCls, restConfig = _objectWithoutProperties(_useContext, _excluded);
  var tokenContext = (_proTheme$useToken = proTheme.useToken) === null || _proTheme$useToken === void 0 ? void 0 : _proTheme$useToken.call(proTheme);
  var proProvide = (0, import_react4.useContext)(ProConfigContext);
  var proComponentsCls = prefixCls ? ".".concat(prefixCls) : ".".concat(getPrefixCls(), "-pro");
  var antCls = "." + getPrefixCls();
  var salt = "".concat(proComponentsCls);
  var proLayoutTokenMerge = (0, import_react4.useMemo)(function() {
    return getLayoutDesignToken(propsToken || {}, tokenContext.token || defaultToken);
  }, [propsToken, tokenContext.token]);
  var proProvideValue = (0, import_react4.useMemo)(function() {
    var _proProvide$intl;
    var localeName = locale4 === null || locale4 === void 0 ? void 0 : locale4.locale;
    var key = findIntlKeyByAntdLocaleKey(localeName);
    var resolvedIntl = intl !== null && intl !== void 0 ? intl : localeName && ((_proProvide$intl = proProvide.intl) === null || _proProvide$intl === void 0 ? void 0 : _proProvide$intl.locale) === "default" ? intlMap[key] : proProvide.intl || intlMap[key];
    return _objectSpread2(_objectSpread2({}, proProvide), {}, {
      dark: dark !== null && dark !== void 0 ? dark : proProvide.dark,
      token: merge(proProvide.token, tokenContext.token, {
        proComponentsCls,
        antCls,
        themeId: tokenContext.theme.id,
        layout: proLayoutTokenMerge
      }),
      intl: resolvedIntl || zhCNIntl
    });
  }, [locale4 === null || locale4 === void 0 ? void 0 : locale4.locale, proProvide, dark, tokenContext.token, tokenContext.theme.id, proComponentsCls, antCls, proLayoutTokenMerge, intl]);
  var finalToken = _objectSpread2(_objectSpread2({}, proProvideValue.token || {}), {}, {
    proComponentsCls
  });
  var _useCacheToken = useCacheToken(tokenContext.theme, [tokenContext.token, finalToken !== null && finalToken !== void 0 ? finalToken : {}], {
    salt,
    override: finalToken
  }), _useCacheToken2 = _slicedToArray(_useCacheToken, 2), token2 = _useCacheToken2[0], nativeHashId = _useCacheToken2[1];
  var hashed = (0, import_react4.useMemo)(function() {
    if (props.hashed === false) {
      return false;
    }
    if (proProvide.hashed === false)
      return false;
    return true;
  }, [proProvide.hashed, props.hashed]);
  var hashId = (0, import_react4.useMemo)(function() {
    if (props.hashed === false) {
      return "";
    }
    if (proProvide.hashed === false)
      return "";
    if (isNeedOpenHash() === false) {
      return "";
    } else if (tokenContext.hashId) {
      return tokenContext.hashId;
    } else {
      return nativeHashId;
    }
  }, [nativeHashId, proProvide.hashed, props.hashed]);
  (0, import_react4.useEffect)(function() {
    import_dayjs.default.locale((locale4 === null || locale4 === void 0 ? void 0 : locale4.locale) || "zh-cn");
  }, [locale4 === null || locale4 === void 0 ? void 0 : locale4.locale]);
  var themeConfig = (0, import_react4.useMemo)(function() {
    return _objectSpread2(_objectSpread2({}, restConfig.theme), {}, {
      hashId,
      hashed: hashed && isNeedOpenHash()
    });
  }, [restConfig.theme, hashId, hashed, isNeedOpenHash()]);
  var proConfigContextValue = (0, import_react4.useMemo)(function() {
    return _objectSpread2(_objectSpread2({}, proProvideValue), {}, {
      valueTypeMap: valueTypeMap || (proProvideValue === null || proProvideValue === void 0 ? void 0 : proProvideValue.valueTypeMap),
      token: token2,
      theme: tokenContext.theme,
      hashed,
      hashId
    });
  }, [proProvideValue, valueTypeMap, token2, tokenContext.theme, hashed, hashId]);
  var configProviderDom = (0, import_react4.useMemo)(function() {
    return (0, import_jsx_runtime.jsx)(config_provider_default, _objectSpread2(_objectSpread2({}, restConfig), {}, {
      theme: themeConfig,
      children: (0, import_jsx_runtime.jsx)(ProConfigContext.Provider, {
        value: proConfigContextValue,
        children: (0, import_jsx_runtime3.jsxs)(import_jsx_runtime2.Fragment, {
          children: [autoClearCache && (0, import_jsx_runtime.jsx)(CacheClean, {}), children]
        })
      })
    }));
  }, [restConfig, themeConfig, proConfigContextValue, autoClearCache, children]);
  if (!autoClearCache)
    return configProviderDom;
  return (0, import_jsx_runtime.jsx)(SWRConfig2, {
    value: {
      provider: function provider() {
        return /* @__PURE__ */ new Map();
      }
    },
    children: configProviderDom
  });
};
var ProConfigProvider = function ProConfigProvider2(props) {
  var needDeps = props.needDeps, dark = props.dark, token2 = props.token;
  var proProvide = (0, import_react4.useContext)(ProConfigContext);
  var _useContext2 = (0, import_react4.useContext)(config_provider_default.ConfigContext), locale4 = _useContext2.locale, theme = _useContext2.theme, rest = _objectWithoutProperties(_useContext2, _excluded2);
  var isNullProvide = needDeps && proProvide.hashId !== void 0 && Object.keys(props).sort().join("-") === "children-needDeps";
  if (isNullProvide)
    return (0, import_jsx_runtime.jsx)(import_jsx_runtime2.Fragment, {
      children: props.children
    });
  var mergeAlgorithm = function mergeAlgorithm2() {
    var isDark = dark !== null && dark !== void 0 ? dark : proProvide.dark;
    if (isDark && !Array.isArray(theme === null || theme === void 0 ? void 0 : theme.algorithm)) {
      return [theme === null || theme === void 0 ? void 0 : theme.algorithm, proTheme.darkAlgorithm].filter(Boolean);
    }
    if (isDark && Array.isArray(theme === null || theme === void 0 ? void 0 : theme.algorithm)) {
      return [].concat(_toConsumableArray((theme === null || theme === void 0 ? void 0 : theme.algorithm) || []), [proTheme.darkAlgorithm]).filter(Boolean);
    }
    return theme === null || theme === void 0 ? void 0 : theme.algorithm;
  };
  var configProvider = _objectSpread2(_objectSpread2({}, rest), {}, {
    locale: locale4 || zh_CN_default7,
    theme: omitUndefined(_objectSpread2(_objectSpread2({}, theme), {}, {
      algorithm: mergeAlgorithm()
    }))
  });
  return (0, import_jsx_runtime.jsx)(config_provider_default, _objectSpread2(_objectSpread2({}, configProvider), {}, {
    children: (0, import_jsx_runtime.jsx)(ConfigProviderContainer, _objectSpread2(_objectSpread2({}, props), {}, {
      token: token2
    }))
  }));
};
function useIntl() {
  var _useContext3 = (0, import_react4.useContext)(config_provider_default.ConfigContext), locale4 = _useContext3.locale;
  var _useContext4 = (0, import_react4.useContext)(ProConfigContext), intl = _useContext4.intl;
  if (intl && intl.locale !== "default") {
    return intl || zhCNIntl;
  }
  if (locale4 !== null && locale4 !== void 0 && locale4.locale) {
    return intlMap[findIntlKeyByAntdLocaleKey(locale4.locale)] || zhCNIntl;
  }
  return zhCNIntl;
}
ProConfigContext.displayName = "ProProvider";
var ProProvider = ProConfigContext;
var es_default = ProConfigContext;

// node_modules/@ant-design/pro-provider/es/useStyle/index.js
var setAlpha = function setAlpha2(baseColor, alpha) {
  return new TinyColor(baseColor).setAlpha(alpha).toRgbString();
};
var lighten = function lighten2(baseColor, brightness) {
  var instance = new TinyColor(baseColor);
  return instance.lighten(brightness).toHexString();
};
var genTheme = function genTheme2() {
  if (typeof theme_default === "undefined" || !theme_default)
    return token_exports;
  return theme_default;
};
var proTheme = genTheme();
var useToken3 = proTheme.useToken;
var resetComponent = function resetComponent2(token2) {
  return {
    boxSizing: "border-box",
    margin: 0,
    padding: 0,
    color: token2.colorText,
    fontSize: token2.fontSize,
    lineHeight: token2.lineHeight,
    listStyle: "none"
  };
};
var operationUnit = function operationUnit2(token2) {
  return {
    // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.
    // And Typography use this to generate link style which should not do this.
    color: token2.colorLink,
    outline: "none",
    cursor: "pointer",
    transition: "color ".concat(token2.motionDurationSlow),
    "&:focus, &:hover": {
      color: token2.colorLinkHover
    },
    "&:active": {
      color: token2.colorLinkActive
    }
  };
};
function useStyle(componentName, styleFn) {
  var _token$proComponentsC;
  var _useContext = (0, import_react5.useContext)(ProProvider), _useContext$token = _useContext.token, token2 = _useContext$token === void 0 ? {} : _useContext$token;
  var _useContext2 = (0, import_react5.useContext)(ProProvider), hashed = _useContext2.hashed;
  var _useToken = useToken3(), antdToken = _useToken.token, hashId = _useToken.hashId;
  var _useContext3 = (0, import_react5.useContext)(ProProvider), provideTheme = _useContext3.theme;
  var _useContext4 = (0, import_react5.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext4.getPrefixCls, csp = _useContext4.csp;
  if (!token2.layout) {
    token2 = _objectSpread2({}, antdToken);
  }
  token2.proComponentsCls = (_token$proComponentsC = token2.proComponentsCls) !== null && _token$proComponentsC !== void 0 ? _token$proComponentsC : ".".concat(getPrefixCls("pro"));
  token2.antCls = ".".concat(getPrefixCls());
  return {
    wrapSSR: useStyleRegister({
      theme: provideTheme,
      token: token2,
      path: [componentName],
      nonce: csp === null || csp === void 0 ? void 0 : csp.nonce,
      layer: {
        name: "antd-pro",
        dependencies: ["antd"]
      }
    }, function() {
      return styleFn(token2);
    }),
    hashId: hashed ? hashId : ""
  };
}

// node_modules/@ant-design/pro-utils/es/components/DropdownFooter/index.js
var import_classnames = __toESM(require_classnames());
var import_react6 = __toESM(require_react());

// node_modules/@ant-design/pro-utils/es/components/DropdownFooter/style.js
init_defineProperty();
var genProStyle = function genProStyle2(token2) {
  return _defineProperty({}, token2.componentCls, {
    display: "flex",
    justifyContent: "space-between",
    paddingBlock: 8,
    paddingInlineStart: 8,
    paddingInlineEnd: 8,
    borderBlockStart: "1px solid ".concat(token2.colorSplit)
  });
};
function useStyle2(prefixCls) {
  return useStyle("DropdownFooter", function(token2) {
    var proToken = _objectSpread2(_objectSpread2({}, token2), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}

// node_modules/@ant-design/pro-utils/es/components/DropdownFooter/index.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var DropdownFooter = function DropdownFooter2(props) {
  var intl = useIntl();
  var onClear = props.onClear, onConfirm = props.onConfirm, disabled = props.disabled, footerRender = props.footerRender;
  var _useContext = (0, import_react6.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls("pro-core-dropdown-footer");
  var _useStyle = useStyle2(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var defaultFooter = [(0, import_jsx_runtime4.jsx)(button_default, {
    style: {
      visibility: onClear ? "visible" : "hidden"
    },
    type: "link",
    size: "small",
    disabled,
    onClick: function onClick(e) {
      if (onClear) {
        onClear(e);
      }
      e.stopPropagation();
    },
    children: intl.getMessage("form.lightFilter.clear", "清除")
  }, "clear"), (0, import_jsx_runtime4.jsx)(button_default, {
    "data-type": "confirm",
    type: "primary",
    size: "small",
    onClick: onConfirm,
    disabled,
    children: intl.getMessage("form.lightFilter.confirm", "确认")
  }, "confirm")];
  if (footerRender === false || (footerRender === null || footerRender === void 0 ? void 0 : footerRender(onConfirm, onClear)) === false) {
    return null;
  }
  var renderDom = (footerRender === null || footerRender === void 0 ? void 0 : footerRender(onConfirm, onClear)) || defaultFooter;
  return wrapSSR((0, import_jsx_runtime4.jsx)("div", {
    className: (0, import_classnames.default)(prefixCls, hashId),
    onClick: function onClick(e) {
      return e.target.getAttribute("data-type") !== "confirm" && e.stopPropagation();
    },
    children: renderDom
  }));
};

// node_modules/@ant-design/pro-utils/es/components/ErrorBoundary/index.js
init_defineProperty();
var import_react7 = __toESM(require_react());
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var ErrorBoundary = function(_React$Component) {
  _inherits(ErrorBoundary2, _React$Component);
  var _super = _createSuper(ErrorBoundary2);
  function ErrorBoundary2() {
    var _this;
    _classCallCheck(this, ErrorBoundary2);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _super.call.apply(_super, [this].concat(args));
    _defineProperty(_assertThisInitialized(_this), "state", {
      hasError: false,
      errorInfo: ""
    });
    return _this;
  }
  _createClass(ErrorBoundary2, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error2, errorInfo) {
      console.log(error2, errorInfo);
    }
  }, {
    key: "render",
    value: function render() {
      if (this.state.hasError) {
        return (0, import_jsx_runtime5.jsx)(result_default, {
          status: "error",
          title: "Something went wrong.",
          extra: this.state.errorInfo
        });
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error2) {
      return {
        hasError: true,
        errorInfo: error2.message
      };
    }
  }]);
  return ErrorBoundary2;
}(import_react7.default.Component);

// node_modules/@ant-design/pro-utils/es/components/FieldLabel/index.js
init_defineProperty();
var import_classnames2 = __toESM(require_classnames());
var import_react8 = __toESM(require_react());

// node_modules/@ant-design/pro-utils/es/components/FieldLabel/style.js
init_defineProperty();
var genProStyle3 = function genProStyle4(token2) {
  return _defineProperty({}, token2.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    display: "inline-flex",
    gap: token2.marginXXS,
    alignItems: "center",
    height: "30px",
    paddingBlock: 0,
    paddingInline: 8,
    fontSize: token2.fontSize,
    lineHeight: "30px",
    borderRadius: "2px",
    cursor: "pointer",
    "&:hover": {
      backgroundColor: token2.colorBgTextHover
    },
    "&-active": _defineProperty({
      paddingBlock: 0,
      paddingInline: 8,
      backgroundColor: token2.colorBgTextHover
    }, "&".concat(token2.componentCls, "-allow-clear:hover:not(").concat(token2.componentCls, "-disabled)"), _defineProperty(_defineProperty({}, "".concat(token2.componentCls, "-arrow"), {
      display: "none"
    }), "".concat(token2.componentCls, "-close"), {
      display: "inline-flex"
    }))
  }, "".concat(token2.antCls, "-select"), _defineProperty({}, "".concat(token2.antCls, "-select-clear"), {
    borderRadius: "50%"
  })), "".concat(token2.antCls, "-picker"), _defineProperty({}, "".concat(token2.antCls, "-picker-clear"), {
    borderRadius: "50%"
  })), "&-icon", _defineProperty(_defineProperty({
    color: token2.colorIcon,
    transition: "color 0.3s",
    fontSize: 12,
    verticalAlign: "middle"
  }, "&".concat(token2.componentCls, "-close"), {
    display: "none",
    fontSize: 12,
    alignItems: "center",
    justifyContent: "center",
    color: token2.colorTextPlaceholder,
    borderRadius: "50%"
  }), "&:hover", {
    color: token2.colorIconHover
  })), "&-disabled", _defineProperty({
    color: token2.colorTextPlaceholder,
    cursor: "not-allowed"
  }, "".concat(token2.componentCls, "-icon"), {
    color: token2.colorTextPlaceholder
  })), "&-small", _defineProperty(_defineProperty(_defineProperty({
    height: "24px",
    paddingBlock: 0,
    paddingInline: 4,
    fontSize: token2.fontSizeSM,
    lineHeight: "24px"
  }, "&".concat(token2.componentCls, "-active"), {
    paddingBlock: 0,
    paddingInline: 8
  }), "".concat(token2.componentCls, "-icon"), {
    paddingBlock: 0,
    paddingInline: 0
  }), "".concat(token2.componentCls, "-close"), {
    marginBlockStart: "-2px",
    paddingBlock: 4,
    paddingInline: 4,
    fontSize: "6px"
  })), "&-bordered", {
    height: "32px",
    paddingBlock: 0,
    paddingInline: 8,
    border: "".concat(token2.lineWidth, "px solid ").concat(token2.colorBorder),
    borderRadius: "@border-radius-base"
  }), "&-bordered&-small", {
    height: "24px",
    paddingBlock: 0,
    paddingInline: 8
  }), "&-bordered&-active", {
    backgroundColor: token2.colorBgContainer
  }));
};
function useStyle3(prefixCls) {
  return useStyle("FieldLabel", function(token2) {
    var proToken = _objectSpread2(_objectSpread2({}, token2), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle3(proToken)];
  });
}

// node_modules/@ant-design/pro-utils/es/components/FieldLabel/index.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var FieldLabelFunction = function FieldLabelFunction2(props, ref) {
  var _ConfigProvider$useCo, _ref2, _props$size;
  var label = props.label, onClear = props.onClear, value = props.value, disabled = props.disabled, onLabelClick = props.onLabelClick, ellipsis = props.ellipsis, placeholder = props.placeholder, className = props.className, formatter = props.formatter, bordered = props.bordered, style = props.style, downIcon = props.downIcon, _props$allowClear = props.allowClear, allowClear = _props$allowClear === void 0 ? true : _props$allowClear, _props$valueMaxLength = props.valueMaxLength, valueMaxLength = _props$valueMaxLength === void 0 ? 41 : _props$valueMaxLength;
  var _ref = (config_provider_default === null || config_provider_default === void 0 || (_ConfigProvider$useCo = config_provider_default.useConfig) === null || _ConfigProvider$useCo === void 0 ? void 0 : _ConfigProvider$useCo.call(config_provider_default)) || {
    componentSize: "middle"
  }, componentSize = _ref.componentSize;
  var size = componentSize;
  var _useContext = (0, import_react8.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls("pro-core-field-label");
  var _useStyle = useStyle3(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var intl = useIntl();
  var clearRef = (0, import_react8.useRef)(null);
  var labelRef = (0, import_react8.useRef)(null);
  (0, import_react8.useImperativeHandle)(ref, function() {
    return {
      labelRef,
      clearRef
    };
  });
  var wrapElements = function wrapElements2(array) {
    if (array.every(function(item) {
      return typeof item === "string";
    }))
      return array.join(",");
    return array.map(function(item, index2) {
      var comma = index2 === array.length - 1 ? "" : ",";
      if (typeof item === "string") {
        return (0, import_jsx_runtime6.jsxs)("span", {
          children: [item, comma]
        }, index2);
      }
      return (0, import_jsx_runtime6.jsxs)("span", {
        style: {
          display: "flex"
        },
        children: [item, comma]
      }, index2);
    });
  };
  var formatterText = function formatterText2(aValue) {
    if (formatter) {
      return formatter(aValue);
    }
    return Array.isArray(aValue) ? wrapElements(aValue) : aValue;
  };
  var getTextByValue = function getTextByValue2(aLabel, aValue) {
    if (aValue !== void 0 && aValue !== null && aValue !== "" && (!Array.isArray(aValue) || aValue.length)) {
      var _str$toString, _str$toString$slice;
      var prefix = aLabel ? (0, import_jsx_runtime6.jsxs)("span", {
        onClick: function onClick() {
          onLabelClick === null || onLabelClick === void 0 || onLabelClick();
        },
        className: "".concat(prefixCls, "-text"),
        children: [aLabel, ": "]
      }) : "";
      var str = formatterText(aValue);
      if (!ellipsis) {
        return (0, import_jsx_runtime6.jsxs)("span", {
          style: {
            display: "inline-flex",
            alignItems: "center"
          },
          children: [prefix, formatterText(aValue)]
        });
      }
      var getText = function getText2() {
        var isArrayValue = Array.isArray(aValue) && aValue.length > 1;
        var unitText = intl.getMessage("form.lightFilter.itemUnit", "项");
        if (typeof str === "string" && str.length > valueMaxLength && isArrayValue) {
          return "...".concat(aValue.length).concat(unitText);
        }
        return "";
      };
      var tail = getText();
      return (0, import_jsx_runtime6.jsxs)("span", {
        title: typeof str === "string" ? str : void 0,
        style: {
          display: "inline-flex",
          alignItems: "center"
        },
        children: [prefix, (0, import_jsx_runtime7.jsx)("span", {
          style: {
            paddingInlineStart: 4,
            display: "flex"
          },
          children: typeof str === "string" ? str === null || str === void 0 || (_str$toString = str.toString()) === null || _str$toString === void 0 || (_str$toString$slice = _str$toString.slice) === null || _str$toString$slice === void 0 ? void 0 : _str$toString$slice.call(_str$toString, 0, valueMaxLength) : str
        }), tail]
      });
    }
    return aLabel || placeholder;
  };
  return wrapSSR((0, import_jsx_runtime6.jsxs)("span", {
    className: (0, import_classnames2.default)(prefixCls, hashId, "".concat(prefixCls, "-").concat((_ref2 = (_props$size = props.size) !== null && _props$size !== void 0 ? _props$size : size) !== null && _ref2 !== void 0 ? _ref2 : "middle"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(prefixCls, "-active"), (Array.isArray(value) ? value.length > 0 : !!value) || value === 0), "".concat(prefixCls, "-disabled"), disabled), "".concat(prefixCls, "-bordered"), bordered), "".concat(prefixCls, "-allow-clear"), allowClear), className),
    style,
    ref: labelRef,
    onClick: function onClick() {
      var _props$onClick;
      props === null || props === void 0 || (_props$onClick = props.onClick) === null || _props$onClick === void 0 || _props$onClick.call(props);
    },
    children: [getTextByValue(label, value), (value || value === 0) && allowClear && (0, import_jsx_runtime7.jsx)(CloseCircleFilled_default, {
      role: "button",
      title: intl.getMessage("form.lightFilter.clear", "清除"),
      className: (0, import_classnames2.default)("".concat(prefixCls, "-icon"), hashId, "".concat(prefixCls, "-close")),
      onClick: function onClick(e) {
        if (!disabled)
          onClear === null || onClear === void 0 || onClear();
        e.stopPropagation();
      },
      ref: clearRef
    }), downIcon !== false ? downIcon !== null && downIcon !== void 0 ? downIcon : (0, import_jsx_runtime7.jsx)(DownOutlined_default, {
      className: (0, import_classnames2.default)("".concat(prefixCls, "-icon"), hashId, "".concat(prefixCls, "-arrow"))
    }) : null]
  }));
};
var FieldLabel = import_react8.default.forwardRef(FieldLabelFunction);

// node_modules/@ant-design/pro-utils/es/omitUndefined/index.js
var omitUndefined3 = function omitUndefined4(obj) {
  var newObj = {};
  Object.keys(obj || {}).forEach(function(key) {
    if (obj[key] !== void 0) {
      newObj[key] = obj[key];
    }
  });
  if (Object.keys(newObj).length < 1) {
    return void 0;
  }
  return newObj;
};

// node_modules/@ant-design/pro-utils/es/compareVersions/index.js
init_typeof();
var semver = /^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i;
var isWildcard = function isWildcard2(s) {
  return s === "*" || s === "x" || s === "X";
};
var tryParse = function tryParse2(v) {
  var n = parseInt(v, 10);
  return isNaN(n) ? v : n;
};
var forceType = function forceType2(a, b) {
  return _typeof(a) !== _typeof(b) ? [String(a), String(b)] : [a, b];
};
var compareStrings = function compareStrings2(a, b) {
  if (isWildcard(a) || isWildcard(b))
    return 0;
  var _forceType = forceType(tryParse(a), tryParse(b)), _forceType2 = _slicedToArray(_forceType, 2), ap = _forceType2[0], bp = _forceType2[1];
  if (ap > bp)
    return 1;
  if (ap < bp)
    return -1;
  return 0;
};
var compareSegments = function compareSegments2(a, b) {
  for (var i = 0; i < Math.max(a.length, b.length); i++) {
    var r = compareStrings(a[i] || "0", b[i] || "0");
    if (r !== 0)
      return r;
  }
  return 0;
};
var validateAndParse = function validateAndParse2(version) {
  var _match$shift;
  var match = version.match(semver);
  match === null || match === void 0 || (_match$shift = match.shift) === null || _match$shift === void 0 || _match$shift.call(match);
  return match;
};
var compareVersions = function compareVersions2(v1, v2) {
  var n1 = validateAndParse(v1);
  var n2 = validateAndParse(v2);
  var p1 = n1.pop();
  var p2 = n2.pop();
  var r = compareSegments(n1, n2);
  if (r !== 0)
    return r;
  if (p1 || p2) {
    return p1 ? -1 : 1;
  }
  return 0;
};

// node_modules/@ant-design/pro-utils/es/compareVersions/openVisibleCompatible.js
var getVersion = function getVersion2() {
  var _process;
  if (typeof process === "undefined")
    return version_default;
  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version_default;
};
var openVisibleCompatible = function openVisibleCompatible2(open, onOpenChange) {
  var props = compareVersions(getVersion(), "4.23.0") > -1 ? {
    open,
    onOpenChange
  } : {
    visible: open,
    onVisibleChange: onOpenChange
  };
  return omitUndefined3(props);
};

// node_modules/@ant-design/pro-utils/es/components/FilterDropdown/index.js
init_defineProperty();
var import_react9 = __toESM(require_react());
var import_classnames3 = __toESM(require_classnames());

// node_modules/@ant-design/pro-utils/es/components/FilterDropdown/style.js
init_defineProperty();
var genProStyle5 = function genProStyle6(token2) {
  return _defineProperty(_defineProperty(_defineProperty({}, "".concat(token2.componentCls, "-label"), {
    cursor: "pointer"
  }), "".concat(token2.componentCls, "-overlay"), {
    minWidth: "200px",
    marginBlockStart: "4px"
  }), "".concat(token2.componentCls, "-content"), {
    paddingBlock: 16,
    paddingInline: 16
  });
};
function useStyle4(prefixCls) {
  return useStyle("FilterDropdown", function(token2) {
    var proToken = _objectSpread2(_objectSpread2({}, token2), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle5(proToken)];
  });
}

// node_modules/@ant-design/pro-utils/es/components/FilterDropdown/index.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var FilterDropdown = function FilterDropdown2(props) {
  var children = props.children, label = props.label, footer = props.footer, open = props.open, onOpenChange = props.onOpenChange, disabled = props.disabled, onVisibleChange = props.onVisibleChange, visible = props.visible, footerRender = props.footerRender, placement = props.placement;
  var _useContext = (0, import_react9.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls("pro-core-field-dropdown");
  var _useStyle = useStyle4(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var dropdownOpenProps = openVisibleCompatible(open || visible || false, onOpenChange || onVisibleChange);
  var htmlRef = (0, import_react9.useRef)(null);
  return wrapSSR((0, import_jsx_runtime8.jsx)(popover_default, _objectSpread2(_objectSpread2({
    placement,
    trigger: ["click"]
  }, dropdownOpenProps), {}, {
    overlayInnerStyle: {
      padding: 0
    },
    content: (0, import_jsx_runtime9.jsxs)("div", {
      ref: htmlRef,
      className: (0, import_classnames3.default)("".concat(prefixCls, "-overlay"), _defineProperty(_defineProperty({}, "".concat(prefixCls, "-overlay-").concat(placement), placement), "hashId", hashId)),
      children: [(0, import_jsx_runtime8.jsx)(config_provider_default, {
        getPopupContainer: function getPopupContainer() {
          return htmlRef.current || document.body;
        },
        children: (0, import_jsx_runtime8.jsx)("div", {
          className: "".concat(prefixCls, "-content ").concat(hashId).trim(),
          children
        })
      }), footer && (0, import_jsx_runtime8.jsx)(DropdownFooter, _objectSpread2({
        disabled,
        footerRender
      }, footer))]
    }),
    children: (0, import_jsx_runtime8.jsx)("span", {
      className: "".concat(prefixCls, "-label ").concat(hashId).trim(),
      children: label
    })
  })));
};

// node_modules/@ant-design/pro-utils/es/components/InlineErrorFormItem/index.js
var import_react10 = __toESM(require_react());

// node_modules/@ant-design/pro-utils/es/components/InlineErrorFormItem/style.js
init_defineProperty();
var genProStyle7 = function genProStyle8(token2) {
  var progressBgCls = "".concat(token2.antCls, "-progress-bg");
  return _defineProperty({}, token2.componentCls, {
    "&-multiple": {
      paddingBlockStart: 6,
      paddingBlockEnd: 12,
      paddingInline: 8
    },
    "&-progress": {
      "&-success": _defineProperty({}, progressBgCls, {
        backgroundColor: token2.colorSuccess
      }),
      "&-error": _defineProperty({}, progressBgCls, {
        backgroundColor: token2.colorError
      }),
      "&-warning": _defineProperty({}, progressBgCls, {
        backgroundColor: token2.colorWarning
      })
    },
    "&-rule": {
      display: "flex",
      alignItems: "center",
      "&-icon": {
        "&-default": {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: "14px",
          height: "22px",
          "&-circle": {
            width: "6px",
            height: "6px",
            backgroundColor: token2.colorTextSecondary,
            borderRadius: "4px"
          }
        },
        "&-loading": {
          color: token2.colorPrimary
        },
        "&-error": {
          color: token2.colorError
        },
        "&-success": {
          color: token2.colorSuccess
        }
      },
      "&-text": {
        color: token2.colorText
      }
    }
  });
};
function useStyle5(prefixCls) {
  return useStyle("InlineErrorFormItem", function(token2) {
    var proToken = _objectSpread2(_objectSpread2({}, token2), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle7(proToken)];
  });
}

// node_modules/@ant-design/pro-utils/es/components/InlineErrorFormItem/index.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var _excluded3 = ["rules", "name", "children", "popoverProps"];
var _excluded22 = ["errorType", "rules", "name", "popoverProps", "children"];
var FIX_INLINE_STYLE = {
  marginBlockStart: -5,
  marginBlockEnd: -5,
  marginInlineStart: 0,
  marginInlineEnd: 0
};
var InlineErrorFormItemPopover = function InlineErrorFormItemPopover2(_ref) {
  var inputProps = _ref.inputProps, input = _ref.input, extra = _ref.extra, errorList = _ref.errorList, popoverProps = _ref.popoverProps;
  var _useState = (0, import_react10.useState)(false), _useState2 = _slicedToArray(_useState, 2), open = _useState2[0], setOpen = _useState2[1];
  var _useState3 = (0, import_react10.useState)([]), _useState4 = _slicedToArray(_useState3, 2), errorStringList = _useState4[0], setErrorList = _useState4[1];
  var _useContext = (0, import_react10.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls();
  var token2 = useToken3();
  var _useStyle = useStyle5("".concat(prefixCls, "-form-item-with-help")), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  (0, import_react10.useEffect)(function() {
    if (inputProps.validateStatus !== "validating") {
      setErrorList(inputProps.errors);
    }
  }, [inputProps.errors, inputProps.validateStatus]);
  var popoverOpenProps = openVisibleCompatible(errorStringList.length < 1 ? false : open, function(changeOpen) {
    if (changeOpen === open)
      return;
    setOpen(changeOpen);
  });
  var loading = inputProps.validateStatus === "validating";
  return (0, import_jsx_runtime10.jsx)(popover_default, _objectSpread2(_objectSpread2(_objectSpread2({
    trigger: (popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.trigger) || ["click"],
    placement: (popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.placement) || "topLeft"
  }, popoverOpenProps), {}, {
    getPopupContainer: popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.getPopupContainer,
    getTooltipContainer: popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.getTooltipContainer,
    content: wrapSSR((0, import_jsx_runtime10.jsx)("div", {
      className: "".concat(prefixCls, "-form-item ").concat(hashId, " ").concat(token2.hashId).trim(),
      style: {
        margin: 0,
        padding: 0
      },
      children: (0, import_jsx_runtime11.jsxs)("div", {
        className: "".concat(prefixCls, "-form-item-with-help ").concat(hashId, " ").concat(token2.hashId).trim(),
        children: [loading ? (0, import_jsx_runtime10.jsx)(LoadingOutlined_default, {}) : null, errorList]
      })
    }))
  }, popoverProps), {}, {
    children: (0, import_jsx_runtime11.jsxs)(import_jsx_runtime12.Fragment, {
      children: [input, extra]
    })
  }), "popover");
};
var InternalFormItemFunction = function InternalFormItemFunction2(_ref2) {
  var rules = _ref2.rules, name = _ref2.name, children = _ref2.children, popoverProps = _ref2.popoverProps, rest = _objectWithoutProperties(_ref2, _excluded3);
  return (0, import_jsx_runtime10.jsx)(form_default.Item, _objectSpread2(_objectSpread2({
    name,
    rules,
    hasFeedback: false,
    shouldUpdate: function shouldUpdate(prev, next) {
      if (prev === next)
        return false;
      var shouldName = [name].flat(1);
      if (shouldName.length > 1) {
        shouldName.pop();
      }
      try {
        return JSON.stringify(get(prev, shouldName)) !== JSON.stringify(get(next, shouldName));
      } catch (error2) {
        return true;
      }
    },
    _internalItemRender: {
      mark: "pro_table_render",
      render: function render(inputProps, doms) {
        return (0, import_jsx_runtime10.jsx)(InlineErrorFormItemPopover, _objectSpread2({
          inputProps,
          popoverProps
        }, doms));
      }
    }
  }, rest), {}, {
    style: _objectSpread2(_objectSpread2({}, FIX_INLINE_STYLE), rest === null || rest === void 0 ? void 0 : rest.style),
    children
  }));
};
var InlineErrorFormItem = function InlineErrorFormItem2(props) {
  var errorType = props.errorType, rules = props.rules, name = props.name, popoverProps = props.popoverProps, children = props.children, rest = _objectWithoutProperties(props, _excluded22);
  if (name && rules !== null && rules !== void 0 && rules.length && errorType === "popover") {
    return (0, import_jsx_runtime10.jsx)(InternalFormItemFunction, _objectSpread2(_objectSpread2({
      name,
      rules,
      popoverProps
    }, rest), {}, {
      children
    }));
  }
  return (0, import_jsx_runtime10.jsx)(form_default.Item, _objectSpread2(_objectSpread2({
    rules,
    shouldUpdate: name ? function(prev, next) {
      if (prev === next)
        return false;
      var shouldName = [name].flat(1);
      if (shouldName.length > 1) {
        shouldName.pop();
      }
      try {
        return JSON.stringify(get(prev, shouldName)) !== JSON.stringify(get(next, shouldName));
      } catch (error2) {
        return true;
      }
    } : void 0
  }, rest), {}, {
    style: _objectSpread2(_objectSpread2({}, FIX_INLINE_STYLE), rest.style),
    name,
    children
  }));
};

// node_modules/@ant-design/pro-utils/es/components/LabelIconTip/index.js
init_defineProperty();
var import_classnames4 = __toESM(require_classnames());
var import_react11 = __toESM(require_react());

// node_modules/@ant-design/pro-utils/es/components/LabelIconTip/style.js
init_defineProperty();
var genProStyle9 = function genProStyle10(token2) {
  return _defineProperty({}, token2.componentCls, {
    display: "inline-flex",
    alignItems: "center",
    maxWidth: "100%",
    "&-icon": {
      display: "block",
      marginInlineStart: "4px",
      cursor: "pointer",
      "&:hover": {
        color: token2.colorPrimary
      }
    },
    "&-title": {
      display: "inline-flex",
      flex: "1"
    },
    "&-subtitle ": {
      marginInlineStart: 8,
      color: token2.colorTextSecondary,
      fontWeight: "normal",
      fontSize: token2.fontSize,
      whiteSpace: "nowrap"
    },
    "&-title-ellipsis": {
      overflow: "hidden",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
      wordBreak: "keep-all"
    }
  });
};
function useStyle6(prefixCls) {
  return useStyle("LabelIconTip", function(token2) {
    var proToken = _objectSpread2(_objectSpread2({}, token2), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle9(proToken)];
  });
}

// node_modules/@ant-design/pro-utils/es/components/LabelIconTip/index.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var LabelIconTip = import_react11.default.memo(function(props) {
  var label = props.label, tooltip = props.tooltip, ellipsis = props.ellipsis, subTitle = props.subTitle;
  var _useContext = (0, import_react11.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var className = getPrefixCls("pro-core-label-tip");
  var _useStyle = useStyle6(className), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  if (!tooltip && !subTitle) {
    return (0, import_jsx_runtime14.jsx)(import_jsx_runtime13.Fragment, {
      children: label
    });
  }
  var tooltipProps = typeof tooltip === "string" || import_react11.default.isValidElement(tooltip) ? {
    title: tooltip
  } : tooltip;
  var icon = (tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.icon) || (0, import_jsx_runtime14.jsx)(InfoCircleOutlined_default, {});
  return wrapSSR((0, import_jsx_runtime15.jsxs)("div", {
    className: (0, import_classnames4.default)(className, hashId),
    onMouseDown: function onMouseDown(e) {
      return e.stopPropagation();
    },
    onMouseLeave: function onMouseLeave(e) {
      return e.stopPropagation();
    },
    onMouseMove: function onMouseMove(e) {
      return e.stopPropagation();
    },
    children: [(0, import_jsx_runtime14.jsx)("div", {
      className: (0, import_classnames4.default)("".concat(className, "-title"), hashId, _defineProperty({}, "".concat(className, "-title-ellipsis"), ellipsis)),
      children: label
    }), subTitle && (0, import_jsx_runtime14.jsx)("div", {
      className: "".concat(className, "-subtitle ").concat(hashId).trim(),
      children: subTitle
    }), tooltip && (0, import_jsx_runtime14.jsx)(tooltip_default, _objectSpread2(_objectSpread2({}, tooltipProps), {}, {
      children: (0, import_jsx_runtime14.jsx)("span", {
        className: "".concat(className, "-icon ").concat(hashId).trim(),
        children: icon
      })
    }))]
  }));
});

// node_modules/@ant-design/pro-utils/es/components/ProFormContext/index.js
var import_react12 = __toESM(require_react());
var ProFormContext = import_react12.default.createContext({});

// node_modules/@ant-design/pro-utils/es/isNil/index.js
var isNil = function isNil2(value) {
  return value === null || value === void 0;
};

// node_modules/@ant-design/pro-utils/es/conversionMomentValue/index.js
init_typeof();
var import_dayjs2 = __toESM(require_dayjs_min());
var import_quarterOfYear = __toESM(require_quarterOfYear());
import_dayjs2.default.extend(import_quarterOfYear.default);
var dateFormatterMap = {
  time: "HH:mm:ss",
  timeRange: "HH:mm:ss",
  date: "YYYY-MM-DD",
  dateWeek: "YYYY-wo",
  dateMonth: "YYYY-MM",
  dateQuarter: "YYYY-[Q]Q",
  dateYear: "YYYY",
  dateRange: "YYYY-MM-DD",
  dateTime: "YYYY-MM-DD HH:mm:ss",
  dateTimeRange: "YYYY-MM-DD HH:mm:ss"
};
function isObject(o) {
  return Object.prototype.toString.call(o) === "[object Object]";
}
function isPlainObject(o) {
  if (isObject(o) === false)
    return false;
  var ctor = o.constructor;
  if (ctor === void 0)
    return true;
  var prot = ctor.prototype;
  if (isObject(prot) === false)
    return false;
  if (prot.hasOwnProperty("isPrototypeOf") === false) {
    return false;
  }
  return true;
}
var isMoment = function isMoment2(value) {
  return !!(value !== null && value !== void 0 && value._isAMomentObject);
};
var convertMoment = function convertMoment2(value, dateFormatter, valueType) {
  if (!dateFormatter) {
    return value;
  }
  if (import_dayjs2.default.isDayjs(value) || isMoment(value)) {
    if (dateFormatter === "number") {
      return value.valueOf();
    }
    if (dateFormatter === "string") {
      return value.format(dateFormatterMap[valueType] || "YYYY-MM-DD HH:mm:ss");
    }
    if (typeof dateFormatter === "string" && dateFormatter !== "string") {
      return value.format(dateFormatter);
    }
    if (typeof dateFormatter === "function") {
      return dateFormatter(value, valueType);
    }
  }
  return value;
};
var conversionMomentValue = function conversionMomentValue2(value, dateFormatter, valueTypeMap, omitNil, parentKey) {
  var tmpValue = {};
  if (typeof window === "undefined")
    return value;
  if (_typeof(value) !== "object" || isNil(value) || value instanceof Blob || Array.isArray(value)) {
    return value;
  }
  Object.keys(value).forEach(function(valueKey) {
    var namePath = parentKey ? [parentKey, valueKey].flat(1) : [valueKey];
    var valueFormatMap = get(valueTypeMap, namePath) || "text";
    var valueType = "text";
    var dateFormat;
    if (typeof valueFormatMap === "string") {
      valueType = valueFormatMap;
    } else if (valueFormatMap) {
      valueType = valueFormatMap.valueType;
      dateFormat = valueFormatMap.dateFormat;
    }
    var itemValue = value[valueKey];
    if (isNil(itemValue) && omitNil) {
      return;
    }
    if (isPlainObject(itemValue) && // 不是数组
    !Array.isArray(itemValue) && // 不是 dayjs
    !import_dayjs2.default.isDayjs(itemValue) && // 不是 moment
    !isMoment(itemValue)) {
      tmpValue[valueKey] = conversionMomentValue2(itemValue, dateFormatter, valueTypeMap, omitNil, namePath);
      return;
    }
    if (Array.isArray(itemValue)) {
      tmpValue[valueKey] = itemValue.map(function(arrayValue, index2) {
        if (import_dayjs2.default.isDayjs(arrayValue) || isMoment(arrayValue)) {
          return convertMoment(arrayValue, dateFormat || dateFormatter, valueType);
        }
        return conversionMomentValue2(arrayValue, dateFormatter, valueTypeMap, omitNil, [valueKey, "".concat(index2)].flat(1));
      });
      return;
    }
    tmpValue[valueKey] = convertMoment(itemValue, dateFormat || dateFormatter, valueType);
  });
  return tmpValue;
};

// node_modules/@ant-design/pro-utils/es/dateArrayFormatter/index.js
init_typeof();
var import_dayjs3 = __toESM(require_dayjs_min());
var formatString = function formatString2(endText, format) {
  if (typeof format === "function") {
    return format((0, import_dayjs3.default)(endText));
  }
  return (0, import_dayjs3.default)(endText).format(format);
};
var dateArrayFormatter = function dateArrayFormatter2(value, format) {
  var _ref = Array.isArray(value) ? value : [], _ref2 = _slicedToArray(_ref, 2), startText = _ref2[0], endText = _ref2[1];
  var formatFirst;
  var formatEnd;
  if (Array.isArray(format)) {
    formatFirst = format[0];
    formatEnd = format[1];
  } else if (_typeof(format) === "object" && format.type === "mask") {
    formatFirst = format.format;
    formatEnd = format.format;
  } else {
    formatFirst = format;
    formatEnd = format;
  }
  var parsedStartText = startText ? formatString(startText, formatFirst) : "";
  var parsedEndText = endText ? formatString(endText, formatEnd) : "";
  var valueStr = parsedStartText && parsedEndText ? "".concat(parsedStartText, " ~ ").concat(parsedEndText) : "";
  return valueStr;
};

// node_modules/@ant-design/pro-utils/es/genCopyable/index.js
var import_react13 = __toESM(require_react());
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var isNeedTranText = function isNeedTranText2(item) {
  var _item$valueType;
  if (item !== null && item !== void 0 && (_item$valueType = item.valueType) !== null && _item$valueType !== void 0 && _item$valueType.toString().startsWith("date")) {
    return true;
  }
  if ((item === null || item === void 0 ? void 0 : item.valueType) === "select" || item !== null && item !== void 0 && item.valueEnum) {
    return true;
  }
  return false;
};
var getEllipsis = function getEllipsis2(item) {
  var _item$ellipsis;
  if (((_item$ellipsis = item.ellipsis) === null || _item$ellipsis === void 0 ? void 0 : _item$ellipsis.showTitle) === false) {
    return false;
  }
  return item.ellipsis;
};
var genCopyable = function genCopyable2(dom, item, text) {
  if (item.copyable || item.ellipsis) {
    var copyable = item.copyable && text ? {
      text,
      tooltips: ["", ""]
    } : void 0;
    var needTranText = isNeedTranText(item);
    var ellipsis = getEllipsis(item) && text ? {
      tooltip: (
        // 支持一下 tooltip 的关闭
        (item === null || item === void 0 ? void 0 : item.tooltip) !== false && needTranText ? (0, import_jsx_runtime16.jsx)("div", {
          className: "pro-table-tooltip-text",
          children: dom
        }) : text
      )
    } : false;
    return (0, import_jsx_runtime16.jsx)(typography_default.Text, {
      style: {
        width: "100%",
        margin: 0,
        padding: 0
      },
      title: "",
      copyable,
      ellipsis,
      children: dom
    });
  }
  return dom;
};

// node_modules/@ant-design/pro-utils/es/runFunction/index.js
function runFunction(valueEnum) {
  if (typeof valueEnum === "function") {
    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      rest[_key - 1] = arguments[_key];
    }
    return valueEnum.apply(void 0, rest);
  }
  return valueEnum;
}

// node_modules/@ant-design/pro-utils/es/getFieldPropsOrFormItemProps/index.js
var getFieldPropsOrFormItemProps = function getFieldPropsOrFormItemProps2(fieldProps, form, extraProps) {
  if (form === void 0) {
    return fieldProps;
  }
  return runFunction(fieldProps, form, extraProps);
};

// node_modules/@ant-design/pro-utils/es/compareVersions/coverToNewToken.js
function coverToNewToken(token2) {
  if (compareVersions(getVersion(), "5.6.0") < 0)
    return token2;
  var deprecatedTokens = {
    colorGroupTitle: "groupTitleColor",
    radiusItem: "itemBorderRadius",
    radiusSubMenuItem: "subMenuItemBorderRadius",
    colorItemText: "itemColor",
    colorItemTextHover: "itemHoverColor",
    colorItemTextHoverHorizontal: "horizontalItemHoverColor",
    colorItemTextSelected: "itemSelectedColor",
    colorItemTextSelectedHorizontal: "horizontalItemSelectedColor",
    colorItemTextDisabled: "itemDisabledColor",
    colorDangerItemText: "dangerItemColor",
    colorDangerItemTextHover: "dangerItemHoverColor",
    colorDangerItemTextSelected: "dangerItemSelectedColor",
    colorDangerItemBgActive: "dangerItemActiveBg",
    colorDangerItemBgSelected: "dangerItemSelectedBg",
    colorItemBg: "itemBg",
    colorItemBgHover: "itemHoverBg",
    colorSubItemBg: "subMenuItemBg",
    colorItemBgActive: "itemActiveBg",
    colorItemBgSelected: "itemSelectedBg",
    colorItemBgSelectedHorizontal: "horizontalItemSelectedBg",
    colorActiveBarWidth: "activeBarWidth",
    colorActiveBarHeight: "activeBarHeight",
    colorActiveBarBorderSize: "activeBarBorderWidth"
  };
  var newToken = _objectSpread2({}, token2);
  Object.keys(deprecatedTokens).forEach(function(key) {
    if (newToken[key] !== void 0) {
      newToken[deprecatedTokens[key]] = newToken[key];
      delete newToken[key];
    }
  });
  return newToken;
}

// node_modules/@ant-design/pro-utils/es/compareVersions/menuOverlayCompatible.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
var menuOverlayCompatible = function menuOverlayCompatible2(menu) {
  var props = compareVersions(getVersion(), "4.24.0") > -1 ? {
    menu
  } : {
    overlay: (0, import_jsx_runtime17.jsx)(menu_default, _objectSpread2({}, menu))
  };
  return omitUndefined3(props);
};

// node_modules/@ant-design/pro-utils/es/hooks/useRefFunction/index.js
var import_react14 = __toESM(require_react());
var useRefFunction = function useRefFunction2(reFunction) {
  var ref = (0, import_react14.useRef)(null);
  ref.current = reFunction;
  return (0, import_react14.useCallback)(function() {
    var _ref$current;
    for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
      rest[_key] = arguments[_key];
    }
    return (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.call.apply(_ref$current, [ref].concat(_toConsumableArray(rest)));
  }, []);
};

// node_modules/@ant-design/pro-utils/es/hooks/useDebounceFn/index.js
var import_react15 = __toESM(require_react());
function useDebounceFn(fn, wait) {
  var callback = useRefFunction(fn);
  var timer = (0, import_react15.useRef)();
  var cancel = (0, import_react15.useCallback)(function() {
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }
  }, []);
  var run = (0, import_react15.useCallback)(_asyncToGenerator(_regeneratorRuntime().mark(function _callee2() {
    var _len, args, _key, _args2 = arguments;
    return _regeneratorRuntime().wrap(function _callee2$(_context2) {
      while (1)
        switch (_context2.prev = _context2.next) {
          case 0:
            for (_len = _args2.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
              args[_key] = _args2[_key];
            }
            if (!(wait === 0 || wait === void 0)) {
              _context2.next = 3;
              break;
            }
            return _context2.abrupt("return", callback.apply(void 0, args));
          case 3:
            cancel();
            return _context2.abrupt("return", new Promise(function(resolve) {
              timer.current = setTimeout(_asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
                return _regeneratorRuntime().wrap(function _callee$(_context) {
                  while (1)
                    switch (_context.prev = _context.next) {
                      case 0:
                        _context.t0 = resolve;
                        _context.next = 3;
                        return callback.apply(void 0, args);
                      case 3:
                        _context.t1 = _context.sent;
                        (0, _context.t0)(_context.t1);
                        return _context.abrupt("return");
                      case 6:
                      case "end":
                        return _context.stop();
                    }
                }, _callee);
              })), wait);
            }));
          case 5:
          case "end":
            return _context2.stop();
        }
    }, _callee2);
  })), [callback, cancel, wait]);
  (0, import_react15.useEffect)(function() {
    return cancel;
  }, [cancel]);
  return {
    run,
    cancel
  };
}

// node_modules/@ant-design/pro-utils/es/hooks/useLatest/index.js
var import_react16 = __toESM(require_react());
var useLatest = function useLatest2(value) {
  var ref = (0, import_react16.useRef)(value);
  ref.current = value;
  return ref;
};

// node_modules/@ant-design/pro-utils/es/hooks/useDebounceValue/index.js
var import_react17 = __toESM(require_react());
function useDebounceValue(value) {
  var delay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;
  var deps = arguments.length > 2 ? arguments[2] : void 0;
  var _useState = (0, import_react17.useState)(value), _useState2 = _slicedToArray(_useState, 2), debouncedValue = _useState2[0], setDebouncedValue = _useState2[1];
  var valueRef = useLatest(value);
  (0, import_react17.useEffect)(
    function() {
      var handler = setTimeout(function() {
        setDebouncedValue(valueRef.current);
      }, delay);
      return function() {
        return clearTimeout(handler);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    deps ? [delay].concat(_toConsumableArray(deps)) : void 0
  );
  return debouncedValue;
}

// node_modules/@ant-design/pro-utils/es/isDeepEqualReact/index.js
init_typeof();
function isDeepEqualReact(a, b, ignoreKeys, debug) {
  if (a === b)
    return true;
  if (a && b && _typeof(a) === "object" && _typeof(b) === "object") {
    if (a.constructor !== b.constructor)
      return false;
    var length;
    var i;
    var keys;
    if (Array.isArray(a)) {
      length = a.length;
      if (length != b.length)
        return false;
      for (i = length; i-- !== 0; )
        if (!isDeepEqualReact(a[i], b[i], ignoreKeys, debug))
          return false;
      return true;
    }
    if (a instanceof Map && b instanceof Map) {
      if (a.size !== b.size)
        return false;
      var _iterator = _createForOfIteratorHelper(a.entries()), _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done; ) {
          i = _step.value;
          if (!b.has(i[0]))
            return false;
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      var _iterator2 = _createForOfIteratorHelper(a.entries()), _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done; ) {
          i = _step2.value;
          if (!isDeepEqualReact(i[1], b.get(i[0]), ignoreKeys, debug))
            return false;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return true;
    }
    if (a instanceof Set && b instanceof Set) {
      if (a.size !== b.size)
        return false;
      var _iterator3 = _createForOfIteratorHelper(a.entries()), _step3;
      try {
        for (_iterator3.s(); !(_step3 = _iterator3.n()).done; ) {
          i = _step3.value;
          if (!b.has(i[0]))
            return false;
        }
      } catch (err) {
        _iterator3.e(err);
      } finally {
        _iterator3.f();
      }
      return true;
    }
    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
      length = a.length;
      if (length != b.length)
        return false;
      for (i = length; i-- !== 0; )
        if (a[i] !== b[i])
          return false;
      return true;
    }
    if (a.constructor === RegExp)
      return a.source === b.source && a.flags === b.flags;
    if (a.valueOf !== Object.prototype.valueOf && a.valueOf)
      return a.valueOf() === b.valueOf();
    if (a.toString !== Object.prototype.toString && a.toString)
      return a.toString() === b.toString();
    keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b).length)
      return false;
    for (i = length; i-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(b, keys[i]))
        return false;
    for (i = length; i-- !== 0; ) {
      var key = keys[i];
      if (ignoreKeys !== null && ignoreKeys !== void 0 && ignoreKeys.includes(key))
        continue;
      if (key === "_owner" && a.$$typeof) {
        continue;
      }
      if (!isDeepEqualReact(a[key], b[key], ignoreKeys, debug)) {
        if (debug) {
          console.log(key);
        }
        return false;
      }
    }
    return true;
  }
  return a !== a && b !== b;
}

// node_modules/@ant-design/pro-utils/es/hooks/useDeepCompareEffect/index.js
var import_react18 = __toESM(require_react());
var isDeepEqual = function isDeepEqual2(a, b, ignoreKeys) {
  return isDeepEqualReact(a, b, ignoreKeys);
};
function useDeepCompareMemoize(value, ignoreKeys) {
  var ref = (0, import_react18.useRef)();
  if (!isDeepEqual(value, ref.current, ignoreKeys)) {
    ref.current = value;
  }
  return ref.current;
}
function useDeepCompareEffect(effect, dependencies, ignoreKeys) {
  (0, import_react18.useEffect)(effect, useDeepCompareMemoize(dependencies || [], ignoreKeys));
}
function useDeepCompareEffectDebounce(effect, dependencies, ignoreKeys, waitTime) {
  var effectDn = useDebounceFn(_asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1)
        switch (_context.prev = _context.next) {
          case 0:
            effect();
          case 1:
          case "end":
            return _context.stop();
        }
    }, _callee);
  })), waitTime || 16);
  (0, import_react18.useEffect)(function() {
    effectDn.run();
  }, useDeepCompareMemoize(dependencies || [], ignoreKeys));
}

// node_modules/@ant-design/pro-utils/es/hooks/useDeepCompareMemo/index.js
var import_react19 = __toESM(require_react());
function useDeepCompareMemo(factory, dependencies) {
  return import_react19.default.useMemo(factory, useDeepCompareMemoize(dependencies));
}
var useDeepCompareMemo_default = useDeepCompareMemo;

// node_modules/@ant-design/pro-utils/es/isBrowser/index.js
var isNode = typeof process !== "undefined" && process.versions != null && process.versions.node != null;
var isBrowser = function isBrowser2() {
  if (typeof process !== "undefined" && false) {
    return true;
  }
  return typeof window !== "undefined" && typeof window.document !== "undefined" && typeof window.matchMedia !== "undefined" && !isNode;
};

// node_modules/@ant-design/pro-utils/es/hooks/useDocumentTitle/index.js
var import_react20 = __toESM(require_react());
function useDocumentTitle(titleInfo, appDefaultTitle) {
  var titleText = typeof titleInfo.pageName === "string" ? titleInfo.title : appDefaultTitle;
  (0, import_react20.useEffect)(function() {
    if (isBrowser() && titleText) {
      document.title = titleText;
    }
  }, [titleInfo.title, titleText]);
}

// node_modules/@ant-design/pro-utils/es/hooks/useFetchData/index.js
var import_react21 = __toESM(require_react());
var testId = 0;
function useFetchData(props) {
  var abortRef = (0, import_react21.useRef)(null);
  var _useState = (0, import_react21.useState)(function() {
    if (props.proFieldKey) {
      return props.proFieldKey.toString();
    }
    testId += 1;
    return testId.toString();
  }), _useState2 = _slicedToArray(_useState, 1), cacheKey = _useState2[0];
  var proFieldKeyRef = (0, import_react21.useRef)(cacheKey);
  var fetchData = function() {
    var _ref = _asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
      var _abortRef$current, _props$request;
      var abort, loadData;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              (_abortRef$current = abortRef.current) === null || _abortRef$current === void 0 || _abortRef$current.abort();
              abort = new AbortController();
              abortRef.current = abort;
              _context.next = 5;
              return Promise.race([(_props$request = props.request) === null || _props$request === void 0 ? void 0 : _props$request.call(props, props.params, props), new Promise(function(_, reject) {
                var _abortRef$current2;
                (_abortRef$current2 = abortRef.current) === null || _abortRef$current2 === void 0 || (_abortRef$current2 = _abortRef$current2.signal) === null || _abortRef$current2 === void 0 || _abortRef$current2.addEventListener("abort", function() {
                  reject(new Error("aborted"));
                });
              })]);
            case 5:
              loadData = _context.sent;
              return _context.abrupt("return", loadData);
            case 7:
            case "end":
              return _context.stop();
          }
      }, _callee);
    }));
    return function fetchData2() {
      return _ref.apply(this, arguments);
    };
  }();
  (0, import_react21.useEffect)(function() {
    return function() {
      testId += 1;
    };
  }, []);
  var _useSWR = useSWR([proFieldKeyRef.current, props.params], fetchData, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
    revalidateOnReconnect: false
  }), data = _useSWR.data, error2 = _useSWR.error;
  return [data || error2];
}

// node_modules/@ant-design/pro-utils/es/hooks/usePrevious/index.js
var import_react22 = __toESM(require_react());
var usePrevious = function usePrevious2(state) {
  var ref = (0, import_react22.useRef)();
  (0, import_react22.useEffect)(function() {
    ref.current = state;
  });
  return ref.current;
};

// node_modules/@ant-design/pro-utils/es/hooks/useRefCallback/index.js
var import_react23 = __toESM(require_react());
function useRefCallback(callback, initialValue) {
  var ref = (0, import_react23.useMemo)(function() {
    var defaultValue = {
      current: initialValue
    };
    return new Proxy(defaultValue, {
      set: function set2(target, prop, newValue) {
        if (!Object.is(target[prop], newValue)) {
          target[prop] = newValue;
          callback(ref);
        }
        return true;
      }
    });
  }, []);
  return ref;
}

// node_modules/@ant-design/pro-utils/es/hooks/useForceRender/index.js
var import_react24 = __toESM(require_react());
function useForceRender() {
  var _useState = (0, import_react24.useState)(true), _useState2 = _slicedToArray(_useState, 2), setValue = _useState2[1];
  var updateValue = (0, import_react24.useCallback)(function() {
    return setValue(function(oldValue) {
      return !oldValue;
    });
  }, []);
  return updateValue;
}

// node_modules/@ant-design/pro-utils/es/hooks/useReactiveRef/index.js
function useReactiveRef(initialValue) {
  var forceRender = useForceRender();
  var ref = useRefCallback(forceRender, initialValue);
  return ref;
}

// node_modules/@ant-design/pro-utils/es/isDropdownValueType/index.js
var isDropdownValueType = function isDropdownValueType2(valueType) {
  var isDropdown = false;
  if (typeof valueType === "string" && valueType.startsWith("date") && !valueType.endsWith("Range") || valueType === "select" || valueType === "time") {
    isDropdown = true;
  }
  return isDropdown;
};

// node_modules/@ant-design/pro-utils/es/isImg/index.js
function isImg(path) {
  return /\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(path);
}

// node_modules/@ant-design/pro-utils/es/isUrl/index.js
var isUrl = function isUrl2(path) {
  if (!path)
    return false;
  if (!path.startsWith("http")) {
    return false;
  }
  try {
    var url = new URL(path);
    return !!url;
  } catch (error2) {
    return false;
  }
};

// node_modules/@ant-design/pro-utils/es/merge/index.js
init_typeof();
var merge3 = function merge4() {
  var obj = {};
  for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
    rest[_key] = arguments[_key];
  }
  var il = rest.length;
  var key;
  var i = 0;
  for (; i < il; i += 1) {
    for (key in rest[i]) {
      if (rest[i].hasOwnProperty(key)) {
        if (_typeof(obj[key]) === "object" && _typeof(rest[i][key]) === "object" && obj[key] !== void 0 && obj[key] !== null && !Array.isArray(obj[key]) && !Array.isArray(rest[i][key])) {
          obj[key] = _objectSpread2(_objectSpread2({}, obj[key]), rest[i][key]);
        } else {
          obj[key] = rest[i][key];
        }
      }
    }
  }
  return obj;
};

// node_modules/@ant-design/pro-utils/es/nanoid/index.js
var index = 0;
var genNanoid = function genNanoid2() {
  var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 21;
  if (typeof window === "undefined")
    return (index += 1).toFixed(0);
  if (!window.crypto)
    return (index += 1).toFixed(0);
  var e = "", r = crypto.getRandomValues(new Uint8Array(t));
  for (; t--; ) {
    var n = 63 & r[t];
    e += n < 36 ? n.toString(36) : n < 62 ? (n - 26).toString(36).toUpperCase() : n < 63 ? "_" : "-";
  }
  return e;
};
var nanoid = function nanoid2() {
  if (typeof window === "undefined")
    return genNanoid();
  if (window.crypto && window.crypto.randomUUID && typeof crypto.randomUUID == "function") {
    return crypto.randomUUID();
  }
  return genNanoid();
};

// node_modules/@ant-design/pro-utils/es/omitBoolean/index.js
var omitBoolean = function omitBoolean2(obj) {
  if (obj && obj !== true) {
    return obj;
  }
  return void 0;
};

// node_modules/@ant-design/pro-utils/es/omitUndefinedAndEmptyArr/index.js
var omitUndefinedAndEmptyArr = function omitUndefinedAndEmptyArr2(obj) {
  var newObj = {};
  Object.keys(obj || {}).forEach(function(key) {
    var _obj$key;
    if (Array.isArray(obj[key]) && ((_obj$key = obj[key]) === null || _obj$key === void 0 ? void 0 : _obj$key.length) === 0) {
      return;
    }
    if (obj[key] === void 0) {
      return;
    }
    newObj[key] = obj[key];
  });
  return newObj;
};

// node_modules/@ant-design/pro-utils/es/parseValueToMoment/index.js
var import_dayjs4 = __toESM(require_dayjs_min());
var import_customParseFormat = __toESM(require_customParseFormat());
import_dayjs4.default.extend(import_customParseFormat.default);
var isMoment3 = function isMoment4(value) {
  return !!(value !== null && value !== void 0 && value._isAMomentObject);
};
var parseValueToDay = function parseValueToDay2(value, formatter) {
  if (isNil(value) || import_dayjs4.default.isDayjs(value) || isMoment3(value)) {
    if (isMoment3(value)) {
      return (0, import_dayjs4.default)(value);
    }
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(function(v) {
      return parseValueToDay2(v, formatter);
    });
  }
  if (typeof value === "number")
    return (0, import_dayjs4.default)(value);
  return (0, import_dayjs4.default)(value, formatter);
};

// node_modules/@ant-design/pro-utils/es/pickProFormItemProps/index.js
var antdFormItemPropsList = [
  // https://ant.design/components/form-cn/#Form.Item
  "colon",
  "dependencies",
  "extra",
  "getValueFromEvent",
  "getValueProps",
  "hasFeedback",
  "help",
  "htmlFor",
  "initialValue",
  "noStyle",
  "label",
  "labelAlign",
  "labelCol",
  "name",
  "preserve",
  "normalize",
  "required",
  "rules",
  "shouldUpdate",
  "trigger",
  "validateFirst",
  "validateStatus",
  "validateTrigger",
  "valuePropName",
  "wrapperCol",
  "hidden",
  // 我自定义的
  "addonBefore",
  "addonAfter",
  "addonWarpStyle"
];
function pickProFormItemProps(props) {
  var attrs = {};
  antdFormItemPropsList.forEach(function(key) {
    if (props[key] !== void 0) {
      attrs[key] = props[key];
    }
  });
  return attrs;
}

// node_modules/@ant-design/pro-utils/es/pickProProps/index.js
var proFieldProps = "valueType request plain renderFormItem render text formItemProps valueEnum";
var proFormProps = "fieldProps isDefaultDom groupProps contentRender submitterProps submitter";
function pickProProps(props) {
  var propList = "".concat(proFieldProps, " ").concat(proFormProps).split(/[\s\n]+/);
  var attrs = {};
  Object.keys(props || {}).forEach(function(key) {
    if (propList.includes(key)) {
      return;
    }
    attrs[key] = props[key];
  });
  return attrs;
}

// node_modules/@ant-design/pro-utils/es/proFieldParsingText/index.js
init_typeof();
var import_react25 = __toESM(require_react());
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
function getType(obj) {
  var type = Object.prototype.toString.call(obj).match(/^\[object (.*)\]$/)[1].toLowerCase();
  if (type === "string" && _typeof(obj) === "object")
    return "object";
  if (obj === null)
    return "null";
  if (obj === void 0)
    return "undefined";
  return type;
}
var ProFieldBadgeColor = function ProFieldBadgeColor2(_ref) {
  var color = _ref.color, children = _ref.children;
  return (0, import_jsx_runtime18.jsx)(badge_default, {
    color,
    text: children
  });
};
var objectToMap = function objectToMap2(value) {
  if (getType(value) === "map") {
    return value;
  }
  return new Map(Object.entries(value || {}));
};
var TableStatus = {
  Success: function Success(_ref2) {
    var children = _ref2.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "success",
      text: children
    });
  },
  Error: function Error2(_ref3) {
    var children = _ref3.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "error",
      text: children
    });
  },
  Default: function Default(_ref4) {
    var children = _ref4.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "default",
      text: children
    });
  },
  Processing: function Processing(_ref5) {
    var children = _ref5.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "processing",
      text: children
    });
  },
  Warning: function Warning(_ref6) {
    var children = _ref6.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "warning",
      text: children
    });
  },
  success: function success(_ref7) {
    var children = _ref7.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "success",
      text: children
    });
  },
  error: function error(_ref8) {
    var children = _ref8.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "error",
      text: children
    });
  },
  default: function _default(_ref9) {
    var children = _ref9.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "default",
      text: children
    });
  },
  processing: function processing(_ref10) {
    var children = _ref10.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "processing",
      text: children
    });
  },
  warning: function warning(_ref11) {
    var children = _ref11.children;
    return (0, import_jsx_runtime18.jsx)(badge_default, {
      status: "warning",
      text: children
    });
  }
};
var proFieldParsingText = function proFieldParsingText2(text, valueEnumParams, key) {
  if (Array.isArray(text)) {
    return (0, import_jsx_runtime18.jsx)(space_default, {
      split: ",",
      size: 2,
      wrap: true,
      children: text.map(function(value, index2) {
        return (
          // @ts-ignore
          proFieldParsingText2(value, valueEnumParams, index2)
        );
      })
    }, key);
  }
  var valueEnum = objectToMap(valueEnumParams);
  if (!valueEnum.has(text) && !valueEnum.has("".concat(text))) {
    return (text === null || text === void 0 ? void 0 : text.label) || text;
  }
  var domText = valueEnum.get(text) || valueEnum.get("".concat(text));
  if (!domText) {
    return (0, import_jsx_runtime18.jsx)(import_react25.default.Fragment, {
      children: (text === null || text === void 0 ? void 0 : text.label) || text
    }, key);
  }
  var status = domText.status, color = domText.color;
  var Status = TableStatus[status || "Init"];
  if (Status) {
    return (0, import_jsx_runtime18.jsx)(Status, {
      children: domText.text
    }, key);
  }
  if (color) {
    return (0, import_jsx_runtime18.jsx)(ProFieldBadgeColor, {
      color,
      children: domText.text
    }, key);
  }
  return (0, import_jsx_runtime18.jsx)(import_react25.default.Fragment, {
    children: domText.text || domText
  }, key);
};

// node_modules/safe-stable-stringify/esm/wrapper.js
var import__2 = __toESM(require_safe_stable_stringify());
var configure = import__2.default.configure;

// node_modules/@ant-design/pro-utils/es/stringify/index.js
var stringify = configure({
  bigint: true,
  circularValue: "Magic circle!",
  deterministic: false,
  maximumDepth: 4
  //   maximumBreadth: 4,
});
var stringify_default = stringify;

// node_modules/@ant-design/pro-utils/es/transformKeySubmitValue/index.js
init_typeof();

// node_modules/lodash-es/_listCacheClear.js
function listCacheClear() {
  this.__data__ = [];
  this.size = 0;
}
var listCacheClear_default = listCacheClear;

// node_modules/lodash-es/eq.js
function eq(value, other) {
  return value === other || value !== value && other !== other;
}
var eq_default = eq;

// node_modules/lodash-es/_assocIndexOf.js
function assocIndexOf(array, key) {
  var length = array.length;
  while (length--) {
    if (eq_default(array[length][0], key)) {
      return length;
    }
  }
  return -1;
}
var assocIndexOf_default = assocIndexOf;

// node_modules/lodash-es/_listCacheDelete.js
var arrayProto = Array.prototype;
var splice = arrayProto.splice;
function listCacheDelete(key) {
  var data = this.__data__, index2 = assocIndexOf_default(data, key);
  if (index2 < 0) {
    return false;
  }
  var lastIndex = data.length - 1;
  if (index2 == lastIndex) {
    data.pop();
  } else {
    splice.call(data, index2, 1);
  }
  --this.size;
  return true;
}
var listCacheDelete_default = listCacheDelete;

// node_modules/lodash-es/_listCacheGet.js
function listCacheGet(key) {
  var data = this.__data__, index2 = assocIndexOf_default(data, key);
  return index2 < 0 ? void 0 : data[index2][1];
}
var listCacheGet_default = listCacheGet;

// node_modules/lodash-es/_listCacheHas.js
function listCacheHas(key) {
  return assocIndexOf_default(this.__data__, key) > -1;
}
var listCacheHas_default = listCacheHas;

// node_modules/lodash-es/_listCacheSet.js
function listCacheSet(key, value) {
  var data = this.__data__, index2 = assocIndexOf_default(data, key);
  if (index2 < 0) {
    ++this.size;
    data.push([key, value]);
  } else {
    data[index2][1] = value;
  }
  return this;
}
var listCacheSet_default = listCacheSet;

// node_modules/lodash-es/_ListCache.js
function ListCache(entries) {
  var index2 = -1, length = entries == null ? 0 : entries.length;
  this.clear();
  while (++index2 < length) {
    var entry = entries[index2];
    this.set(entry[0], entry[1]);
  }
}
ListCache.prototype.clear = listCacheClear_default;
ListCache.prototype["delete"] = listCacheDelete_default;
ListCache.prototype.get = listCacheGet_default;
ListCache.prototype.has = listCacheHas_default;
ListCache.prototype.set = listCacheSet_default;
var ListCache_default = ListCache;

// node_modules/lodash-es/_stackClear.js
function stackClear() {
  this.__data__ = new ListCache_default();
  this.size = 0;
}
var stackClear_default = stackClear;

// node_modules/lodash-es/_stackDelete.js
function stackDelete(key) {
  var data = this.__data__, result = data["delete"](key);
  this.size = data.size;
  return result;
}
var stackDelete_default = stackDelete;

// node_modules/lodash-es/_stackGet.js
function stackGet(key) {
  return this.__data__.get(key);
}
var stackGet_default = stackGet;

// node_modules/lodash-es/_stackHas.js
function stackHas(key) {
  return this.__data__.has(key);
}
var stackHas_default = stackHas;

// node_modules/lodash-es/_freeGlobal.js
var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
var freeGlobal_default = freeGlobal;

// node_modules/lodash-es/_root.js
var freeSelf = typeof self == "object" && self && self.Object === Object && self;
var root = freeGlobal_default || freeSelf || Function("return this")();
var root_default = root;

// node_modules/lodash-es/_Symbol.js
var Symbol2 = root_default.Symbol;
var Symbol_default = Symbol2;

// node_modules/lodash-es/_getRawTag.js
var objectProto = Object.prototype;
var hasOwnProperty = objectProto.hasOwnProperty;
var nativeObjectToString = objectProto.toString;
var symToStringTag = Symbol_default ? Symbol_default.toStringTag : void 0;
function getRawTag(value) {
  var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];
  try {
    value[symToStringTag] = void 0;
    var unmasked = true;
  } catch (e) {
  }
  var result = nativeObjectToString.call(value);
  if (unmasked) {
    if (isOwn) {
      value[symToStringTag] = tag;
    } else {
      delete value[symToStringTag];
    }
  }
  return result;
}
var getRawTag_default = getRawTag;

// node_modules/lodash-es/_objectToString.js
var objectProto2 = Object.prototype;
var nativeObjectToString2 = objectProto2.toString;
function objectToString(value) {
  return nativeObjectToString2.call(value);
}
var objectToString_default = objectToString;

// node_modules/lodash-es/_baseGetTag.js
var nullTag = "[object Null]";
var undefinedTag = "[object Undefined]";
var symToStringTag2 = Symbol_default ? Symbol_default.toStringTag : void 0;
function baseGetTag(value) {
  if (value == null) {
    return value === void 0 ? undefinedTag : nullTag;
  }
  return symToStringTag2 && symToStringTag2 in Object(value) ? getRawTag_default(value) : objectToString_default(value);
}
var baseGetTag_default = baseGetTag;

// node_modules/lodash-es/isObject.js
function isObject2(value) {
  var type = typeof value;
  return value != null && (type == "object" || type == "function");
}
var isObject_default = isObject2;

// node_modules/lodash-es/isFunction.js
var asyncTag = "[object AsyncFunction]";
var funcTag = "[object Function]";
var genTag = "[object GeneratorFunction]";
var proxyTag = "[object Proxy]";
function isFunction2(value) {
  if (!isObject_default(value)) {
    return false;
  }
  var tag = baseGetTag_default(value);
  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
}
var isFunction_default = isFunction2;

// node_modules/lodash-es/_coreJsData.js
var coreJsData = root_default["__core-js_shared__"];
var coreJsData_default = coreJsData;

// node_modules/lodash-es/_isMasked.js
var maskSrcKey = function() {
  var uid = /[^.]+$/.exec(coreJsData_default && coreJsData_default.keys && coreJsData_default.keys.IE_PROTO || "");
  return uid ? "Symbol(src)_1." + uid : "";
}();
function isMasked(func) {
  return !!maskSrcKey && maskSrcKey in func;
}
var isMasked_default = isMasked;

// node_modules/lodash-es/_toSource.js
var funcProto = Function.prototype;
var funcToString = funcProto.toString;
function toSource(func) {
  if (func != null) {
    try {
      return funcToString.call(func);
    } catch (e) {
    }
    try {
      return func + "";
    } catch (e) {
    }
  }
  return "";
}
var toSource_default = toSource;

// node_modules/lodash-es/_baseIsNative.js
var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
var reIsHostCtor = /^\[object .+?Constructor\]$/;
var funcProto2 = Function.prototype;
var objectProto3 = Object.prototype;
var funcToString2 = funcProto2.toString;
var hasOwnProperty2 = objectProto3.hasOwnProperty;
var reIsNative = RegExp(
  "^" + funcToString2.call(hasOwnProperty2).replace(reRegExpChar, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
);
function baseIsNative(value) {
  if (!isObject_default(value) || isMasked_default(value)) {
    return false;
  }
  var pattern = isFunction_default(value) ? reIsNative : reIsHostCtor;
  return pattern.test(toSource_default(value));
}
var baseIsNative_default = baseIsNative;

// node_modules/lodash-es/_getValue.js
function getValue(object, key) {
  return object == null ? void 0 : object[key];
}
var getValue_default = getValue;

// node_modules/lodash-es/_getNative.js
function getNative(object, key) {
  var value = getValue_default(object, key);
  return baseIsNative_default(value) ? value : void 0;
}
var getNative_default = getNative;

// node_modules/lodash-es/_Map.js
var Map2 = getNative_default(root_default, "Map");
var Map_default = Map2;

// node_modules/lodash-es/_nativeCreate.js
var nativeCreate = getNative_default(Object, "create");
var nativeCreate_default = nativeCreate;

// node_modules/lodash-es/_hashClear.js
function hashClear() {
  this.__data__ = nativeCreate_default ? nativeCreate_default(null) : {};
  this.size = 0;
}
var hashClear_default = hashClear;

// node_modules/lodash-es/_hashDelete.js
function hashDelete(key) {
  var result = this.has(key) && delete this.__data__[key];
  this.size -= result ? 1 : 0;
  return result;
}
var hashDelete_default = hashDelete;

// node_modules/lodash-es/_hashGet.js
var HASH_UNDEFINED = "__lodash_hash_undefined__";
var objectProto4 = Object.prototype;
var hasOwnProperty3 = objectProto4.hasOwnProperty;
function hashGet(key) {
  var data = this.__data__;
  if (nativeCreate_default) {
    var result = data[key];
    return result === HASH_UNDEFINED ? void 0 : result;
  }
  return hasOwnProperty3.call(data, key) ? data[key] : void 0;
}
var hashGet_default = hashGet;

// node_modules/lodash-es/_hashHas.js
var objectProto5 = Object.prototype;
var hasOwnProperty4 = objectProto5.hasOwnProperty;
function hashHas(key) {
  var data = this.__data__;
  return nativeCreate_default ? data[key] !== void 0 : hasOwnProperty4.call(data, key);
}
var hashHas_default = hashHas;

// node_modules/lodash-es/_hashSet.js
var HASH_UNDEFINED2 = "__lodash_hash_undefined__";
function hashSet(key, value) {
  var data = this.__data__;
  this.size += this.has(key) ? 0 : 1;
  data[key] = nativeCreate_default && value === void 0 ? HASH_UNDEFINED2 : value;
  return this;
}
var hashSet_default = hashSet;

// node_modules/lodash-es/_Hash.js
function Hash(entries) {
  var index2 = -1, length = entries == null ? 0 : entries.length;
  this.clear();
  while (++index2 < length) {
    var entry = entries[index2];
    this.set(entry[0], entry[1]);
  }
}
Hash.prototype.clear = hashClear_default;
Hash.prototype["delete"] = hashDelete_default;
Hash.prototype.get = hashGet_default;
Hash.prototype.has = hashHas_default;
Hash.prototype.set = hashSet_default;
var Hash_default = Hash;

// node_modules/lodash-es/_mapCacheClear.js
function mapCacheClear() {
  this.size = 0;
  this.__data__ = {
    "hash": new Hash_default(),
    "map": new (Map_default || ListCache_default)(),
    "string": new Hash_default()
  };
}
var mapCacheClear_default = mapCacheClear;

// node_modules/lodash-es/_isKeyable.js
function isKeyable(value) {
  var type = typeof value;
  return type == "string" || type == "number" || type == "symbol" || type == "boolean" ? value !== "__proto__" : value === null;
}
var isKeyable_default = isKeyable;

// node_modules/lodash-es/_getMapData.js
function getMapData(map, key) {
  var data = map.__data__;
  return isKeyable_default(key) ? data[typeof key == "string" ? "string" : "hash"] : data.map;
}
var getMapData_default = getMapData;

// node_modules/lodash-es/_mapCacheDelete.js
function mapCacheDelete(key) {
  var result = getMapData_default(this, key)["delete"](key);
  this.size -= result ? 1 : 0;
  return result;
}
var mapCacheDelete_default = mapCacheDelete;

// node_modules/lodash-es/_mapCacheGet.js
function mapCacheGet(key) {
  return getMapData_default(this, key).get(key);
}
var mapCacheGet_default = mapCacheGet;

// node_modules/lodash-es/_mapCacheHas.js
function mapCacheHas(key) {
  return getMapData_default(this, key).has(key);
}
var mapCacheHas_default = mapCacheHas;

// node_modules/lodash-es/_mapCacheSet.js
function mapCacheSet(key, value) {
  var data = getMapData_default(this, key), size = data.size;
  data.set(key, value);
  this.size += data.size == size ? 0 : 1;
  return this;
}
var mapCacheSet_default = mapCacheSet;

// node_modules/lodash-es/_MapCache.js
function MapCache(entries) {
  var index2 = -1, length = entries == null ? 0 : entries.length;
  this.clear();
  while (++index2 < length) {
    var entry = entries[index2];
    this.set(entry[0], entry[1]);
  }
}
MapCache.prototype.clear = mapCacheClear_default;
MapCache.prototype["delete"] = mapCacheDelete_default;
MapCache.prototype.get = mapCacheGet_default;
MapCache.prototype.has = mapCacheHas_default;
MapCache.prototype.set = mapCacheSet_default;
var MapCache_default = MapCache;

// node_modules/lodash-es/_stackSet.js
var LARGE_ARRAY_SIZE = 200;
function stackSet(key, value) {
  var data = this.__data__;
  if (data instanceof ListCache_default) {
    var pairs = data.__data__;
    if (!Map_default || pairs.length < LARGE_ARRAY_SIZE - 1) {
      pairs.push([key, value]);
      this.size = ++data.size;
      return this;
    }
    data = this.__data__ = new MapCache_default(pairs);
  }
  data.set(key, value);
  this.size = data.size;
  return this;
}
var stackSet_default = stackSet;

// node_modules/lodash-es/_Stack.js
function Stack(entries) {
  var data = this.__data__ = new ListCache_default(entries);
  this.size = data.size;
}
Stack.prototype.clear = stackClear_default;
Stack.prototype["delete"] = stackDelete_default;
Stack.prototype.get = stackGet_default;
Stack.prototype.has = stackHas_default;
Stack.prototype.set = stackSet_default;
var Stack_default = Stack;

// node_modules/lodash-es/_defineProperty.js
var defineProperty = function() {
  try {
    var func = getNative_default(Object, "defineProperty");
    func({}, "", {});
    return func;
  } catch (e) {
  }
}();
var defineProperty_default = defineProperty;

// node_modules/lodash-es/_baseAssignValue.js
function baseAssignValue(object, key, value) {
  if (key == "__proto__" && defineProperty_default) {
    defineProperty_default(object, key, {
      "configurable": true,
      "enumerable": true,
      "value": value,
      "writable": true
    });
  } else {
    object[key] = value;
  }
}
var baseAssignValue_default = baseAssignValue;

// node_modules/lodash-es/_assignMergeValue.js
function assignMergeValue(object, key, value) {
  if (value !== void 0 && !eq_default(object[key], value) || value === void 0 && !(key in object)) {
    baseAssignValue_default(object, key, value);
  }
}
var assignMergeValue_default = assignMergeValue;

// node_modules/lodash-es/_createBaseFor.js
function createBaseFor(fromRight) {
  return function(object, iteratee, keysFunc) {
    var index2 = -1, iterable = Object(object), props = keysFunc(object), length = props.length;
    while (length--) {
      var key = props[fromRight ? length : ++index2];
      if (iteratee(iterable[key], key, iterable) === false) {
        break;
      }
    }
    return object;
  };
}
var createBaseFor_default = createBaseFor;

// node_modules/lodash-es/_baseFor.js
var baseFor = createBaseFor_default();
var baseFor_default = baseFor;

// node_modules/lodash-es/_cloneBuffer.js
var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
var moduleExports = freeModule && freeModule.exports === freeExports;
var Buffer = moduleExports ? root_default.Buffer : void 0;
var allocUnsafe = Buffer ? Buffer.allocUnsafe : void 0;
function cloneBuffer(buffer, isDeep) {
  if (isDeep) {
    return buffer.slice();
  }
  var length = buffer.length, result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);
  buffer.copy(result);
  return result;
}
var cloneBuffer_default = cloneBuffer;

// node_modules/lodash-es/_Uint8Array.js
var Uint8Array2 = root_default.Uint8Array;
var Uint8Array_default = Uint8Array2;

// node_modules/lodash-es/_cloneArrayBuffer.js
function cloneArrayBuffer(arrayBuffer) {
  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
  new Uint8Array_default(result).set(new Uint8Array_default(arrayBuffer));
  return result;
}
var cloneArrayBuffer_default = cloneArrayBuffer;

// node_modules/lodash-es/_cloneTypedArray.js
function cloneTypedArray(typedArray, isDeep) {
  var buffer = isDeep ? cloneArrayBuffer_default(typedArray.buffer) : typedArray.buffer;
  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
}
var cloneTypedArray_default = cloneTypedArray;

// node_modules/lodash-es/_copyArray.js
function copyArray(source, array) {
  var index2 = -1, length = source.length;
  array || (array = Array(length));
  while (++index2 < length) {
    array[index2] = source[index2];
  }
  return array;
}
var copyArray_default = copyArray;

// node_modules/lodash-es/_baseCreate.js
var objectCreate = Object.create;
var baseCreate = function() {
  function object() {
  }
  return function(proto) {
    if (!isObject_default(proto)) {
      return {};
    }
    if (objectCreate) {
      return objectCreate(proto);
    }
    object.prototype = proto;
    var result = new object();
    object.prototype = void 0;
    return result;
  };
}();
var baseCreate_default = baseCreate;

// node_modules/lodash-es/_overArg.js
function overArg(func, transform) {
  return function(arg) {
    return func(transform(arg));
  };
}
var overArg_default = overArg;

// node_modules/lodash-es/_getPrototype.js
var getPrototype = overArg_default(Object.getPrototypeOf, Object);
var getPrototype_default = getPrototype;

// node_modules/lodash-es/_isPrototype.js
var objectProto6 = Object.prototype;
function isPrototype(value) {
  var Ctor = value && value.constructor, proto = typeof Ctor == "function" && Ctor.prototype || objectProto6;
  return value === proto;
}
var isPrototype_default = isPrototype;

// node_modules/lodash-es/_initCloneObject.js
function initCloneObject(object) {
  return typeof object.constructor == "function" && !isPrototype_default(object) ? baseCreate_default(getPrototype_default(object)) : {};
}
var initCloneObject_default = initCloneObject;

// node_modules/lodash-es/isObjectLike.js
function isObjectLike(value) {
  return value != null && typeof value == "object";
}
var isObjectLike_default = isObjectLike;

// node_modules/lodash-es/_baseIsArguments.js
var argsTag = "[object Arguments]";
function baseIsArguments(value) {
  return isObjectLike_default(value) && baseGetTag_default(value) == argsTag;
}
var baseIsArguments_default = baseIsArguments;

// node_modules/lodash-es/isArguments.js
var objectProto7 = Object.prototype;
var hasOwnProperty5 = objectProto7.hasOwnProperty;
var propertyIsEnumerable = objectProto7.propertyIsEnumerable;
var isArguments = baseIsArguments_default(function() {
  return arguments;
}()) ? baseIsArguments_default : function(value) {
  return isObjectLike_default(value) && hasOwnProperty5.call(value, "callee") && !propertyIsEnumerable.call(value, "callee");
};
var isArguments_default = isArguments;

// node_modules/lodash-es/isArray.js
var isArray = Array.isArray;
var isArray_default = isArray;

// node_modules/lodash-es/isLength.js
var MAX_SAFE_INTEGER = 9007199254740991;
function isLength(value) {
  return typeof value == "number" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
}
var isLength_default = isLength;

// node_modules/lodash-es/isArrayLike.js
function isArrayLike(value) {
  return value != null && isLength_default(value.length) && !isFunction_default(value);
}
var isArrayLike_default = isArrayLike;

// node_modules/lodash-es/isArrayLikeObject.js
function isArrayLikeObject(value) {
  return isObjectLike_default(value) && isArrayLike_default(value);
}
var isArrayLikeObject_default = isArrayLikeObject;

// node_modules/lodash-es/stubFalse.js
function stubFalse() {
  return false;
}
var stubFalse_default = stubFalse;

// node_modules/lodash-es/isBuffer.js
var freeExports2 = typeof exports == "object" && exports && !exports.nodeType && exports;
var freeModule2 = freeExports2 && typeof module == "object" && module && !module.nodeType && module;
var moduleExports2 = freeModule2 && freeModule2.exports === freeExports2;
var Buffer2 = moduleExports2 ? root_default.Buffer : void 0;
var nativeIsBuffer = Buffer2 ? Buffer2.isBuffer : void 0;
var isBuffer = nativeIsBuffer || stubFalse_default;
var isBuffer_default = isBuffer;

// node_modules/lodash-es/isPlainObject.js
var objectTag = "[object Object]";
var funcProto3 = Function.prototype;
var objectProto8 = Object.prototype;
var funcToString3 = funcProto3.toString;
var hasOwnProperty6 = objectProto8.hasOwnProperty;
var objectCtorString = funcToString3.call(Object);
function isPlainObject2(value) {
  if (!isObjectLike_default(value) || baseGetTag_default(value) != objectTag) {
    return false;
  }
  var proto = getPrototype_default(value);
  if (proto === null) {
    return true;
  }
  var Ctor = hasOwnProperty6.call(proto, "constructor") && proto.constructor;
  return typeof Ctor == "function" && Ctor instanceof Ctor && funcToString3.call(Ctor) == objectCtorString;
}
var isPlainObject_default = isPlainObject2;

// node_modules/lodash-es/_baseIsTypedArray.js
var argsTag2 = "[object Arguments]";
var arrayTag = "[object Array]";
var boolTag = "[object Boolean]";
var dateTag = "[object Date]";
var errorTag = "[object Error]";
var funcTag2 = "[object Function]";
var mapTag = "[object Map]";
var numberTag = "[object Number]";
var objectTag2 = "[object Object]";
var regexpTag = "[object RegExp]";
var setTag = "[object Set]";
var stringTag = "[object String]";
var weakMapTag = "[object WeakMap]";
var arrayBufferTag = "[object ArrayBuffer]";
var dataViewTag = "[object DataView]";
var float32Tag = "[object Float32Array]";
var float64Tag = "[object Float64Array]";
var int8Tag = "[object Int8Array]";
var int16Tag = "[object Int16Array]";
var int32Tag = "[object Int32Array]";
var uint8Tag = "[object Uint8Array]";
var uint8ClampedTag = "[object Uint8ClampedArray]";
var uint16Tag = "[object Uint16Array]";
var uint32Tag = "[object Uint32Array]";
var typedArrayTags = {};
typedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;
typedArrayTags[argsTag2] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag2] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag2] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;
function baseIsTypedArray(value) {
  return isObjectLike_default(value) && isLength_default(value.length) && !!typedArrayTags[baseGetTag_default(value)];
}
var baseIsTypedArray_default = baseIsTypedArray;

// node_modules/lodash-es/_baseUnary.js
function baseUnary(func) {
  return function(value) {
    return func(value);
  };
}
var baseUnary_default = baseUnary;

// node_modules/lodash-es/_nodeUtil.js
var freeExports3 = typeof exports == "object" && exports && !exports.nodeType && exports;
var freeModule3 = freeExports3 && typeof module == "object" && module && !module.nodeType && module;
var moduleExports3 = freeModule3 && freeModule3.exports === freeExports3;
var freeProcess = moduleExports3 && freeGlobal_default.process;
var nodeUtil = function() {
  try {
    var types = freeModule3 && freeModule3.require && freeModule3.require("util").types;
    if (types) {
      return types;
    }
    return freeProcess && freeProcess.binding && freeProcess.binding("util");
  } catch (e) {
  }
}();
var nodeUtil_default = nodeUtil;

// node_modules/lodash-es/isTypedArray.js
var nodeIsTypedArray = nodeUtil_default && nodeUtil_default.isTypedArray;
var isTypedArray = nodeIsTypedArray ? baseUnary_default(nodeIsTypedArray) : baseIsTypedArray_default;
var isTypedArray_default = isTypedArray;

// node_modules/lodash-es/_safeGet.js
function safeGet(object, key) {
  if (key === "constructor" && typeof object[key] === "function") {
    return;
  }
  if (key == "__proto__") {
    return;
  }
  return object[key];
}
var safeGet_default = safeGet;

// node_modules/lodash-es/_assignValue.js
var objectProto9 = Object.prototype;
var hasOwnProperty7 = objectProto9.hasOwnProperty;
function assignValue(object, key, value) {
  var objValue = object[key];
  if (!(hasOwnProperty7.call(object, key) && eq_default(objValue, value)) || value === void 0 && !(key in object)) {
    baseAssignValue_default(object, key, value);
  }
}
var assignValue_default = assignValue;

// node_modules/lodash-es/_copyObject.js
function copyObject(source, props, object, customizer) {
  var isNew = !object;
  object || (object = {});
  var index2 = -1, length = props.length;
  while (++index2 < length) {
    var key = props[index2];
    var newValue = customizer ? customizer(object[key], source[key], key, object, source) : void 0;
    if (newValue === void 0) {
      newValue = source[key];
    }
    if (isNew) {
      baseAssignValue_default(object, key, newValue);
    } else {
      assignValue_default(object, key, newValue);
    }
  }
  return object;
}
var copyObject_default = copyObject;

// node_modules/lodash-es/_baseTimes.js
function baseTimes(n, iteratee) {
  var index2 = -1, result = Array(n);
  while (++index2 < n) {
    result[index2] = iteratee(index2);
  }
  return result;
}
var baseTimes_default = baseTimes;

// node_modules/lodash-es/_isIndex.js
var MAX_SAFE_INTEGER2 = 9007199254740991;
var reIsUint = /^(?:0|[1-9]\d*)$/;
function isIndex(value, length) {
  var type = typeof value;
  length = length == null ? MAX_SAFE_INTEGER2 : length;
  return !!length && (type == "number" || type != "symbol" && reIsUint.test(value)) && (value > -1 && value % 1 == 0 && value < length);
}
var isIndex_default = isIndex;

// node_modules/lodash-es/_arrayLikeKeys.js
var objectProto10 = Object.prototype;
var hasOwnProperty8 = objectProto10.hasOwnProperty;
function arrayLikeKeys(value, inherited) {
  var isArr = isArray_default(value), isArg = !isArr && isArguments_default(value), isBuff = !isArr && !isArg && isBuffer_default(value), isType = !isArr && !isArg && !isBuff && isTypedArray_default(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes_default(value.length, String) : [], length = result.length;
  for (var key in value) {
    if ((inherited || hasOwnProperty8.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.
    (key == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
    isBuff && (key == "offset" || key == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
    isType && (key == "buffer" || key == "byteLength" || key == "byteOffset") || // Skip index properties.
    isIndex_default(key, length)))) {
      result.push(key);
    }
  }
  return result;
}
var arrayLikeKeys_default = arrayLikeKeys;

// node_modules/lodash-es/_nativeKeysIn.js
function nativeKeysIn(object) {
  var result = [];
  if (object != null) {
    for (var key in Object(object)) {
      result.push(key);
    }
  }
  return result;
}
var nativeKeysIn_default = nativeKeysIn;

// node_modules/lodash-es/_baseKeysIn.js
var objectProto11 = Object.prototype;
var hasOwnProperty9 = objectProto11.hasOwnProperty;
function baseKeysIn(object) {
  if (!isObject_default(object)) {
    return nativeKeysIn_default(object);
  }
  var isProto = isPrototype_default(object), result = [];
  for (var key in object) {
    if (!(key == "constructor" && (isProto || !hasOwnProperty9.call(object, key)))) {
      result.push(key);
    }
  }
  return result;
}
var baseKeysIn_default = baseKeysIn;

// node_modules/lodash-es/keysIn.js
function keysIn(object) {
  return isArrayLike_default(object) ? arrayLikeKeys_default(object, true) : baseKeysIn_default(object);
}
var keysIn_default = keysIn;

// node_modules/lodash-es/toPlainObject.js
function toPlainObject(value) {
  return copyObject_default(value, keysIn_default(value));
}
var toPlainObject_default = toPlainObject;

// node_modules/lodash-es/_baseMergeDeep.js
function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {
  var objValue = safeGet_default(object, key), srcValue = safeGet_default(source, key), stacked = stack.get(srcValue);
  if (stacked) {
    assignMergeValue_default(object, key, stacked);
    return;
  }
  var newValue = customizer ? customizer(objValue, srcValue, key + "", object, source, stack) : void 0;
  var isCommon = newValue === void 0;
  if (isCommon) {
    var isArr = isArray_default(srcValue), isBuff = !isArr && isBuffer_default(srcValue), isTyped = !isArr && !isBuff && isTypedArray_default(srcValue);
    newValue = srcValue;
    if (isArr || isBuff || isTyped) {
      if (isArray_default(objValue)) {
        newValue = objValue;
      } else if (isArrayLikeObject_default(objValue)) {
        newValue = copyArray_default(objValue);
      } else if (isBuff) {
        isCommon = false;
        newValue = cloneBuffer_default(srcValue, true);
      } else if (isTyped) {
        isCommon = false;
        newValue = cloneTypedArray_default(srcValue, true);
      } else {
        newValue = [];
      }
    } else if (isPlainObject_default(srcValue) || isArguments_default(srcValue)) {
      newValue = objValue;
      if (isArguments_default(objValue)) {
        newValue = toPlainObject_default(objValue);
      } else if (!isObject_default(objValue) || isFunction_default(objValue)) {
        newValue = initCloneObject_default(srcValue);
      }
    } else {
      isCommon = false;
    }
  }
  if (isCommon) {
    stack.set(srcValue, newValue);
    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);
    stack["delete"](srcValue);
  }
  assignMergeValue_default(object, key, newValue);
}
var baseMergeDeep_default = baseMergeDeep;

// node_modules/lodash-es/_baseMerge.js
function baseMerge(object, source, srcIndex, customizer, stack) {
  if (object === source) {
    return;
  }
  baseFor_default(source, function(srcValue, key) {
    stack || (stack = new Stack_default());
    if (isObject_default(srcValue)) {
      baseMergeDeep_default(object, source, key, srcIndex, baseMerge, customizer, stack);
    } else {
      var newValue = customizer ? customizer(safeGet_default(object, key), srcValue, key + "", object, source, stack) : void 0;
      if (newValue === void 0) {
        newValue = srcValue;
      }
      assignMergeValue_default(object, key, newValue);
    }
  }, keysIn_default);
}
var baseMerge_default = baseMerge;

// node_modules/lodash-es/identity.js
function identity(value) {
  return value;
}
var identity_default = identity;

// node_modules/lodash-es/_apply.js
function apply(func, thisArg, args) {
  switch (args.length) {
    case 0:
      return func.call(thisArg);
    case 1:
      return func.call(thisArg, args[0]);
    case 2:
      return func.call(thisArg, args[0], args[1]);
    case 3:
      return func.call(thisArg, args[0], args[1], args[2]);
  }
  return func.apply(thisArg, args);
}
var apply_default = apply;

// node_modules/lodash-es/_overRest.js
var nativeMax = Math.max;
function overRest(func, start, transform) {
  start = nativeMax(start === void 0 ? func.length - 1 : start, 0);
  return function() {
    var args = arguments, index2 = -1, length = nativeMax(args.length - start, 0), array = Array(length);
    while (++index2 < length) {
      array[index2] = args[start + index2];
    }
    index2 = -1;
    var otherArgs = Array(start + 1);
    while (++index2 < start) {
      otherArgs[index2] = args[index2];
    }
    otherArgs[start] = transform(array);
    return apply_default(func, this, otherArgs);
  };
}
var overRest_default = overRest;

// node_modules/lodash-es/constant.js
function constant(value) {
  return function() {
    return value;
  };
}
var constant_default = constant;

// node_modules/lodash-es/_baseSetToString.js
var baseSetToString = !defineProperty_default ? identity_default : function(func, string) {
  return defineProperty_default(func, "toString", {
    "configurable": true,
    "enumerable": false,
    "value": constant_default(string),
    "writable": true
  });
};
var baseSetToString_default = baseSetToString;

// node_modules/lodash-es/_shortOut.js
var HOT_COUNT = 800;
var HOT_SPAN = 16;
var nativeNow = Date.now;
function shortOut(func) {
  var count = 0, lastCalled = 0;
  return function() {
    var stamp = nativeNow(), remaining = HOT_SPAN - (stamp - lastCalled);
    lastCalled = stamp;
    if (remaining > 0) {
      if (++count >= HOT_COUNT) {
        return arguments[0];
      }
    } else {
      count = 0;
    }
    return func.apply(void 0, arguments);
  };
}
var shortOut_default = shortOut;

// node_modules/lodash-es/_setToString.js
var setToString = shortOut_default(baseSetToString_default);
var setToString_default = setToString;

// node_modules/lodash-es/_baseRest.js
function baseRest(func, start) {
  return setToString_default(overRest_default(func, start, identity_default), func + "");
}
var baseRest_default = baseRest;

// node_modules/lodash-es/_isIterateeCall.js
function isIterateeCall(value, index2, object) {
  if (!isObject_default(object)) {
    return false;
  }
  var type = typeof index2;
  if (type == "number" ? isArrayLike_default(object) && isIndex_default(index2, object.length) : type == "string" && index2 in object) {
    return eq_default(object[index2], value);
  }
  return false;
}
var isIterateeCall_default = isIterateeCall;

// node_modules/lodash-es/_createAssigner.js
function createAssigner(assigner) {
  return baseRest_default(function(object, sources) {
    var index2 = -1, length = sources.length, customizer = length > 1 ? sources[length - 1] : void 0, guard = length > 2 ? sources[2] : void 0;
    customizer = assigner.length > 3 && typeof customizer == "function" ? (length--, customizer) : void 0;
    if (guard && isIterateeCall_default(sources[0], sources[1], guard)) {
      customizer = length < 3 ? void 0 : customizer;
      length = 1;
    }
    object = Object(object);
    while (++index2 < length) {
      var source = sources[index2];
      if (source) {
        assigner(object, source, index2, customizer);
      }
    }
    return object;
  });
}
var createAssigner_default = createAssigner;

// node_modules/lodash-es/merge.js
var merge5 = createAssigner_default(function(object, source, srcIndex) {
  baseMerge_default(object, source, srcIndex);
});
var merge_default = merge5;

// node_modules/@ant-design/pro-utils/es/transformKeySubmitValue/index.js
var import_react26 = __toESM(require_react());
function isPlainObj(itemValue) {
  if (_typeof(itemValue) !== "object")
    return false;
  if (itemValue === null)
    return true;
  if (import_react26.default.isValidElement(itemValue))
    return false;
  if (itemValue.constructor === RegExp)
    return false;
  if (itemValue instanceof Map)
    return false;
  if (itemValue instanceof Set)
    return false;
  if (itemValue instanceof HTMLElement)
    return false;
  if (itemValue instanceof Blob)
    return false;
  if (itemValue instanceof File)
    return false;
  if (Array.isArray(itemValue))
    return false;
  return true;
}
var transformKeySubmitValue = function transformKeySubmitValue2(values, dataFormatMapRaw) {
  var omit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
  var dataFormatMap = Object.keys(dataFormatMapRaw).reduce(function(ret, key) {
    var value = dataFormatMapRaw[key];
    if (!isNil(value)) {
      ret[key] = value;
    }
    return ret;
  }, {});
  if (Object.keys(dataFormatMap).length < 1) {
    return values;
  }
  if (typeof window === "undefined")
    return values;
  if (_typeof(values) !== "object" || isNil(values) || values instanceof Blob) {
    return values;
  }
  var finalValues = Array.isArray(values) ? [] : {};
  var gen = function gen2(tempValues, parentsKey) {
    var isArrayValues = Array.isArray(tempValues);
    var result = isArrayValues ? [] : {};
    if (tempValues == null || tempValues === void 0) {
      return result;
    }
    Object.keys(tempValues).forEach(function(entityKey) {
      var transformForArray = function transformForArray2(transformList, subItemValue) {
        if (!Array.isArray(transformList))
          return entityKey;
        transformList.forEach(function(transform2, idx) {
          if (!transform2)
            return;
          var subTransformItem = subItemValue === null || subItemValue === void 0 ? void 0 : subItemValue[idx];
          if (typeof transform2 === "function") {
            subItemValue[idx] = transform2(subItemValue, entityKey, tempValues);
          }
          if (_typeof(transform2) === "object" && !Array.isArray(transform2)) {
            Object.keys(transform2).forEach(function(transformArrayItem) {
              var subTransformItemValue = subTransformItem === null || subTransformItem === void 0 ? void 0 : subTransformItem[transformArrayItem];
              if (typeof transform2[transformArrayItem] === "function" && subTransformItemValue) {
                var res = transform2[transformArrayItem](subTransformItem[transformArrayItem], entityKey, tempValues);
                subTransformItem[transformArrayItem] = _typeof(res) === "object" ? res[transformArrayItem] : res;
              } else if (_typeof(transform2[transformArrayItem]) === "object" && Array.isArray(transform2[transformArrayItem]) && subTransformItemValue) {
                transformForArray2(transform2[transformArrayItem], subTransformItemValue);
              }
            });
          }
          if (_typeof(transform2) === "object" && Array.isArray(transform2) && subTransformItem) {
            transformForArray2(transform2, subTransformItem);
          }
        });
        return entityKey;
      };
      var key = parentsKey ? [parentsKey, entityKey].flat(1) : [entityKey].flat(1);
      var itemValue = tempValues[entityKey];
      var transformFunction = get(dataFormatMap, key);
      var transform = function transform2() {
        var tempKey, transformedResult, isTransformedResultPrimitive = false;
        if (typeof transformFunction === "function") {
          transformedResult = transformFunction === null || transformFunction === void 0 ? void 0 : transformFunction(itemValue, entityKey, tempValues);
          var typeOfResult = _typeof(transformedResult);
          if (typeOfResult !== "object" && typeOfResult !== "undefined") {
            tempKey = entityKey;
            isTransformedResultPrimitive = true;
          } else {
            tempKey = transformedResult;
          }
        } else {
          tempKey = transformForArray(transformFunction, itemValue);
        }
        if (Array.isArray(tempKey)) {
          result = set(result, tempKey, itemValue);
          return;
        }
        if (_typeof(tempKey) === "object" && !Array.isArray(finalValues)) {
          finalValues = merge_default(finalValues, tempKey);
        } else if (_typeof(tempKey) === "object" && Array.isArray(finalValues)) {
          result = _objectSpread2(_objectSpread2({}, result), tempKey);
        } else if (tempKey !== null || tempKey !== void 0) {
          result = set(result, [tempKey], isTransformedResultPrimitive ? transformedResult : itemValue);
        }
      };
      if (transformFunction && typeof transformFunction === "function") {
        transform();
      }
      if (typeof window === "undefined")
        return;
      if (isPlainObj(itemValue)) {
        var genValues = gen2(itemValue, key);
        if (Object.keys(genValues).length < 1) {
          return;
        }
        result = set(result, [entityKey], genValues);
        return;
      }
      transform();
    });
    return omit ? result : tempValues;
  };
  finalValues = Array.isArray(values) && Array.isArray(finalValues) ? _toConsumableArray(gen(values)) : merge3({}, gen(values), finalValues);
  return finalValues;
};

// node_modules/@ant-design/pro-utils/es/compatible/compatibleBorder.js
var compatibleBorder = function compatibleBorder2(bordered) {
  if (bordered === void 0) {
    return {};
  }
  return compareVersions(version_default, "5.13.0") <= 0 ? {
    bordered
  } : {
    variant: bordered ? void 0 : "borderless"
  };
};

// node_modules/@ant-design/pro-utils/es/useEditableArray/index.js
init_defineProperty();
init_typeof();
var import_react30 = __toESM(require_react());

// node_modules/@ant-design/pro-utils/es/useEditableMap/index.js
var import_react27 = __toESM(require_react());
var warning2 = function warning3(messageStr) {
  return (message_default.warn || message_default.warning)(messageStr);
};
function editableRowByKey(_ref) {
  var data = _ref.data, row = _ref.row;
  return _objectSpread2(_objectSpread2({}, data), row);
}
function useEditableMap(props) {
  var preEditRowRef = (0, import_react27.useRef)(null);
  var editableType = props.type || "single";
  var intl = useIntl();
  var _useMergedState = useMergedState([], {
    value: props.editableKeys,
    onChange: props.onChange ? function(keys) {
      var _props$onChange;
      props === null || props === void 0 || (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(
        props,
        // 计算编辑的key
        keys,
        props.dataSource
      );
    } : void 0
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), editableKeys = _useMergedState2[0], setEditableRowKeys = _useMergedState2[1];
  var editableKeysSet = (0, import_react27.useMemo)(function() {
    var keys = editableType === "single" ? editableKeys === null || editableKeys === void 0 ? void 0 : editableKeys.slice(0, 1) : editableKeys;
    return new Set(keys);
  }, [(editableKeys || []).join(","), editableType]);
  var isEditable = (0, import_react27.useCallback)(function(recordKey) {
    if (editableKeys !== null && editableKeys !== void 0 && editableKeys.includes(recordKeyToString(recordKey)))
      return true;
    return false;
  }, [(editableKeys || []).join(",")]);
  var startEditable = function startEditable2(recordKey, recordValue) {
    var _ref2;
    if (editableKeysSet.size > 0 && editableType === "single") {
      warning2(props.onlyOneLineEditorAlertMessage || intl.getMessage("editableTable.onlyOneLineEditor", "只能同时编辑一行"));
      return false;
    }
    preEditRowRef.current = (_ref2 = recordValue !== null && recordValue !== void 0 ? recordValue : get(props.dataSource, Array.isArray(recordKey) ? recordKey : [recordKey])) !== null && _ref2 !== void 0 ? _ref2 : null;
    editableKeysSet.add(recordKeyToString(recordKey));
    setEditableRowKeys(Array.from(editableKeysSet));
    return true;
  };
  var cancelEditable = function cancelEditable2(recordKey) {
    editableKeysSet.delete(recordKeyToString(recordKey));
    setEditableRowKeys(Array.from(editableKeysSet));
    return true;
  };
  var onCancel = function() {
    var _ref3 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(recordKey, editRow, originRow, newLine) {
      var _props$onCancel;
      var success2;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return props === null || props === void 0 || (_props$onCancel = props.onCancel) === null || _props$onCancel === void 0 ? void 0 : _props$onCancel.call(props, recordKey, editRow, originRow, newLine);
            case 2:
              success2 = _context.sent;
              if (!(success2 === false)) {
                _context.next = 5;
                break;
              }
              return _context.abrupt("return", false);
            case 5:
              return _context.abrupt("return", true);
            case 6:
            case "end":
              return _context.stop();
          }
      }, _callee);
    }));
    return function onCancel2(_x, _x2, _x3, _x4) {
      return _ref3.apply(this, arguments);
    };
  }();
  var onSave = function() {
    var _ref4 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee2(recordKey, editRow, originRow) {
      var _props$onSave;
      var success2, actionProps;
      return _regeneratorRuntime().wrap(function _callee2$(_context2) {
        while (1)
          switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return props === null || props === void 0 || (_props$onSave = props.onSave) === null || _props$onSave === void 0 ? void 0 : _props$onSave.call(props, recordKey, editRow, originRow);
            case 2:
              success2 = _context2.sent;
              if (!(success2 === false)) {
                _context2.next = 5;
                break;
              }
              return _context2.abrupt("return", false);
            case 5:
              _context2.next = 7;
              return cancelEditable(recordKey);
            case 7:
              actionProps = {
                data: props.dataSource,
                row: editRow,
                key: recordKey,
                childrenColumnName: props.childrenColumnName || "children"
              };
              props.setDataSource(editableRowByKey(actionProps));
              return _context2.abrupt("return", true);
            case 10:
            case "end":
              return _context2.stop();
          }
      }, _callee2);
    }));
    return function onSave2(_x5, _x6, _x7) {
      return _ref4.apply(this, arguments);
    };
  }();
  var saveText = intl.getMessage("editableTable.action.save", "保存");
  var deleteText = intl.getMessage("editableTable.action.delete", "删除");
  var cancelText = intl.getMessage("editableTable.action.cancel", "取消");
  var actionRender = (0, import_react27.useCallback)(function(key, config) {
    var renderConfig = _objectSpread2({
      recordKey: key,
      cancelEditable,
      onCancel,
      onSave,
      editableKeys,
      setEditableRowKeys,
      saveText,
      cancelText,
      preEditRowRef,
      deleteText,
      deletePopconfirmMessage: "".concat(intl.getMessage("deleteThisLine", "删除此项"), "?"),
      editorType: "Map"
    }, config);
    var renderResult = defaultActionRender(props.dataSource, renderConfig);
    if (props.actionRender) {
      return props.actionRender(props.dataSource, renderConfig, {
        save: renderResult.save,
        delete: renderResult.delete,
        cancel: renderResult.cancel
      });
    }
    return [renderResult.save, renderResult.delete, renderResult.cancel];
  }, [editableKeys && editableKeys.join(","), props.dataSource]);
  return {
    editableKeys,
    setEditableRowKeys,
    isEditable,
    actionRender,
    startEditable,
    cancelEditable
  };
}

// node_modules/@ant-design/pro-utils/es/useMediaQuery/index.js
var import_react29 = __toESM(require_react());

// node_modules/@ant-design/pro-utils/es/useMediaQuery/query.js
var import_react28 = __toESM(require_react());
function useMediaQuery(mediaQuery) {
  var isSsr = typeof window === "undefined";
  var _useState = (0, import_react28.useState)(function() {
    return isSsr ? false : window.matchMedia(mediaQuery).matches;
  }), _useState2 = _slicedToArray(_useState, 2), matches = _useState2[0], setMatches = _useState2[1];
  (0, import_react28.useLayoutEffect)(function() {
    if (isSsr) {
      return;
    }
    var mediaQueryList = window.matchMedia(mediaQuery);
    var listener = function listener2(e) {
      return setMatches(e.matches);
    };
    mediaQueryList.addListener(listener);
    return function() {
      return mediaQueryList.removeListener(listener);
    };
  }, [mediaQuery]);
  return matches;
}

// node_modules/@ant-design/pro-utils/es/useMediaQuery/index.js
var MediaQueryEnum = {
  xs: {
    maxWidth: 575,
    matchMedia: "(max-width: 575px)"
  },
  sm: {
    minWidth: 576,
    maxWidth: 767,
    matchMedia: "(min-width: 576px) and (max-width: 767px)"
  },
  md: {
    minWidth: 768,
    maxWidth: 991,
    matchMedia: "(min-width: 768px) and (max-width: 991px)"
  },
  lg: {
    minWidth: 992,
    maxWidth: 1199,
    matchMedia: "(min-width: 992px) and (max-width: 1199px)"
  },
  xl: {
    minWidth: 1200,
    maxWidth: 1599,
    matchMedia: "(min-width: 1200px) and (max-width: 1599px)"
  },
  xxl: {
    minWidth: 1600,
    matchMedia: "(min-width: 1600px)"
  }
};
var getScreenClassName = function getScreenClassName2() {
  var queryKey = void 0;
  if (typeof window === "undefined") {
    return queryKey;
  }
  var mediaQueryKey = Object.keys(MediaQueryEnum).find(function(key) {
    var matchMedia = MediaQueryEnum[key].matchMedia;
    if (window.matchMedia(matchMedia).matches) {
      return true;
    }
    return false;
  });
  queryKey = mediaQueryKey;
  return queryKey;
};
var useBreakpoint = function useBreakpoint2() {
  var isMd = useMediaQuery(MediaQueryEnum.md.matchMedia);
  var isLg = useMediaQuery(MediaQueryEnum.lg.matchMedia);
  var isXxl = useMediaQuery(MediaQueryEnum.xxl.matchMedia);
  var isXl = useMediaQuery(MediaQueryEnum.xl.matchMedia);
  var isSm = useMediaQuery(MediaQueryEnum.sm.matchMedia);
  var isXs = useMediaQuery(MediaQueryEnum.xs.matchMedia);
  var _useState = (0, import_react29.useState)(getScreenClassName()), _useState2 = _slicedToArray(_useState, 2), colSpan = _useState2[0], setColSpan = _useState2[1];
  (0, import_react29.useEffect)(function() {
    if (false) {
      setColSpan(process.env.USE_MEDIA || "md");
      return;
    }
    if (isXxl) {
      setColSpan("xxl");
      return;
    }
    if (isXl) {
      setColSpan("xl");
      return;
    }
    if (isLg) {
      setColSpan("lg");
      return;
    }
    if (isMd) {
      setColSpan("md");
      return;
    }
    if (isSm) {
      setColSpan("sm");
      return;
    }
    if (isXs) {
      setColSpan("xs");
      return;
    }
    setColSpan("md");
  }, [isMd, isLg, isXxl, isXl, isSm, isXs]);
  return colSpan;
};

// node_modules/@ant-design/pro-utils/es/useEditableArray/index.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime());
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
var _excluded4 = ["map_row_parentKey"];
var _excluded23 = ["map_row_parentKey", "map_row_key"];
var _excluded32 = ["map_row_key"];
var warning4 = function warning5(messageStr) {
  return (message_default.warn || message_default.warning)(messageStr);
};
var recordKeyToString = function recordKeyToString2(rowKey) {
  if (Array.isArray(rowKey))
    return rowKey.join(",");
  return rowKey;
};
function editableRowByKey2(keyProps, action) {
  var _recordKeyToString;
  var getRowKey = keyProps.getRowKey, row = keyProps.row, data = keyProps.data, _keyProps$childrenCol = keyProps.childrenColumnName, childrenColumnName = _keyProps$childrenCol === void 0 ? "children" : _keyProps$childrenCol;
  var key = (_recordKeyToString = recordKeyToString(keyProps.key)) === null || _recordKeyToString === void 0 ? void 0 : _recordKeyToString.toString();
  var kvMap = /* @__PURE__ */ new Map();
  function dig(records, map_row_parentKey, map_row_index) {
    records.forEach(function(record, index2) {
      var eachIndex = (map_row_index || 0) * 10 + index2;
      var recordKey = getRowKey(record, eachIndex).toString();
      if (record && _typeof(record) === "object" && childrenColumnName in record) {
        dig(record[childrenColumnName] || [], recordKey, eachIndex);
      }
      var newRecord = _objectSpread2(_objectSpread2({}, record), {}, {
        map_row_key: recordKey,
        children: void 0,
        map_row_parentKey
      });
      delete newRecord.children;
      if (!map_row_parentKey) {
        delete newRecord.map_row_parentKey;
      }
      kvMap.set(recordKey, newRecord);
    });
  }
  if (action === "top") {
    kvMap.set(key, _objectSpread2(_objectSpread2({}, kvMap.get(key)), row));
  }
  dig(data);
  if (action === "update") {
    kvMap.set(key, _objectSpread2(_objectSpread2({}, kvMap.get(key)), row));
  }
  if (action === "delete") {
    kvMap.delete(key);
  }
  var fill = function fill2(map) {
    var kvArrayMap = /* @__PURE__ */ new Map();
    var kvSource = [];
    var fillNewRecord = function fillNewRecord2() {
      var fillChildren = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
      map.forEach(function(value) {
        if (value.map_row_parentKey && !value.map_row_key) {
          var map_row_parentKey = value.map_row_parentKey, rest = _objectWithoutProperties(value, _excluded4);
          if (!kvArrayMap.has(map_row_parentKey)) {
            kvArrayMap.set(map_row_parentKey, []);
          }
          if (fillChildren) {
            var _kvArrayMap$get;
            (_kvArrayMap$get = kvArrayMap.get(map_row_parentKey)) === null || _kvArrayMap$get === void 0 || _kvArrayMap$get.push(rest);
          }
        }
      });
    };
    fillNewRecord(action === "top");
    map.forEach(function(value) {
      if (value.map_row_parentKey && value.map_row_key) {
        var _kvArrayMap$get2;
        var map_row_parentKey = value.map_row_parentKey, map_row_key = value.map_row_key, rest = _objectWithoutProperties(value, _excluded23);
        if (kvArrayMap.has(map_row_key)) {
          rest[childrenColumnName] = kvArrayMap.get(map_row_key);
        }
        if (!kvArrayMap.has(map_row_parentKey)) {
          kvArrayMap.set(map_row_parentKey, []);
        }
        (_kvArrayMap$get2 = kvArrayMap.get(map_row_parentKey)) === null || _kvArrayMap$get2 === void 0 || _kvArrayMap$get2.push(rest);
      }
    });
    fillNewRecord(action === "update");
    map.forEach(function(value) {
      if (!value.map_row_parentKey) {
        var map_row_key = value.map_row_key, rest = _objectWithoutProperties(value, _excluded32);
        if (map_row_key && kvArrayMap.has(map_row_key)) {
          var item = _objectSpread2(_objectSpread2({}, rest), {}, _defineProperty({}, childrenColumnName, kvArrayMap.get(map_row_key)));
          kvSource.push(item);
          return;
        }
        kvSource.push(rest);
      }
    });
    return kvSource;
  };
  return fill(kvMap);
}
function SaveEditableAction(_ref, ref) {
  var recordKey = _ref.recordKey, onSave = _ref.onSave, row = _ref.row, children = _ref.children, newLineConfig = _ref.newLineConfig, editorType = _ref.editorType, tableName = _ref.tableName;
  var context = (0, import_react30.useContext)(ProFormContext);
  var form = form_default.useFormInstance();
  var _useMountMergeState = useMergedState(false), _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2), loading = _useMountMergeState2[0], setLoading = _useMountMergeState2[1];
  var save = useRefFunction(_asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
    var _context$getFieldForm, isMapEditor, namePath, fields, _recordKey, recordKeyPath, curValue, data, res;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1)
        switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            isMapEditor = editorType === "Map";
            namePath = [tableName, Array.isArray(recordKey) ? recordKey[0] : recordKey].map(function(key) {
              return key === null || key === void 0 ? void 0 : key.toString();
            }).flat(1).filter(Boolean);
            setLoading(true);
            _context.next = 6;
            return form.validateFields(namePath, {
              recursive: true
            });
          case 6:
            fields = (context === null || context === void 0 || (_context$getFieldForm = context.getFieldFormatValue) === null || _context$getFieldForm === void 0 ? void 0 : _context$getFieldForm.call(context, namePath)) || form.getFieldValue(namePath);
            if (Array.isArray(recordKey) && recordKey.length > 1) {
              _recordKey = _toArray(recordKey), recordKeyPath = _recordKey.slice(1);
              curValue = get(fields, recordKeyPath);
              set(fields, recordKeyPath, curValue);
            }
            data = isMapEditor ? set({}, namePath, fields) : fields;
            _context.next = 11;
            return onSave === null || onSave === void 0 ? void 0 : onSave(
              recordKey,
              // 如果是 map 模式，fields 就是一个值，所以需要set 到对象中
              // 数据模式 fields 是一个对象，所以不需要
              merge3({}, row, data),
              row,
              newLineConfig
            );
          case 11:
            res = _context.sent;
            setLoading(false);
            return _context.abrupt("return", res);
          case 16:
            _context.prev = 16;
            _context.t0 = _context["catch"](0);
            console.log(_context.t0);
            setLoading(false);
            throw _context.t0;
          case 21:
          case "end":
            return _context.stop();
        }
    }, _callee, null, [[0, 16]]);
  })));
  (0, import_react30.useImperativeHandle)(ref, function() {
    return {
      save
    };
  }, [save]);
  return (0, import_jsx_runtime20.jsxs)("a", {
    onClick: function() {
      var _ref3 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee2(e) {
        return _regeneratorRuntime().wrap(function _callee2$(_context2) {
          while (1)
            switch (_context2.prev = _context2.next) {
              case 0:
                e.stopPropagation();
                e.preventDefault();
                _context2.prev = 2;
                _context2.next = 5;
                return save();
              case 5:
                _context2.next = 9;
                break;
              case 7:
                _context2.prev = 7;
                _context2.t0 = _context2["catch"](2);
              case 9:
              case "end":
                return _context2.stop();
            }
        }, _callee2, null, [[2, 7]]);
      }));
      return function(_x) {
        return _ref3.apply(this, arguments);
      };
    }(),
    children: [loading ? (0, import_jsx_runtime19.jsx)(LoadingOutlined_default, {
      style: {
        marginInlineEnd: 8
      }
    }) : null, children || "保存"]
  }, "save");
}
var DeleteEditableAction = function DeleteEditableAction2(_ref4) {
  var recordKey = _ref4.recordKey, onDelete = _ref4.onDelete, preEditRowRef = _ref4.preEditRowRef, row = _ref4.row, children = _ref4.children, deletePopconfirmMessage = _ref4.deletePopconfirmMessage;
  var _useMountMergeState3 = useMergedState(function() {
    return false;
  }), _useMountMergeState4 = _slicedToArray(_useMountMergeState3, 2), loading = _useMountMergeState4[0], setLoading = _useMountMergeState4[1];
  var _onConfirm = useRefFunction(_asyncToGenerator(_regeneratorRuntime().mark(function _callee3() {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1)
        switch (_context3.prev = _context3.next) {
          case 0:
            _context3.prev = 0;
            setLoading(true);
            _context3.next = 4;
            return onDelete === null || onDelete === void 0 ? void 0 : onDelete(recordKey, row);
          case 4:
            res = _context3.sent;
            setLoading(false);
            return _context3.abrupt("return", res);
          case 9:
            _context3.prev = 9;
            _context3.t0 = _context3["catch"](0);
            console.log(_context3.t0);
            setLoading(false);
            return _context3.abrupt("return", null);
          case 14:
            _context3.prev = 14;
            if (preEditRowRef)
              preEditRowRef.current = null;
            return _context3.finish(14);
          case 17:
          case "end":
            return _context3.stop();
        }
    }, _callee3, null, [[0, 9, 14, 17]]);
  })));
  return children !== false ? (0, import_jsx_runtime19.jsx)(popconfirm_default, {
    title: deletePopconfirmMessage,
    onConfirm: function onConfirm() {
      return _onConfirm();
    },
    children: (0, import_jsx_runtime20.jsxs)("a", {
      children: [loading ? (0, import_jsx_runtime19.jsx)(LoadingOutlined_default, {
        style: {
          marginInlineEnd: 8
        }
      }) : null, children || "删除"]
    })
  }, "delete") : null;
};
var CancelEditableAction = function CancelEditableAction2(props) {
  var recordKey = props.recordKey, tableName = props.tableName, newLineConfig = props.newLineConfig, editorType = props.editorType, onCancel = props.onCancel, cancelEditable = props.cancelEditable, row = props.row, cancelText = props.cancelText, preEditRowRef = props.preEditRowRef;
  var context = (0, import_react30.useContext)(ProFormContext);
  var form = form_default.useFormInstance();
  return (0, import_jsx_runtime19.jsx)("a", {
    onClick: function() {
      var _ref6 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee4(e) {
        var _context$getFieldForm2;
        var isMapEditor, namePath, fields, record, res, _props$onDelete;
        return _regeneratorRuntime().wrap(function _callee4$(_context4) {
          while (1)
            switch (_context4.prev = _context4.next) {
              case 0:
                e.stopPropagation();
                e.preventDefault();
                isMapEditor = editorType === "Map";
                namePath = [tableName, recordKey].flat(1).filter(Boolean);
                fields = (context === null || context === void 0 || (_context$getFieldForm2 = context.getFieldFormatValue) === null || _context$getFieldForm2 === void 0 ? void 0 : _context$getFieldForm2.call(context, namePath)) || (form === null || form === void 0 ? void 0 : form.getFieldValue(namePath));
                record = isMapEditor ? set({}, namePath, fields) : fields;
                _context4.next = 8;
                return onCancel === null || onCancel === void 0 ? void 0 : onCancel(recordKey, record, row, newLineConfig);
              case 8:
                res = _context4.sent;
                _context4.next = 11;
                return cancelEditable(recordKey);
              case 11:
                if (!((preEditRowRef === null || preEditRowRef === void 0 ? void 0 : preEditRowRef.current) !== null)) {
                  _context4.next = 15;
                  break;
                }
                form.setFieldsValue(set({}, namePath, preEditRowRef === null || preEditRowRef === void 0 ? void 0 : preEditRowRef.current));
                _context4.next = 17;
                break;
              case 15:
                _context4.next = 17;
                return (_props$onDelete = props.onDelete) === null || _props$onDelete === void 0 ? void 0 : _props$onDelete.call(props, recordKey, row);
              case 17:
                if (preEditRowRef)
                  preEditRowRef.current = null;
                return _context4.abrupt("return", res);
              case 19:
              case "end":
                return _context4.stop();
            }
        }, _callee4);
      }));
      return function(_x2) {
        return _ref6.apply(this, arguments);
      };
    }(),
    children: cancelText || "取消"
  }, "cancel");
};
function defaultActionRender(row, config) {
  var recordKey = config.recordKey, newLineConfig = config.newLineConfig, saveText = config.saveText, deleteText = config.deleteText;
  var SaveEditableActionRef = (0, import_react30.forwardRef)(SaveEditableAction);
  var saveRef = (0, import_react30.createRef)();
  return {
    save: (0, import_jsx_runtime19.jsx)(SaveEditableActionRef, _objectSpread2(_objectSpread2({}, config), {}, {
      row,
      ref: saveRef,
      children: saveText
    }), "save" + recordKey),
    saveRef,
    delete: (newLineConfig === null || newLineConfig === void 0 ? void 0 : newLineConfig.options.recordKey) !== recordKey ? (0, import_jsx_runtime19.jsx)(DeleteEditableAction, _objectSpread2(_objectSpread2({}, config), {}, {
      row,
      children: deleteText
    }), "delete" + recordKey) : void 0,
    cancel: (0, import_jsx_runtime19.jsx)(CancelEditableAction, _objectSpread2(_objectSpread2({}, config), {}, {
      row
    }), "cancel" + recordKey)
  };
}
function useEditableArray(props) {
  var intl = useIntl();
  var preEditRowRef = (0, import_react30.useRef)(null);
  var _useState = (0, import_react30.useState)(void 0), _useState2 = _slicedToArray(_useState, 2), newLineRecordCache = _useState2[0], setNewLineRecordCache = _useState2[1];
  var resetMapRef = function resetMapRef2() {
    var map = /* @__PURE__ */ new Map();
    var loopGetKey = function loopGetKey2(dataSource, parentKey) {
      dataSource === null || dataSource === void 0 || dataSource.forEach(function(record, index2) {
        var _recordKeyToString2;
        var key = parentKey === void 0 || parentKey === null ? index2.toString() : parentKey + "_" + index2.toString();
        map.set(key, recordKeyToString(props.getRowKey(record, -1)));
        map.set((_recordKeyToString2 = recordKeyToString(props.getRowKey(record, -1))) === null || _recordKeyToString2 === void 0 ? void 0 : _recordKeyToString2.toString(), key);
        if (props.childrenColumnName && record !== null && record !== void 0 && record[props.childrenColumnName]) {
          loopGetKey2(record[props.childrenColumnName], key);
        }
      });
    };
    loopGetKey(props.dataSource);
    return map;
  };
  var initDataSourceKeyIndexMap = (0, import_react30.useMemo)(function() {
    return resetMapRef();
  }, []);
  var dataSourceKeyIndexMapRef = (0, import_react30.useRef)(initDataSourceKeyIndexMap);
  var newLineRecordRef = (0, import_react30.useRef)(void 0);
  useDeepCompareEffectDebounce(function() {
    dataSourceKeyIndexMapRef.current = resetMapRef();
  }, [props.dataSource]);
  newLineRecordRef.current = newLineRecordCache;
  var editableType = props.type || "single";
  var _useLazyKVMap = useLazyKVMap_default(props.dataSource, "children", props.getRowKey), _useLazyKVMap2 = _slicedToArray(_useLazyKVMap, 1), getRecordByKey = _useLazyKVMap2[0];
  var _useMergedState = useMergedState([], {
    value: props.editableKeys,
    onChange: props.onChange ? function(keys) {
      var _props$onChange, _keys$filter, _keys$map$filter;
      props === null || props === void 0 || (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(
        props,
        // 计算编辑的key
        (_keys$filter = keys === null || keys === void 0 ? void 0 : keys.filter(function(key) {
          return key !== void 0;
        })) !== null && _keys$filter !== void 0 ? _keys$filter : [],
        // 计算编辑的行
        (_keys$map$filter = keys === null || keys === void 0 ? void 0 : keys.map(function(key) {
          return getRecordByKey(key);
        }).filter(function(key) {
          return key !== void 0;
        })) !== null && _keys$map$filter !== void 0 ? _keys$map$filter : []
      );
    } : void 0
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), editableKeys = _useMergedState2[0], setEditableRowKeys = _useMergedState2[1];
  var editableKeysSet = (0, import_react30.useMemo)(function() {
    var keys = editableType === "single" ? editableKeys === null || editableKeys === void 0 ? void 0 : editableKeys.slice(0, 1) : editableKeys;
    return new Set(keys);
  }, [(editableKeys || []).join(","), editableType]);
  var editableKeysRef = usePrevious(editableKeys);
  var isEditable = useRefFunction(function(row) {
    var _props$getRowKey, _props$getRowKey$toSt, _props$getRowKey2, _props$getRowKey2$toS;
    var recordKeyOrIndex = (_props$getRowKey = props.getRowKey(row, row.index)) === null || _props$getRowKey === void 0 || (_props$getRowKey$toSt = _props$getRowKey.toString) === null || _props$getRowKey$toSt === void 0 ? void 0 : _props$getRowKey$toSt.call(_props$getRowKey);
    var recordKey = (_props$getRowKey2 = props.getRowKey(row, -1)) === null || _props$getRowKey2 === void 0 || (_props$getRowKey2$toS = _props$getRowKey2.toString) === null || _props$getRowKey2$toS === void 0 ? void 0 : _props$getRowKey2$toS.call(_props$getRowKey2);
    var stringEditableKeys = editableKeys === null || editableKeys === void 0 ? void 0 : editableKeys.map(function(key) {
      return key === null || key === void 0 ? void 0 : key.toString();
    });
    var stringEditableKeysRef = (editableKeysRef === null || editableKeysRef === void 0 ? void 0 : editableKeysRef.map(function(key) {
      return key === null || key === void 0 ? void 0 : key.toString();
    })) || [];
    var preIsEditable = props.tableName && !!(stringEditableKeysRef !== null && stringEditableKeysRef !== void 0 && stringEditableKeysRef.includes(recordKey)) || !!(stringEditableKeysRef !== null && stringEditableKeysRef !== void 0 && stringEditableKeysRef.includes(recordKeyOrIndex));
    return {
      recordKey,
      isEditable: props.tableName && (stringEditableKeys === null || stringEditableKeys === void 0 ? void 0 : stringEditableKeys.includes(recordKey)) || (stringEditableKeys === null || stringEditableKeys === void 0 ? void 0 : stringEditableKeys.includes(recordKeyOrIndex)),
      preIsEditable
    };
  });
  var startEditable = useRefFunction(function(recordKey, record) {
    var _ref7, _props$dataSource;
    if (editableKeysSet.size > 0 && editableType === "single" && props.onlyOneLineEditorAlertMessage !== false) {
      warning4(props.onlyOneLineEditorAlertMessage || intl.getMessage("editableTable.onlyOneLineEditor", "只能同时编辑一行"));
      return false;
    }
    editableKeysSet.add(recordKey);
    setEditableRowKeys(Array.from(editableKeysSet));
    preEditRowRef.current = (_ref7 = record !== null && record !== void 0 ? record : (_props$dataSource = props.dataSource) === null || _props$dataSource === void 0 ? void 0 : _props$dataSource.find(function(recordData, index2) {
      return props.getRowKey(recordData, index2) === recordKey;
    })) !== null && _ref7 !== void 0 ? _ref7 : null;
    return true;
  });
  var cancelEditable = useRefFunction(function() {
    var _ref8 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee5(recordKey, needReTry) {
      var relayKey, key;
      return _regeneratorRuntime().wrap(function _callee5$(_context5) {
        while (1)
          switch (_context5.prev = _context5.next) {
            case 0:
              relayKey = recordKeyToString(recordKey).toString();
              key = dataSourceKeyIndexMapRef.current.get(relayKey);
              if (!(!editableKeysSet.has(relayKey) && key && (needReTry !== null && needReTry !== void 0 ? needReTry : true) && props.tableName)) {
                _context5.next = 5;
                break;
              }
              cancelEditable(key, false);
              return _context5.abrupt("return");
            case 5:
              if (newLineRecordCache && newLineRecordCache.options.recordKey === recordKey) {
                setNewLineRecordCache(void 0);
              }
              editableKeysSet.delete(relayKey);
              editableKeysSet.delete(recordKeyToString(recordKey));
              setEditableRowKeys(Array.from(editableKeysSet));
              return _context5.abrupt("return", true);
            case 10:
            case "end":
              return _context5.stop();
          }
      }, _callee5);
    }));
    return function(_x3, _x4) {
      return _ref8.apply(this, arguments);
    };
  }());
  var propsOnValuesChange = useDebounceFn(_asyncToGenerator(_regeneratorRuntime().mark(function _callee6() {
    var _props$onValuesChange;
    var _len, rest, _key, _args6 = arguments;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1)
        switch (_context6.prev = _context6.next) {
          case 0:
            for (_len = _args6.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
              rest[_key] = _args6[_key];
            }
            (_props$onValuesChange = props.onValuesChange) === null || _props$onValuesChange === void 0 || _props$onValuesChange.call.apply(_props$onValuesChange, [props].concat(rest));
          case 2:
          case "end":
            return _context6.stop();
        }
    }, _callee6);
  })), 64);
  var onValuesChange = useRefFunction(function(value, values) {
    var _Object$keys$pop;
    if (!props.onValuesChange) {
      return;
    }
    var dataSource = props.dataSource;
    editableKeys === null || editableKeys === void 0 || editableKeys.forEach(function(eachRecordKey) {
      if ((newLineRecordCache === null || newLineRecordCache === void 0 ? void 0 : newLineRecordCache.options.recordKey) === eachRecordKey)
        return;
      var recordKey2 = eachRecordKey.toString();
      var editRow2 = get(values, [props.tableName || "", recordKey2].flat(1).filter(function(key) {
        return key || key === 0;
      }));
      if (!editRow2)
        return;
      dataSource = editableRowByKey2({
        data: dataSource,
        getRowKey: props.getRowKey,
        row: editRow2,
        key: recordKey2,
        childrenColumnName: props.childrenColumnName || "children"
      }, "update");
    });
    var relayValue = value;
    var recordKey = (_Object$keys$pop = Object.keys(relayValue || {}).pop()) === null || _Object$keys$pop === void 0 ? void 0 : _Object$keys$pop.toString();
    var newLineRecordData = _objectSpread2(_objectSpread2({}, newLineRecordCache === null || newLineRecordCache === void 0 ? void 0 : newLineRecordCache.defaultValue), get(values, [props.tableName || "", recordKey.toString()].flat(1).filter(function(key) {
      return key || key === 0;
    })));
    var editRow = dataSourceKeyIndexMapRef.current.has(recordKeyToString(recordKey)) ? dataSource.find(function(item, index2) {
      var _props$getRowKey3;
      var key = (_props$getRowKey3 = props.getRowKey(item, index2)) === null || _props$getRowKey3 === void 0 ? void 0 : _props$getRowKey3.toString();
      return key === recordKey;
    }) : newLineRecordData;
    propsOnValuesChange.run(editRow || newLineRecordData, dataSource);
  });
  var saveRefsMap = (0, import_react30.useRef)(/* @__PURE__ */ new Map());
  (0, import_react30.useEffect)(function() {
    saveRefsMap.current.forEach(function(ref, key) {
      if (!editableKeysSet.has(key)) {
        saveRefsMap.current.delete(key);
      }
    });
  }, [saveRefsMap, editableKeysSet]);
  var saveEditable = useRefFunction(function() {
    var _ref10 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee7(recordKey, needReTry) {
      var relayKey, key, saveRef, _saveRef$current;
      return _regeneratorRuntime().wrap(function _callee7$(_context7) {
        while (1)
          switch (_context7.prev = _context7.next) {
            case 0:
              relayKey = recordKeyToString(recordKey);
              key = dataSourceKeyIndexMapRef.current.get(recordKey.toString());
              if (!(!editableKeysSet.has(relayKey) && key && (needReTry !== null && needReTry !== void 0 ? needReTry : true) && props.tableName)) {
                _context7.next = 6;
                break;
              }
              _context7.next = 5;
              return saveEditable(key, false);
            case 5:
              return _context7.abrupt("return", _context7.sent);
            case 6:
              saveRef = saveRefsMap.current.get(relayKey) || saveRefsMap.current.get(relayKey.toString());
              _context7.prev = 7;
              _context7.next = 10;
              return saveRef === null || saveRef === void 0 || (_saveRef$current = saveRef.current) === null || _saveRef$current === void 0 ? void 0 : _saveRef$current.save();
            case 10:
              _context7.next = 15;
              break;
            case 12:
              _context7.prev = 12;
              _context7.t0 = _context7["catch"](7);
              return _context7.abrupt("return", false);
            case 15:
              editableKeysSet.delete(relayKey);
              editableKeysSet.delete(relayKey.toString());
              setEditableRowKeys(Array.from(editableKeysSet));
              return _context7.abrupt("return", true);
            case 19:
            case "end":
              return _context7.stop();
          }
      }, _callee7, null, [[7, 12]]);
    }));
    return function(_x5, _x6) {
      return _ref10.apply(this, arguments);
    };
  }());
  var addEditRecord = useRefFunction(function(row, options) {
    if (options !== null && options !== void 0 && options.parentKey && !dataSourceKeyIndexMapRef.current.has(recordKeyToString(options === null || options === void 0 ? void 0 : options.parentKey).toString())) {
      console.warn("can't find record by key", options === null || options === void 0 ? void 0 : options.parentKey);
      return false;
    }
    if (newLineRecordRef.current && props.onlyAddOneLineAlertMessage !== false) {
      warning4(props.onlyAddOneLineAlertMessage || intl.getMessage("editableTable.onlyAddOneLine", "只能新增一行"));
      return false;
    }
    if (editableKeysSet.size > 0 && editableType === "single" && props.onlyOneLineEditorAlertMessage !== false) {
      warning4(props.onlyOneLineEditorAlertMessage || intl.getMessage("editableTable.onlyOneLineEditor", "只能同时编辑一行"));
      return false;
    }
    var recordKey = props.getRowKey(row, -1);
    if (!recordKey && recordKey !== 0) {
      noteOnce(!!recordKey, "请设置 recordCreatorProps.record 并返回一个唯一的key  \n  https://procomponents.ant.design/components/editable-table#editable-%E6%96%B0%E5%BB%BA%E8%A1%8C");
      throw new Error("请设置 recordCreatorProps.record 并返回一个唯一的key");
    }
    editableKeysSet.add(recordKey);
    setEditableRowKeys(Array.from(editableKeysSet));
    if ((options === null || options === void 0 ? void 0 : options.newRecordType) === "dataSource" || props.tableName) {
      var _recordKeyToString3;
      var actionProps = {
        data: props.dataSource,
        getRowKey: props.getRowKey,
        row: _objectSpread2(_objectSpread2({}, row), {}, {
          map_row_parentKey: options !== null && options !== void 0 && options.parentKey ? (_recordKeyToString3 = recordKeyToString(options === null || options === void 0 ? void 0 : options.parentKey)) === null || _recordKeyToString3 === void 0 ? void 0 : _recordKeyToString3.toString() : void 0
        }),
        key: recordKey,
        childrenColumnName: props.childrenColumnName || "children"
      };
      props.setDataSource(editableRowByKey2(actionProps, (options === null || options === void 0 ? void 0 : options.position) === "top" ? "top" : "update"));
    } else {
      setNewLineRecordCache({
        defaultValue: row,
        options: _objectSpread2(_objectSpread2({}, options), {}, {
          recordKey
        })
      });
    }
    return true;
  });
  var saveText = (props === null || props === void 0 ? void 0 : props.saveText) || intl.getMessage("editableTable.action.save", "保存");
  var deleteText = (props === null || props === void 0 ? void 0 : props.deleteText) || intl.getMessage("editableTable.action.delete", "删除");
  var cancelText = (props === null || props === void 0 ? void 0 : props.cancelText) || intl.getMessage("editableTable.action.cancel", "取消");
  var actionSaveRef = useRefFunction(function() {
    var _ref11 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee8(recordKey, editRow, originRow, newLine) {
      var _props$onSave, _recordKeyToString4, _options$parentKey;
      var res, _ref12, options, actionProps;
      return _regeneratorRuntime().wrap(function _callee8$(_context8) {
        while (1)
          switch (_context8.prev = _context8.next) {
            case 0:
              _context8.next = 2;
              return props === null || props === void 0 || (_props$onSave = props.onSave) === null || _props$onSave === void 0 ? void 0 : _props$onSave.call(props, recordKey, editRow, originRow, newLine);
            case 2:
              res = _context8.sent;
              _context8.next = 5;
              return cancelEditable(recordKey);
            case 5:
              _ref12 = newLine || newLineRecordRef.current || {}, options = _ref12.options;
              if (!(!(options !== null && options !== void 0 && options.parentKey) && (options === null || options === void 0 ? void 0 : options.recordKey) === recordKey)) {
                _context8.next = 9;
                break;
              }
              if ((options === null || options === void 0 ? void 0 : options.position) === "top") {
                props.setDataSource([editRow].concat(_toConsumableArray(props.dataSource)));
              } else {
                props.setDataSource([].concat(_toConsumableArray(props.dataSource), [editRow]));
              }
              return _context8.abrupt("return", res);
            case 9:
              actionProps = {
                data: props.dataSource,
                getRowKey: props.getRowKey,
                row: options ? _objectSpread2(_objectSpread2({}, editRow), {}, {
                  map_row_parentKey: (_recordKeyToString4 = recordKeyToString((_options$parentKey = options === null || options === void 0 ? void 0 : options.parentKey) !== null && _options$parentKey !== void 0 ? _options$parentKey : "")) === null || _recordKeyToString4 === void 0 ? void 0 : _recordKeyToString4.toString()
                }) : editRow,
                key: recordKey,
                childrenColumnName: props.childrenColumnName || "children"
              };
              props.setDataSource(editableRowByKey2(actionProps, (options === null || options === void 0 ? void 0 : options.position) === "top" ? "top" : "update"));
              _context8.next = 13;
              return cancelEditable(recordKey);
            case 13:
              return _context8.abrupt("return", res);
            case 14:
            case "end":
              return _context8.stop();
          }
      }, _callee8);
    }));
    return function(_x7, _x8, _x9, _x10) {
      return _ref11.apply(this, arguments);
    };
  }());
  var actionDeleteRef = useRefFunction(function() {
    var _ref13 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee9(recordKey, editRow) {
      var _props$onDelete2;
      var actionProps, res;
      return _regeneratorRuntime().wrap(function _callee9$(_context9) {
        while (1)
          switch (_context9.prev = _context9.next) {
            case 0:
              actionProps = {
                data: props.dataSource,
                getRowKey: props.getRowKey,
                row: editRow,
                key: recordKey,
                childrenColumnName: props.childrenColumnName || "children"
              };
              _context9.next = 3;
              return props === null || props === void 0 || (_props$onDelete2 = props.onDelete) === null || _props$onDelete2 === void 0 ? void 0 : _props$onDelete2.call(props, recordKey, editRow);
            case 3:
              res = _context9.sent;
              _context9.next = 6;
              return cancelEditable(recordKey, false);
            case 6:
              props.setDataSource(editableRowByKey2(actionProps, "delete"));
              return _context9.abrupt("return", res);
            case 8:
            case "end":
              return _context9.stop();
          }
      }, _callee9);
    }));
    return function(_x11, _x12) {
      return _ref13.apply(this, arguments);
    };
  }());
  var actionCancelRef = useRefFunction(function() {
    var _ref14 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee10(recordKey, editRow, originRow, newLine) {
      var _props$onCancel;
      var res;
      return _regeneratorRuntime().wrap(function _callee10$(_context10) {
        while (1)
          switch (_context10.prev = _context10.next) {
            case 0:
              _context10.next = 2;
              return props === null || props === void 0 || (_props$onCancel = props.onCancel) === null || _props$onCancel === void 0 ? void 0 : _props$onCancel.call(props, recordKey, editRow, originRow, newLine);
            case 2:
              res = _context10.sent;
              return _context10.abrupt("return", res);
            case 4:
            case "end":
              return _context10.stop();
          }
      }, _callee10);
    }));
    return function(_x13, _x14, _x15, _x16) {
      return _ref14.apply(this, arguments);
    };
  }());
  var existCustomActionRender = props.actionRender && typeof props.actionRender === "function";
  var customActionRender = existCustomActionRender ? props.actionRender : function() {
  };
  var customActionRenderRef = useRefFunction(customActionRender);
  var actionRender = function actionRender2(row) {
    var key = props.getRowKey(row, row.index);
    var config = {
      saveText,
      cancelText,
      deleteText,
      addEditRecord,
      recordKey: key,
      cancelEditable,
      index: row.index,
      tableName: props.tableName,
      newLineConfig: newLineRecordCache,
      onCancel: actionCancelRef,
      onDelete: actionDeleteRef,
      onSave: actionSaveRef,
      editableKeys,
      setEditableRowKeys,
      preEditRowRef,
      deletePopconfirmMessage: props.deletePopconfirmMessage || "".concat(intl.getMessage("deleteThisLine", "删除此项"), "?")
    };
    var renderResult = defaultActionRender(row, config);
    if (props.tableName) {
      saveRefsMap.current.set(dataSourceKeyIndexMapRef.current.get(recordKeyToString(key)) || recordKeyToString(key), renderResult.saveRef);
    } else {
      saveRefsMap.current.set(recordKeyToString(key), renderResult.saveRef);
    }
    if (existCustomActionRender)
      return customActionRenderRef(row, config, {
        save: renderResult.save,
        delete: renderResult.delete,
        cancel: renderResult.cancel
      });
    return [renderResult.save, renderResult.delete, renderResult.cancel];
  };
  return {
    editableKeys,
    setEditableRowKeys,
    isEditable,
    actionRender,
    startEditable,
    cancelEditable,
    addEditRecord,
    saveEditable,
    newLineRecord: newLineRecordCache,
    preEditableKeys: editableKeysRef,
    onValuesChange,
    getRealIndex: props.getRealIndex
  };
}

// node_modules/@umijs/use-params/es/index.js
var import_react31 = __toESM(require_react());
var __assign = function() {
  __assign = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
function setQueryToCurrentUrl(params) {
  var _a;
  var URL2 = (typeof window !== "undefined" ? window : {}).URL;
  var url = new URL2((_a = window === null || window === void 0 ? void 0 : window.location) === null || _a === void 0 ? void 0 : _a.href);
  Object.keys(params).forEach(function(key) {
    var value = params[key];
    if (value !== null && value !== void 0) {
      if (Array.isArray(value)) {
        url.searchParams.delete(key);
        value.forEach(function(valueItem) {
          url.searchParams.append(key, valueItem);
        });
      } else if (value instanceof Date) {
        if (!Number.isNaN(value.getTime())) {
          url.searchParams.set(key, value.toISOString());
        }
      } else if (typeof value === "object") {
        url.searchParams.set(key, JSON.stringify(value));
      } else {
        url.searchParams.set(key, value);
      }
    } else {
      url.searchParams.delete(key);
    }
  });
  return url;
}
function useUrlSearchParams(initial, config) {
  var _a;
  if (initial === void 0) {
    initial = {};
  }
  if (config === void 0) {
    config = { disabled: false };
  }
  var _b = (0, import_react31.useState)(), forceUpdate = _b[1];
  var locationSearch = typeof window !== "undefined" && ((_a = window === null || window === void 0 ? void 0 : window.location) === null || _a === void 0 ? void 0 : _a.search);
  var urlSearchParams = (0, import_react31.useMemo)(function() {
    if (config.disabled)
      return {};
    return new URLSearchParams(locationSearch || {});
  }, [config.disabled, locationSearch]);
  var params = (0, import_react31.useMemo)(function() {
    if (config.disabled)
      return {};
    if (typeof window === "undefined" || !window.URL)
      return {};
    var result = [];
    urlSearchParams.forEach(function(value, key) {
      result.push({
        key,
        value
      });
    });
    result = result.reduce(function(acc, val) {
      (acc[val.key] = acc[val.key] || []).push(val);
      return acc;
    }, {});
    result = Object.keys(result).map(function(key) {
      var valueGroup = result[key];
      if (valueGroup.length === 1) {
        return [key, valueGroup[0].value];
      }
      return [key, valueGroup.map(function(_a2) {
        var value = _a2.value;
        return value;
      })];
    });
    var newParams = __assign({}, initial);
    result.forEach(function(_a2) {
      var key = _a2[0], value = _a2[1];
      newParams[key] = parseValue(key, value, {}, initial);
    });
    return newParams;
  }, [config.disabled, initial, urlSearchParams]);
  function redirectToNewSearchParams(newParams) {
    if (typeof window === "undefined" || !window.URL)
      return;
    var url = setQueryToCurrentUrl(newParams);
    if (window.location.search !== url.search) {
      window.history.replaceState({}, "", url.toString());
    }
    if (urlSearchParams.toString() !== url.searchParams.toString()) {
      forceUpdate({});
    }
  }
  (0, import_react31.useEffect)(function() {
    if (config.disabled)
      return;
    if (typeof window === "undefined" || !window.URL)
      return;
    redirectToNewSearchParams(__assign(__assign({}, initial), params));
  }, [config.disabled, params]);
  var setParams = function(newParams) {
    redirectToNewSearchParams(newParams);
  };
  (0, import_react31.useEffect)(function() {
    if (config.disabled)
      return function() {
      };
    if (typeof window === "undefined" || !window.URL)
      return function() {
      };
    var onPopState = function() {
      forceUpdate({});
    };
    window.addEventListener("popstate", onPopState);
    return function() {
      window.removeEventListener("popstate", onPopState);
    };
  }, [config.disabled]);
  return [params, setParams];
}
var booleanValues = {
  true: true,
  false: false
};
function parseValue(key, _value, types, defaultParams) {
  if (!types)
    return _value;
  var type = types[key];
  var value = _value === void 0 ? defaultParams[key] : _value;
  if (type === Number) {
    return Number(value);
  }
  if (type === Boolean || _value === "true" || _value === "false") {
    return booleanValues[value];
  }
  if (Array.isArray(type)) {
    return type.find(function(item) {
      return item == value;
    }) || defaultParams[key];
  }
  return value;
}

export {
  useSWRConfig,
  useSWR,
  createIntl,
  mnMNIntl,
  arEGIntl,
  zhCNIntl,
  enUSIntl,
  enGBIntl,
  viVNIntl,
  itITIntl,
  jaJPIntl,
  esESIntl,
  caESIntl,
  ruRUIntl,
  srRSIntl,
  msMYIntl,
  zhTWIntl,
  frFRIntl,
  ptBRIntl,
  koKRIntl,
  idIDIntl,
  deDEIntl,
  faIRIntl,
  trTRIntl,
  plPLIntl,
  hrHRIntl,
  thTHIntl,
  csCZIntl,
  skSKIntl,
  heILIntl,
  ukUAIntl,
  uzUZIntl,
  nlNLIntl,
  roROIntl,
  svSEIntl,
  intlMap,
  intlMapKeys,
  findIntlKeyByAntdLocaleKey,
  setAlpha,
  lighten,
  proTheme,
  useToken3 as useToken,
  resetComponent,
  operationUnit,
  useStyle,
  isNeedOpenHash,
  ConfigConsumer,
  ProConfigProvider,
  useIntl,
  ProProvider,
  es_default,
  DropdownFooter,
  ErrorBoundary,
  FieldLabel,
  omitUndefined3 as omitUndefined,
  compareVersions,
  openVisibleCompatible,
  FilterDropdown,
  InlineErrorFormItem,
  LabelIconTip,
  ProFormContext,
  isNil,
  dateFormatterMap,
  convertMoment,
  conversionMomentValue,
  dateArrayFormatter,
  genCopyable,
  runFunction,
  getFieldPropsOrFormItemProps,
  coverToNewToken,
  menuOverlayCompatible,
  useRefFunction,
  useDebounceFn,
  useLatest,
  useDebounceValue,
  isDeepEqualReact,
  useDeepCompareEffect,
  useDeepCompareEffectDebounce,
  useDeepCompareMemo_default,
  isBrowser,
  useDocumentTitle,
  useFetchData,
  usePrevious,
  useRefCallback,
  useReactiveRef,
  isDropdownValueType,
  isImg,
  isUrl,
  merge3 as merge,
  nanoid,
  omitBoolean,
  omitUndefinedAndEmptyArr,
  parseValueToDay,
  pickProFormItemProps,
  pickProProps,
  objectToMap,
  proFieldParsingText,
  stringify_default,
  eq_default,
  root_default,
  Symbol_default,
  baseGetTag_default,
  toSource_default,
  getNative_default,
  Map_default,
  MapCache_default,
  Stack_default,
  Uint8Array_default,
  overArg_default,
  isPrototype_default,
  isObjectLike_default,
  isArguments_default,
  isArray_default,
  isArrayLike_default,
  isBuffer_default,
  isTypedArray_default,
  arrayLikeKeys_default,
  merge_default,
  transformKeySubmitValue,
  compatibleBorder,
  recordKeyToString,
  editableRowByKey2 as editableRowByKey,
  useEditableArray,
  useEditableMap,
  useBreakpoint,
  useUrlSearchParams
};
/*! Bundled license information:

use-sync-external-store/cjs/use-sync-external-store-shim.development.js:
  (**
   * @license React
   * use-sync-external-store-shim.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-JF54ERNK.js.map
