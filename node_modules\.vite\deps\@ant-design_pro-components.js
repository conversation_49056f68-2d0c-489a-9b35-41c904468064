import {
  <PERSON><PERSON>ult<PERSON><PERSON>er,
  <PERSON><PERSON>ult<PERSON>eader,
  FooterToolbar,
  GridContent,
  PageContainer,
  PageHeader,
  PageLoading,
  ProBreadcrumb,
  ProHelp,
  ProHelpContentPanel,
  ProHelpDrawer,
  ProHelpModal,
  ProHelpPanel,
  ProHelpPopover,
  ProHelpProvide,
  ProHelpSelect,
  ProLayout,
  ProPageHeader,
  RenderContentPanel,
  RouteContext,
  SelectKeyProvide,
  SettingDrawer,
  TopNavHeader,
  WaterMark,
  getMenuData,
  getPageTitle
} from "./chunk-CS7PLJDG.js";
import {
  Captcha_default,
  Cascader_default,
  CellEditorTable,
  CheckCard_default,
  Checkbox_default,
  Code_default,
  ColorPicker_default,
  DateMonthRangePicker_default,
  DatePicker_default,
  DatePicker_default2,
  DateQuarterRangePicker_default,
  DateRangePicker_default,
  DateTimePicker_default,
  DateTimeRangePicker_default,
  DateWeekRangePicker_default,
  DateYearRangePicker_default,
  <PERSON>pendency_default,
  <PERSON>gitRange_default,
  <PERSON>git_default,
  DragSortTable_default,
  DrawerForm,
  Dropdown_default,
  EditableTable_default,
  FieldContext,
  FieldSet_default,
  Field_default,
  FormControlRender,
  FormItemProvide,
  FormItemRender,
  FormItem_default,
  FormListContext,
  Form_default,
  GridContext,
  Group_default,
  IndexColumn_default,
  LightFilter,
  ListToolBar_default,
  LoginForm,
  LoginFormPage,
  ModalForm,
  Money_default,
  Money_default2,
  Percent_default,
  ProCard_default,
  ProField,
  ProForm,
  ProFormGroup,
  ProFormItemRender,
  ProFormList,
  Progress_default,
  QueryFilter,
  Radio_default,
  RangePicker_default,
  Rate_default,
  RowEditorTable,
  SchemaForm_default,
  Segmented_default,
  Select_default,
  Select_default2,
  Slider_default,
  StatisticCard_default,
  Statistic_default,
  Status_default,
  StepsFormWarp,
  Submitter_default,
  Switch_default,
  Table_default,
  TextArea_default,
  Text_default,
  Text_default2,
  TimePicker_default,
  TimePicker_default2,
  TreeSelect_default,
  UploadButton_default,
  UploadDragger_default,
  defaultRenderText,
  es_default,
  es_default2,
  pickControlProps,
  pickControlPropsWithId,
  proFieldParsingValueEnumToArray,
  useControlModel
} from "./chunk-USZXIJE7.js";
import {
  ConfigConsumer,
  DropdownFooter,
  ErrorBoundary,
  FieldLabel,
  FilterDropdown,
  InlineErrorFormItem,
  LabelIconTip,
  ProConfigProvider,
  ProFormContext,
  ProProvider,
  arEGIntl,
  caESIntl,
  compareVersions,
  compatibleBorder,
  conversionMomentValue,
  convertMoment,
  coverToNewToken,
  createIntl,
  csCZIntl,
  dateArrayFormatter,
  dateFormatterMap,
  deDEIntl,
  editableRowByKey,
  enGBIntl,
  enUSIntl,
  esESIntl,
  faIRIntl,
  findIntlKeyByAntdLocaleKey,
  frFRIntl,
  genCopyable,
  getFieldPropsOrFormItemProps,
  heILIntl,
  hrHRIntl,
  idIDIntl,
  intlMap,
  intlMapKeys,
  isBrowser,
  isDeepEqualReact,
  isDropdownValueType,
  isImg,
  isNeedOpenHash,
  isNil,
  isUrl,
  itITIntl,
  jaJPIntl,
  koKRIntl,
  lighten,
  menuOverlayCompatible,
  merge,
  mnMNIntl,
  msMYIntl,
  nanoid,
  nlNLIntl,
  objectToMap,
  omitBoolean,
  omitUndefined,
  omitUndefinedAndEmptyArr,
  openVisibleCompatible,
  operationUnit,
  parseValueToDay,
  pickProFormItemProps,
  pickProProps,
  plPLIntl,
  proFieldParsingText,
  proTheme,
  ptBRIntl,
  recordKeyToString,
  resetComponent,
  roROIntl,
  ruRUIntl,
  runFunction,
  setAlpha,
  skSKIntl,
  srRSIntl,
  stringify_default,
  svSEIntl,
  thTHIntl,
  trTRIntl,
  transformKeySubmitValue,
  ukUAIntl,
  useBreakpoint,
  useDebounceFn,
  useDebounceValue,
  useDeepCompareEffect,
  useDeepCompareEffectDebounce,
  useDeepCompareMemo_default,
  useDocumentTitle,
  useEditableArray,
  useEditableMap,
  useFetchData,
  useIntl,
  useLatest,
  usePrevious,
  useReactiveRef,
  useRefCallback,
  useRefFunction,
  useStyle,
  useToken,
  uzUZIntl,
  viVNIntl,
  zhCNIntl,
  zhTWIntl
} from "./chunk-JF54ERNK.js";
import {
  Keyframes_default,
  _asyncToGenerator,
  _regeneratorRuntime,
  _toConsumableArray,
  card_default,
  config_provider_default,
  descriptions_default,
  divider_default,
  get,
  list_default,
  skeleton_default,
  space_default,
  toArray,
  useBreakpoint_default,
  useLazyKVMap_default,
  useMergedState,
  usePagination_default,
  useSelection_default,
  version_default
} from "./chunk-26W7M2TV.js";
import {
  require_jsx_runtime
} from "./chunk-YUEMZWDV.js";
import {
  CheckOutlined_default,
  CloseOutlined_default,
  EditOutlined_default,
  RightOutlined_default,
  _defineProperty,
  _objectSpread2,
  _objectWithoutProperties,
  _slicedToArray,
  init_defineProperty,
  require_classnames
} from "./chunk-V74HSD5Q.js";
import "./chunk-TLVXLPKY.js";
import {
  __toESM,
  require_react
} from "./chunk-5A2KPB3J.js";

// node_modules/@ant-design/pro-skeleton/es/index.js
var import_react4 = __toESM(require_react());

// node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var import_react2 = __toESM(require_react());

// node_modules/@ant-design/pro-skeleton/es/components/List/index.js
var import_react = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var Line = function Line2(_ref) {
  var padding = _ref.padding;
  return (0, import_jsx_runtime.jsx)("div", {
    style: {
      padding: padding || "0 24px"
    },
    children: (0, import_jsx_runtime.jsx)(divider_default, {
      style: {
        margin: 0
      }
    })
  });
};
var MediaQueryKeyEnum = {
  xs: 2,
  sm: 2,
  md: 4,
  lg: 4,
  xl: 6,
  xxl: 6
};
var StatisticSkeleton = function StatisticSkeleton2(_ref2) {
  var size = _ref2.size, active = _ref2.active;
  var defaultCol = (0, import_react.useMemo)(function() {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = useBreakpoint_default() || defaultCol;
  var colSize = Object.keys(col).filter(function(key) {
    return col[key] === true;
  })[0] || "md";
  var arraySize = size === void 0 ? MediaQueryKeyEnum[colSize] || 6 : size;
  var firstWidth = function firstWidth2(index) {
    if (index === 0) {
      return 0;
    }
    if (arraySize > 2) {
      return 42;
    }
    return 16;
  };
  return (0, import_jsx_runtime.jsx)(card_default, {
    bordered: false,
    style: {
      marginBlockEnd: 16
    },
    children: (0, import_jsx_runtime.jsx)("div", {
      style: {
        width: "100%",
        justifyContent: "space-between",
        display: "flex"
      },
      children: new Array(arraySize).fill(null).map(function(_, index) {
        return (0, import_jsx_runtime2.jsxs)("div", {
          style: {
            borderInlineStart: arraySize > 2 && index === 1 ? "1px solid rgba(0,0,0,0.06)" : void 0,
            paddingInlineStart: firstWidth(index),
            flex: 1,
            marginInlineEnd: index === 0 ? 16 : 0
          },
          children: [(0, import_jsx_runtime.jsx)(skeleton_default, {
            active,
            paragraph: false,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            }
          }), (0, import_jsx_runtime.jsx)(skeleton_default.Button, {
            active,
            style: {
              height: 48
            }
          })]
        }, index);
      })
    })
  });
};
var ListSkeletonItem = function ListSkeletonItem2(_ref3) {
  var active = _ref3.active;
  return (0, import_jsx_runtime2.jsxs)(import_jsx_runtime3.Fragment, {
    children: [(0, import_jsx_runtime.jsx)(card_default, {
      bordered: false,
      style: {
        borderRadius: 0
      },
      styles: {
        body: {
          padding: 24
        }
      },
      children: (0, import_jsx_runtime2.jsxs)("div", {
        style: {
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between"
        },
        children: [(0, import_jsx_runtime.jsx)("div", {
          style: {
            maxWidth: "100%",
            flex: 1
          },
          children: (0, import_jsx_runtime.jsx)(skeleton_default, {
            active,
            title: {
              width: 100,
              style: {
                marginBlockStart: 0
              }
            },
            paragraph: {
              rows: 1,
              style: {
                margin: 0
              }
            }
          })
        }), (0, import_jsx_runtime.jsx)(skeleton_default.Button, {
          active,
          size: "small",
          style: {
            width: 165,
            marginBlockStart: 12
          }
        })]
      })
    }), (0, import_jsx_runtime.jsx)(Line, {})]
  });
};
var ListSkeleton = function ListSkeleton2(_ref4) {
  var size = _ref4.size, _ref4$active = _ref4.active, active = _ref4$active === void 0 ? true : _ref4$active, actionButton = _ref4.actionButton;
  return (0, import_jsx_runtime2.jsxs)(card_default, {
    bordered: false,
    styles: {
      body: {
        padding: 0
      }
    },
    children: [new Array(size).fill(null).map(function(_, index) {
      return (
        // eslint-disable-next-line react/no-array-index-key
        (0, import_jsx_runtime.jsx)(ListSkeletonItem, {
          active: !!active
        }, index)
      );
    }), actionButton !== false && (0, import_jsx_runtime.jsx)(card_default, {
      bordered: false,
      style: {
        borderStartEndRadius: 0,
        borderTopLeftRadius: 0
      },
      styles: {
        body: {
          display: "flex",
          alignItems: "center",
          justifyContent: "center"
        }
      },
      children: (0, import_jsx_runtime.jsx)(skeleton_default.Button, {
        style: {
          width: 102
        },
        active,
        size: "small"
      })
    })]
  });
};
var PageHeaderSkeleton = function PageHeaderSkeleton2(_ref5) {
  var active = _ref5.active;
  return (0, import_jsx_runtime2.jsxs)("div", {
    style: {
      marginBlockEnd: 16
    },
    children: [(0, import_jsx_runtime.jsx)(skeleton_default, {
      paragraph: false,
      title: {
        width: 185
      }
    }), (0, import_jsx_runtime.jsx)(skeleton_default.Button, {
      active,
      size: "small"
    })]
  });
};
var ListToolbarSkeleton = function ListToolbarSkeleton2(_ref6) {
  var active = _ref6.active;
  return (0, import_jsx_runtime.jsx)(card_default, {
    bordered: false,
    style: {
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0
    },
    styles: {
      body: {
        paddingBlockEnd: 8
      }
    },
    children: (0, import_jsx_runtime2.jsxs)(space_default, {
      style: {
        width: "100%",
        justifyContent: "space-between"
      },
      children: [(0, import_jsx_runtime.jsx)(skeleton_default.Button, {
        active,
        style: {
          width: 200
        },
        size: "small"
      }), (0, import_jsx_runtime2.jsxs)(space_default, {
        children: [(0, import_jsx_runtime.jsx)(skeleton_default.Button, {
          active,
          size: "small",
          style: {
            width: 120
          }
        }), (0, import_jsx_runtime.jsx)(skeleton_default.Button, {
          active,
          size: "small",
          style: {
            width: 80
          }
        })]
      })]
    })
  });
};
var ListPageSkeleton = function ListPageSkeleton2(_ref7) {
  var _ref7$active = _ref7.active, active = _ref7$active === void 0 ? true : _ref7$active, statistic = _ref7.statistic, actionButton = _ref7.actionButton, toolbar = _ref7.toolbar, pageHeader = _ref7.pageHeader, _ref7$list = _ref7.list, list = _ref7$list === void 0 ? 5 : _ref7$list;
  return (0, import_jsx_runtime2.jsxs)("div", {
    style: {
      width: "100%"
    },
    children: [pageHeader !== false && (0, import_jsx_runtime.jsx)(PageHeaderSkeleton, {
      active
    }), statistic !== false && (0, import_jsx_runtime.jsx)(StatisticSkeleton, {
      size: statistic,
      active
    }), (toolbar !== false || list !== false) && (0, import_jsx_runtime2.jsxs)(card_default, {
      bordered: false,
      styles: {
        body: {
          padding: 0
        }
      },
      children: [toolbar !== false && (0, import_jsx_runtime.jsx)(ListToolbarSkeleton, {
        active
      }), list !== false && (0, import_jsx_runtime.jsx)(ListSkeleton, {
        size: list,
        active,
        actionButton
      })]
    })]
  });
};
var List_default = ListPageSkeleton;

// node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var MediaQueryKeyEnum2 = {
  xs: 1,
  sm: 2,
  md: 3,
  lg: 3,
  xl: 3,
  xxl: 4
};
var DescriptionsLargeItemSkeleton = function DescriptionsLargeItemSkeleton2(_ref) {
  var active = _ref.active;
  return (0, import_jsx_runtime5.jsxs)("div", {
    style: {
      marginBlockStart: 32
    },
    children: [(0, import_jsx_runtime4.jsx)(skeleton_default.Button, {
      active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), (0, import_jsx_runtime5.jsxs)("div", {
      style: {
        width: "100%",
        justifyContent: "space-between",
        display: "flex"
      },
      children: [(0, import_jsx_runtime5.jsxs)("div", {
        style: {
          flex: 1,
          marginInlineEnd: 24,
          maxWidth: 300
        },
        children: [(0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 0
            }
          }
        }), (0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        }), (0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        })]
      }), (0, import_jsx_runtime4.jsx)("div", {
        style: {
          flex: 1,
          alignItems: "center",
          justifyContent: "center"
        },
        children: (0, import_jsx_runtime5.jsxs)("div", {
          style: {
            maxWidth: 300,
            margin: "auto"
          },
          children: [(0, import_jsx_runtime4.jsx)(skeleton_default, {
            active,
            paragraph: false,
            title: {
              style: {
                marginBlockStart: 0
              }
            }
          }), (0, import_jsx_runtime4.jsx)(skeleton_default, {
            active,
            paragraph: false,
            title: {
              style: {
                marginBlockStart: 8
              }
            }
          })]
        })
      })]
    })]
  });
};
var DescriptionsItemSkeleton = function DescriptionsItemSkeleton2(_ref2) {
  var size = _ref2.size, active = _ref2.active;
  var defaultCol = (0, import_react2.useMemo)(function() {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = useBreakpoint_default() || defaultCol;
  var colSize = Object.keys(col).filter(function(key) {
    return col[key] === true;
  })[0] || "md";
  var arraySize = size === void 0 ? MediaQueryKeyEnum2[colSize] || 3 : size;
  return (0, import_jsx_runtime4.jsx)("div", {
    style: {
      width: "100%",
      justifyContent: "space-between",
      display: "flex"
    },
    children: new Array(arraySize).fill(null).map(function(_, index) {
      return (0, import_jsx_runtime5.jsxs)("div", {
        style: {
          flex: 1,
          paddingInlineStart: index === 0 ? 0 : 24,
          paddingInlineEnd: index === arraySize - 1 ? 0 : 24
        },
        children: [(0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 0
            }
          }
        }), (0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        }), (0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              marginBlockStart: 8
            }
          }
        })]
      }, index);
    })
  });
};
var TableItemSkeleton = function TableItemSkeleton2(_ref3) {
  var active = _ref3.active, _ref3$header = _ref3.header, header = _ref3$header === void 0 ? false : _ref3$header;
  var defaultCol = (0, import_react2.useMemo)(function() {
    return {
      lg: true,
      md: true,
      sm: false,
      xl: false,
      xs: false,
      xxl: false
    };
  }, []);
  var col = useBreakpoint_default() || defaultCol;
  var colSize = Object.keys(col).filter(function(key) {
    return col[key] === true;
  })[0] || "md";
  var arraySize = MediaQueryKeyEnum2[colSize] || 3;
  return (0, import_jsx_runtime5.jsxs)(import_jsx_runtime6.Fragment, {
    children: [(0, import_jsx_runtime5.jsxs)("div", {
      style: {
        display: "flex",
        background: header ? "rgba(0,0,0,0.02)" : "none",
        padding: "24px 8px"
      },
      children: [new Array(arraySize).fill(null).map(function(_, index) {
        return (0, import_jsx_runtime4.jsx)("div", {
          style: {
            flex: 1,
            paddingInlineStart: header && index === 0 ? 0 : 20,
            paddingInlineEnd: 32
          },
          children: (0, import_jsx_runtime4.jsx)(skeleton_default, {
            active,
            paragraph: false,
            title: {
              style: {
                margin: 0,
                height: 24,
                width: header ? "75px" : "100%"
              }
            }
          })
        }, index);
      }), (0, import_jsx_runtime4.jsx)("div", {
        style: {
          flex: 3,
          paddingInlineStart: 32
        },
        children: (0, import_jsx_runtime4.jsx)(skeleton_default, {
          active,
          paragraph: false,
          title: {
            style: {
              margin: 0,
              height: 24,
              width: header ? "75px" : "100%"
            }
          }
        })
      })]
    }), (0, import_jsx_runtime4.jsx)(Line, {
      padding: "0px 0px"
    })]
  });
};
var TableSkeleton = function TableSkeleton2(_ref4) {
  var active = _ref4.active, _ref4$size = _ref4.size, size = _ref4$size === void 0 ? 4 : _ref4$size;
  return (0, import_jsx_runtime5.jsxs)(card_default, {
    bordered: false,
    children: [(0, import_jsx_runtime4.jsx)(skeleton_default.Button, {
      active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), (0, import_jsx_runtime4.jsx)(TableItemSkeleton, {
      header: true,
      active
    }), new Array(size).fill(null).map(function(_, index) {
      return (
        // eslint-disable-next-line react/no-array-index-key
        (0, import_jsx_runtime4.jsx)(TableItemSkeleton, {
          active
        }, index)
      );
    }), (0, import_jsx_runtime4.jsx)("div", {
      style: {
        display: "flex",
        justifyContent: "flex-end",
        paddingBlockStart: 16
      },
      children: (0, import_jsx_runtime4.jsx)(skeleton_default, {
        active,
        paragraph: false,
        title: {
          style: {
            margin: 0,
            height: 32,
            float: "right",
            maxWidth: "630px"
          }
        }
      })
    })]
  });
};
var DescriptionsSkeleton = function DescriptionsSkeleton2(_ref5) {
  var active = _ref5.active;
  return (0, import_jsx_runtime5.jsxs)(card_default, {
    bordered: false,
    style: {
      borderStartEndRadius: 0,
      borderTopLeftRadius: 0
    },
    children: [(0, import_jsx_runtime4.jsx)(skeleton_default.Button, {
      active,
      size: "small",
      style: {
        width: 100,
        marginBlockEnd: 16
      }
    }), (0, import_jsx_runtime4.jsx)(DescriptionsItemSkeleton, {
      active
    }), (0, import_jsx_runtime4.jsx)(DescriptionsLargeItemSkeleton, {
      active
    })]
  });
};
var DescriptionsPageSkeleton = function DescriptionsPageSkeleton2(_ref6) {
  var _ref6$active = _ref6.active, active = _ref6$active === void 0 ? true : _ref6$active, pageHeader = _ref6.pageHeader, list = _ref6.list;
  return (0, import_jsx_runtime5.jsxs)("div", {
    style: {
      width: "100%"
    },
    children: [pageHeader !== false && (0, import_jsx_runtime4.jsx)(PageHeaderSkeleton, {
      active
    }), (0, import_jsx_runtime4.jsx)(DescriptionsSkeleton, {
      active
    }), list !== false && (0, import_jsx_runtime4.jsx)(Line, {}), list !== false && (0, import_jsx_runtime4.jsx)(TableSkeleton, {
      active,
      size: list
    })]
  });
};
var Descriptions_default = DescriptionsPageSkeleton;

// node_modules/@ant-design/pro-skeleton/es/components/Result/index.js
var import_react3 = __toESM(require_react());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var ResultPageSkeleton = function ResultPageSkeleton2(_ref) {
  var _ref$active = _ref.active, active = _ref$active === void 0 ? true : _ref$active, pageHeader = _ref.pageHeader;
  return (0, import_jsx_runtime8.jsxs)("div", {
    style: {
      width: "100%"
    },
    children: [pageHeader !== false && (0, import_jsx_runtime7.jsx)(PageHeaderSkeleton, {
      active
    }), (0, import_jsx_runtime7.jsx)(card_default, {
      children: (0, import_jsx_runtime8.jsxs)("div", {
        style: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
          padding: 128
        },
        children: [(0, import_jsx_runtime7.jsx)(skeleton_default.Avatar, {
          size: 64,
          style: {
            marginBlockEnd: 32
          }
        }), (0, import_jsx_runtime7.jsx)(skeleton_default.Button, {
          active,
          style: {
            width: 214,
            marginBlockEnd: 8
          }
        }), (0, import_jsx_runtime7.jsx)(skeleton_default.Button, {
          active,
          style: {
            width: 328
          },
          size: "small"
        }), (0, import_jsx_runtime8.jsxs)(space_default, {
          style: {
            marginBlockStart: 24
          },
          children: [(0, import_jsx_runtime7.jsx)(skeleton_default.Button, {
            active,
            style: {
              width: 116
            }
          }), (0, import_jsx_runtime7.jsx)(skeleton_default.Button, {
            active,
            style: {
              width: 116
            }
          })]
        })]
      })
    })]
  });
};
var Result_default = ResultPageSkeleton;

// node_modules/@ant-design/pro-skeleton/es/index.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var _excluded = ["type"];
var ProSkeleton = function ProSkeleton2(_ref) {
  var _ref$type = _ref.type, type = _ref$type === void 0 ? "list" : _ref$type, rest = _objectWithoutProperties(_ref, _excluded);
  if (type === "result") {
    return (0, import_jsx_runtime9.jsx)(Result_default, _objectSpread2({}, rest));
  }
  if (type === "descriptions") {
    return (0, import_jsx_runtime9.jsx)(Descriptions_default, _objectSpread2({}, rest));
  }
  return (0, import_jsx_runtime9.jsx)(List_default, _objectSpread2({}, rest));
};
var es_default3 = ProSkeleton;

// node_modules/@ant-design/pro-descriptions/es/index.js
var import_react6 = __toESM(require_react());

// node_modules/@ant-design/pro-descriptions/es/useFetchData.js
var import_react5 = __toESM(require_react());
var useFetchData2 = function useFetchData3(getData, options) {
  var _ref = options || {}, onRequestError = _ref.onRequestError, effects = _ref.effects, manual = _ref.manual, dataSource = _ref.dataSource, defaultDataSource = _ref.defaultDataSource, onDataSourceChange = _ref.onDataSourceChange;
  var _useMergedState = useMergedState(defaultDataSource, {
    value: dataSource,
    onChange: onDataSourceChange
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), entity = _useMergedState2[0], setEntity = _useMergedState2[1];
  var _useMergedState3 = useMergedState(options === null || options === void 0 ? void 0 : options.loading, {
    value: options === null || options === void 0 ? void 0 : options.loading,
    onChange: options === null || options === void 0 ? void 0 : options.onLoadingChange
  }), _useMergedState4 = _slicedToArray(_useMergedState3, 2), loading = _useMergedState4[0], setLoading = _useMergedState4[1];
  var updateDataAndLoading = function updateDataAndLoading2(data) {
    setEntity(data);
    setLoading(false);
  };
  var fetchList = function() {
    var _ref2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
      var _ref3, data, success;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1)
          switch (_context.prev = _context.next) {
            case 0:
              if (!loading) {
                _context.next = 2;
                break;
              }
              return _context.abrupt("return");
            case 2:
              setLoading(true);
              _context.prev = 3;
              _context.next = 6;
              return getData();
            case 6:
              _context.t0 = _context.sent;
              if (_context.t0) {
                _context.next = 9;
                break;
              }
              _context.t0 = {};
            case 9:
              _ref3 = _context.t0;
              data = _ref3.data;
              success = _ref3.success;
              if (success !== false) {
                updateDataAndLoading(data);
              }
              _context.next = 23;
              break;
            case 15:
              _context.prev = 15;
              _context.t1 = _context["catch"](3);
              if (!(onRequestError === void 0)) {
                _context.next = 21;
                break;
              }
              throw new Error(_context.t1);
            case 21:
              onRequestError(_context.t1);
            case 22:
              setLoading(false);
            case 23:
              _context.prev = 23;
              setLoading(false);
              return _context.finish(23);
            case 26:
            case "end":
              return _context.stop();
          }
      }, _callee, null, [[3, 15, 23, 26]]);
    }));
    return function fetchList2() {
      return _ref2.apply(this, arguments);
    };
  }();
  (0, import_react5.useEffect)(function() {
    if (manual) {
      return;
    }
    fetchList();
  }, [].concat(_toConsumableArray(effects || []), [manual]));
  return {
    dataSource: entity,
    setDataSource: setEntity,
    loading,
    reload: function reload() {
      return fetchList();
    }
  };
};
var useFetchData_default = useFetchData2;

// node_modules/@ant-design/pro-descriptions/es/index.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var import_react7 = __toESM(require_react());
var _excluded2 = ["valueEnum", "render", "renderText", "mode", "plain", "dataIndex", "request", "params", "editable"];
var _excluded22 = ["request", "columns", "params", "dataSource", "onDataSourceChange", "formProps", "editable", "loading", "onLoadingChange", "actionRef", "onRequestError", "emptyText", "contentStyle"];
var getDataFromConfig = function getDataFromConfig2(item, entity) {
  var dataIndex = item.dataIndex;
  if (dataIndex) {
    var data = Array.isArray(dataIndex) ? get(entity, dataIndex) : entity[dataIndex];
    if (data !== void 0 || data !== null) {
      return data;
    }
  }
  return item.children;
};
var FieldRender = function FieldRender2(props) {
  var _proTheme$useToken2;
  var valueEnum = props.valueEnum, action = props.action, index = props.index, text = props.text, entity = props.entity, mode = props.mode, render = props.render, editableUtils = props.editableUtils, valueType = props.valueType, plain = props.plain, dataIndex = props.dataIndex, request = props.request, renderFormItem = props.renderFormItem, params = props.params, emptyText = props.emptyText;
  var form = es_default.useFormInstance();
  var _proTheme$useToken = (_proTheme$useToken2 = proTheme.useToken) === null || _proTheme$useToken2 === void 0 ? void 0 : _proTheme$useToken2.call(proTheme), token = _proTheme$useToken.token;
  var fieldConfig = {
    text,
    valueEnum,
    mode: mode || "read",
    proFieldProps: {
      emptyText,
      render: render ? function(finText) {
        return render === null || render === void 0 ? void 0 : render(finText, entity, index, action, _objectSpread2(_objectSpread2({}, props), {}, {
          type: "descriptions"
        }));
      } : void 0
    },
    ignoreFormItem: true,
    valueType,
    request,
    params,
    plain
  };
  if (mode === "read" || !mode || valueType === "option") {
    var fieldProps = getFieldPropsOrFormItemProps(props.fieldProps, void 0, _objectSpread2(_objectSpread2({}, props), {}, {
      rowKey: dataIndex,
      isEditable: false
    }));
    return (0, import_jsx_runtime10.jsx)(Field_default, _objectSpread2(_objectSpread2({
      name: dataIndex
    }, fieldConfig), {}, {
      fieldProps
    }));
  }
  var renderDom = function renderDom2() {
    var _editableUtils$action;
    var formItemProps = getFieldPropsOrFormItemProps(props.formItemProps, form, _objectSpread2(_objectSpread2({}, props), {}, {
      rowKey: dataIndex,
      isEditable: true
    }));
    var fieldProps2 = getFieldPropsOrFormItemProps(props.fieldProps, form, _objectSpread2(_objectSpread2({}, props), {}, {
      rowKey: dataIndex,
      isEditable: true
    }));
    return (0, import_jsx_runtime11.jsxs)("div", {
      style: {
        display: "flex",
        gap: token.marginXS,
        alignItems: "baseline"
      },
      children: [(0, import_jsx_runtime10.jsx)(InlineErrorFormItem, _objectSpread2(_objectSpread2({
        name: dataIndex
      }, formItemProps), {}, {
        style: _objectSpread2({
          margin: 0
        }, (formItemProps === null || formItemProps === void 0 ? void 0 : formItemProps.style) || {}),
        initialValue: text || (formItemProps === null || formItemProps === void 0 ? void 0 : formItemProps.initialValue),
        children: (0, import_jsx_runtime10.jsx)(Field_default, _objectSpread2(_objectSpread2({}, fieldConfig), {}, {
          // @ts-ignore
          proFieldProps: _objectSpread2({}, fieldConfig.proFieldProps),
          renderFormItem: renderFormItem ? function() {
            return renderFormItem === null || renderFormItem === void 0 ? void 0 : renderFormItem(_objectSpread2(_objectSpread2({}, props), {}, {
              type: "descriptions"
            }), {
              isEditable: true,
              recordKey: dataIndex,
              record: form.getFieldValue([dataIndex].flat(1)),
              defaultRender: function defaultRender() {
                return (0, import_jsx_runtime10.jsx)(Field_default, _objectSpread2(_objectSpread2({}, fieldConfig), {}, {
                  fieldProps: fieldProps2
                }));
              },
              type: "descriptions"
            }, form);
          } : void 0,
          fieldProps: fieldProps2
        }))
      })), (0, import_jsx_runtime10.jsx)("div", {
        style: {
          display: "flex",
          maxHeight: token.controlHeight,
          alignItems: "center",
          gap: token.marginXS
        },
        children: editableUtils === null || editableUtils === void 0 || (_editableUtils$action = editableUtils.actionRender) === null || _editableUtils$action === void 0 ? void 0 : _editableUtils$action.call(editableUtils, dataIndex || index, {
          cancelText: (0, import_jsx_runtime10.jsx)(CloseOutlined_default, {}),
          saveText: (0, import_jsx_runtime10.jsx)(CheckOutlined_default, {}),
          deleteText: false
        })
      })]
    });
  };
  return (0, import_jsx_runtime10.jsx)("div", {
    style: {
      marginTop: -5,
      marginBottom: -5,
      marginLeft: 0,
      marginRight: 0
    },
    children: renderDom()
  });
};
var schemaToDescriptionsItem = function schemaToDescriptionsItem2(items, entity, action, editableUtils, emptyText) {
  var _items$map;
  var options = [];
  var isBigger58 = compareVersions(version_default, "5.8.0") >= 0;
  var children = items === null || items === void 0 || (_items$map = items.map) === null || _items$map === void 0 ? void 0 : _items$map.call(items, function(item, index) {
    var _getDataFromConfig, _restItem$label, _restItem$label2;
    if (import_react6.default.isValidElement(item)) {
      return isBigger58 ? {
        children: item
      } : item;
    }
    var _ref = item, valueEnum = _ref.valueEnum, render = _ref.render, renderText = _ref.renderText, mode = _ref.mode, plain = _ref.plain, dataIndex = _ref.dataIndex, request = _ref.request, params = _ref.params, editable = _ref.editable, restItem = _objectWithoutProperties(_ref, _excluded2);
    var defaultData = (_getDataFromConfig = getDataFromConfig(item, entity)) !== null && _getDataFromConfig !== void 0 ? _getDataFromConfig : restItem.children;
    var text = renderText ? renderText(defaultData, entity, index, action) : defaultData;
    var title = typeof restItem.title === "function" ? restItem.title(item, "descriptions", null) : restItem.title;
    var valueType = typeof restItem.valueType === "function" ? restItem.valueType(entity || {}, "descriptions") : restItem.valueType;
    var isEditable = editableUtils === null || editableUtils === void 0 ? void 0 : editableUtils.isEditable(dataIndex || index);
    var fieldMode = mode || isEditable ? "edit" : "read";
    var showEditIcon = editableUtils && fieldMode === "read" && editable !== false && (editable === null || editable === void 0 ? void 0 : editable(text, entity, index)) !== false;
    var Component = showEditIcon ? space_default : import_react6.default.Fragment;
    var contentDom = fieldMode === "edit" ? text : genCopyable(text, item, text);
    var field = isBigger58 && valueType !== "option" ? _objectSpread2(_objectSpread2({}, restItem), {}, {
      key: restItem.key || ((_restItem$label = restItem.label) === null || _restItem$label === void 0 ? void 0 : _restItem$label.toString()) || index,
      label: (title || restItem.label || restItem.tooltip) && (0, import_jsx_runtime10.jsx)(LabelIconTip, {
        label: title || restItem.label,
        tooltip: restItem.tooltip,
        ellipsis: item.ellipsis
      }),
      children: (0, import_jsx_runtime11.jsxs)(Component, {
        children: [(0, import_react7.createElement)(FieldRender, _objectSpread2(_objectSpread2({}, item), {}, {
          key: item === null || item === void 0 ? void 0 : item.key,
          dataIndex: item.dataIndex || index,
          mode: fieldMode,
          text: contentDom,
          valueType,
          entity,
          index,
          emptyText,
          action,
          editableUtils
        })), showEditIcon && (0, import_jsx_runtime10.jsx)(EditOutlined_default, {
          onClick: function onClick() {
            editableUtils === null || editableUtils === void 0 || editableUtils.startEditable(dataIndex || index);
          }
        })]
      })
    }) : (0, import_react7.createElement)(descriptions_default.Item, _objectSpread2(_objectSpread2({}, restItem), {}, {
      key: restItem.key || ((_restItem$label2 = restItem.label) === null || _restItem$label2 === void 0 ? void 0 : _restItem$label2.toString()) || index,
      label: (title || restItem.label || restItem.tooltip) && (0, import_jsx_runtime10.jsx)(LabelIconTip, {
        label: title || restItem.label,
        tooltip: restItem.tooltip,
        ellipsis: item.ellipsis
      })
    }), (0, import_jsx_runtime11.jsxs)(Component, {
      children: [(0, import_jsx_runtime10.jsx)(FieldRender, _objectSpread2(_objectSpread2({}, item), {}, {
        dataIndex: item.dataIndex || index,
        mode: fieldMode,
        text: contentDom,
        valueType,
        entity,
        index,
        action,
        editableUtils
      })), showEditIcon && valueType !== "option" && (0, import_jsx_runtime10.jsx)(EditOutlined_default, {
        onClick: function onClick() {
          editableUtils === null || editableUtils === void 0 || editableUtils.startEditable(dataIndex || index);
        }
      })]
    }));
    if (valueType === "option") {
      options.push(field);
      return null;
    }
    return field;
  }).filter(function(item) {
    return item;
  });
  return {
    // 空数组传递还是会被判定为有值
    options: options !== null && options !== void 0 && options.length ? options : null,
    children
  };
};
var ProDescriptionsItem = function ProDescriptionsItem2(props) {
  return (0, import_jsx_runtime10.jsx)(descriptions_default.Item, _objectSpread2(_objectSpread2({}, props), {}, {
    children: props.children
  }));
};
ProDescriptionsItem.displayName = "ProDescriptionsItem";
var DefaultProDescriptionsDom = function DefaultProDescriptionsDom2(dom) {
  return dom.children;
};
var ProDescriptions = function ProDescriptions2(props) {
  var _props$editable;
  var request = props.request, columns = props.columns, params = props.params, dataSource = props.dataSource, onDataSourceChange = props.onDataSourceChange, formProps = props.formProps, editable = props.editable, loading = props.loading, onLoadingChange = props.onLoadingChange, actionRef = props.actionRef, onRequestError = props.onRequestError, emptyText = props.emptyText, contentStyle = props.contentStyle, rest = _objectWithoutProperties(props, _excluded22);
  var context = (0, import_react6.useContext)(config_provider_default.ConfigContext);
  var action = useFetchData_default(_asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
    var data;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1)
        switch (_context.prev = _context.next) {
          case 0:
            if (!request) {
              _context.next = 6;
              break;
            }
            _context.next = 3;
            return request(params || {});
          case 3:
            _context.t0 = _context.sent;
            _context.next = 7;
            break;
          case 6:
            _context.t0 = {
              data: {}
            };
          case 7:
            data = _context.t0;
            return _context.abrupt("return", data);
          case 9:
          case "end":
            return _context.stop();
        }
    }, _callee);
  })), {
    onRequestError,
    effects: [stringify_default(params)],
    manual: !request,
    dataSource,
    loading,
    onLoadingChange,
    onDataSourceChange
  });
  var editableUtils = useEditableMap(_objectSpread2(_objectSpread2({}, props.editable), {}, {
    childrenColumnName: void 0,
    dataSource: action.dataSource,
    setDataSource: action.setDataSource
  }));
  (0, import_react6.useEffect)(function() {
    if (actionRef) {
      actionRef.current = _objectSpread2({
        reload: action.reload
      }, editableUtils);
    }
  }, [action, actionRef, editableUtils]);
  if (action.loading || action.loading === void 0 && request) {
    return (0, import_jsx_runtime10.jsx)(es_default3, {
      type: "descriptions",
      list: false,
      pageHeader: false
    });
  }
  var getColumns = function getColumns2() {
    var childrenColumns = toArray(props.children).filter(Boolean).map(function(item) {
      if (!import_react6.default.isValidElement(item)) {
        return item;
      }
      var _ref3 = item === null || item === void 0 ? void 0 : item.props, valueEnum = _ref3.valueEnum, valueType = _ref3.valueType, dataIndex = _ref3.dataIndex, ellipsis = _ref3.ellipsis, copyable = _ref3.copyable, itemRequest = _ref3.request;
      if (!valueType && !valueEnum && !dataIndex && !itemRequest && !ellipsis && !copyable && // @ts-ignore
      item.type.displayName !== "ProDescriptionsItem") {
        return item;
      }
      return _objectSpread2(_objectSpread2({}, item === null || item === void 0 ? void 0 : item.props), {}, {
        entity: dataSource
      });
    });
    return [].concat(_toConsumableArray(columns || []), _toConsumableArray(childrenColumns)).filter(function(item) {
      if (!item)
        return false;
      if (item !== null && item !== void 0 && item.valueType && ["index", "indexBorder"].includes(item === null || item === void 0 ? void 0 : item.valueType)) {
        return false;
      }
      return !(item !== null && item !== void 0 && item.hideInDescriptions);
    }).sort(function(a, b) {
      if (b.order || a.order) {
        return (b.order || 0) - (a.order || 0);
      }
      return (b.index || 0) - (a.index || 0);
    });
  };
  var _schemaToDescriptions = schemaToDescriptionsItem(getColumns(), action.dataSource || {}, (actionRef === null || actionRef === void 0 ? void 0 : actionRef.current) || action, editable ? editableUtils : void 0, props.emptyText), options = _schemaToDescriptions.options, children = _schemaToDescriptions.children;
  var FormComponent = editable ? es_default : DefaultProDescriptionsDom;
  var title = null;
  if (rest.title || rest.tooltip || rest.tip) {
    title = (0, import_jsx_runtime10.jsx)(LabelIconTip, {
      label: rest.title,
      tooltip: rest.tooltip || rest.tip
    });
  }
  var className = context.getPrefixCls("pro-descriptions");
  var isBigger58 = compareVersions(version_default, "5.8.0") >= 0;
  return (0, import_jsx_runtime10.jsx)(ErrorBoundary, {
    children: (0, import_jsx_runtime10.jsx)(FormComponent, _objectSpread2(_objectSpread2({
      form: (_props$editable = props.editable) === null || _props$editable === void 0 ? void 0 : _props$editable.form,
      component: false,
      submitter: false
    }, formProps), {}, {
      onFinish: void 0,
      children: (0, import_jsx_runtime10.jsx)(descriptions_default, _objectSpread2(_objectSpread2({
        className
      }, rest), {}, {
        contentStyle: _objectSpread2({
          minWidth: 0
        }, contentStyle || {}),
        extra: rest.extra ? (0, import_jsx_runtime11.jsxs)(space_default, {
          children: [options, rest.extra]
        }) : options,
        title,
        items: isBigger58 ? children : void 0,
        children: isBigger58 ? null : children
      }))
    }), "form")
  });
};
ProDescriptions.Item = ProDescriptionsItem;

// node_modules/@ant-design/pro-list/es/index.js
init_defineProperty();
var import_classnames3 = __toESM(require_classnames());
var import_react10 = __toESM(require_react());

// node_modules/@ant-design/pro-list/es/ListView.js
var import_classnames2 = __toESM(require_classnames());

// node_modules/@ant-design/pro-list/node_modules/rc-util/es/utils/get.js
function get2(entity, path) {
  var current = entity;
  for (var i = 0; i < path.length; i += 1) {
    if (current === null || current === void 0) {
      return void 0;
    }
    current = current[path[i]];
  }
  return current;
}

// node_modules/@ant-design/pro-list/es/ListView.js
var import_react9 = __toESM(require_react());

// node_modules/@ant-design/pro-list/es/Item.js
init_defineProperty();
var import_classnames = __toESM(require_classnames());

// node_modules/@ant-design/pro-list/node_modules/rc-util/es/hooks/useMergedState.js
var React6 = __toESM(require_react());
function _slicedToArray2(arr, i) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
}
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
  if (!o)
    return;
  if (typeof o === "string")
    return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor)
    n = o.constructor.name;
  if (n === "Map" || n === "Set")
    return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
    return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length)
    len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) {
    arr2[i] = arr[i];
  }
  return arr2;
}
function _iterableToArrayLimit(arr, i) {
  if (typeof Symbol === "undefined" || !(Symbol.iterator in Object(arr)))
    return;
  var _arr = [];
  var _n = true;
  var _d = false;
  var _e = void 0;
  try {
    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {
      _arr.push(_s.value);
      if (i && _arr.length === i)
        break;
    }
  } catch (err) {
    _d = true;
    _e = err;
  } finally {
    try {
      if (!_n && _i["return"] != null)
        _i["return"]();
    } finally {
      if (_d)
        throw _e;
    }
  }
  return _arr;
}
function _arrayWithHoles(arr) {
  if (Array.isArray(arr))
    return arr;
}
function useControlledState(defaultStateValue, option) {
  var _ref = option || {}, defaultValue = _ref.defaultValue, value = _ref.value, onChange = _ref.onChange, postState = _ref.postState;
  var _React$useState = React6.useState(function() {
    if (value !== void 0) {
      return value;
    }
    if (defaultValue !== void 0) {
      return typeof defaultValue === "function" ? defaultValue() : defaultValue;
    }
    return typeof defaultStateValue === "function" ? defaultStateValue() : defaultStateValue;
  }), _React$useState2 = _slicedToArray2(_React$useState, 2), innerValue = _React$useState2[0], setInnerValue = _React$useState2[1];
  var mergedValue = value !== void 0 ? value : innerValue;
  if (postState) {
    mergedValue = postState(mergedValue);
  }
  function triggerChange(newValue) {
    setInnerValue(newValue);
    if (mergedValue !== newValue && onChange) {
      onChange(newValue, mergedValue);
    }
  }
  var firstRenderRef = React6.useRef(true);
  React6.useEffect(function() {
    if (firstRenderRef.current) {
      firstRenderRef.current = false;
      return;
    }
    if (value === void 0) {
      setInnerValue(value);
    }
  }, [value]);
  return [mergedValue, triggerChange];
}

// node_modules/@ant-design/pro-list/es/Item.js
var import_react8 = __toESM(require_react());
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var _excluded3 = ["title", "subTitle", "content", "itemTitleRender", "prefixCls", "actions", "item", "recordKey", "avatar", "cardProps", "description", "isEditable", "checkbox", "index", "selected", "loading", "expand", "onExpand", "expandable", "rowSupportExpand", "showActions", "showExtra", "type", "style", "className", "record", "onRow", "onItem", "itemHeaderRender", "cardActionProps", "extra"];
function renderExpandIcon(_ref) {
  var prefixCls = _ref.prefixCls, _ref$expandIcon = _ref.expandIcon, expandIcon = _ref$expandIcon === void 0 ? (0, import_jsx_runtime12.jsx)(RightOutlined_default, {}) : _ref$expandIcon, onExpand = _ref.onExpand, expanded = _ref.expanded, record = _ref.record, hashId = _ref.hashId;
  var icon = expandIcon;
  var expandClassName = "".concat(prefixCls, "-row-expand-icon");
  var onClick = function onClick2(event) {
    onExpand(!expanded);
    event.stopPropagation();
  };
  if (typeof expandIcon === "function") {
    icon = expandIcon({
      expanded,
      onExpand,
      record
    });
  }
  return (0, import_jsx_runtime12.jsx)("span", {
    className: (0, import_classnames.default)(expandClassName, hashId, _defineProperty(_defineProperty({}, "".concat(prefixCls, "-row-expanded"), expanded), "".concat(prefixCls, "-row-collapsed"), !expanded)),
    onClick,
    children: icon
  });
}
function ProListItem(props) {
  var _ref3, _ref4;
  var customizePrefixCls = props.prefixCls;
  var _useContext = (0, import_react8.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var _useContext2 = (0, import_react8.useContext)(ProProvider), hashId = _useContext2.hashId;
  var prefixCls = getPrefixCls("pro-list", customizePrefixCls);
  var defaultClassName = "".concat(prefixCls, "-row");
  var title = props.title, subTitle = props.subTitle, content = props.content, itemTitleRender = props.itemTitleRender, restPrefixCls = props.prefixCls, actions = props.actions, item = props.item, recordKey = props.recordKey, avatar = props.avatar, cardProps = props.cardProps, description = props.description, isEditable = props.isEditable, checkbox = props.checkbox, index = props.index, selected = props.selected, loading = props.loading, propsExpand = props.expand, propsOnExpand = props.onExpand, expandableConfig = props.expandable, rowSupportExpand = props.rowSupportExpand, showActions = props.showActions, showExtra = props.showExtra, type = props.type, style = props.style, _props$className = props.className, propsClassName = _props$className === void 0 ? defaultClassName : _props$className, record = props.record, onRow = props.onRow, onItem = props.onItem, itemHeaderRender = props.itemHeaderRender, cardActionProps = props.cardActionProps, extra = props.extra, rest = _objectWithoutProperties(props, _excluded3);
  var _ref2 = expandableConfig || {}, expandedRowRender = _ref2.expandedRowRender, expandIcon = _ref2.expandIcon, expandRowByClick = _ref2.expandRowByClick, _ref2$indentSize = _ref2.indentSize, indentSize = _ref2$indentSize === void 0 ? 8 : _ref2$indentSize, expandedRowClassName = _ref2.expandedRowClassName;
  var _useMergedState = useControlledState(!!propsExpand, {
    value: propsExpand,
    onChange: propsOnExpand
  }), _useMergedState2 = _slicedToArray(_useMergedState, 2), expanded = _useMergedState2[0], onExpand = _useMergedState2[1];
  var className = (0, import_classnames.default)(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(defaultClassName, "-selected"), !cardProps && selected), "".concat(defaultClassName, "-show-action-hover"), showActions === "hover"), "".concat(defaultClassName, "-type-").concat(type), !!type), "".concat(defaultClassName, "-editable"), isEditable), "".concat(defaultClassName, "-show-extra-hover"), showExtra === "hover"), hashId, defaultClassName);
  var extraClassName = (0, import_classnames.default)(hashId, _defineProperty({}, "".concat(propsClassName, "-extra"), showExtra === "hover"));
  var needExpanded = expanded || Object.values(expandableConfig || {}).length === 0;
  var expandedRowDom = expandedRowRender && expandedRowRender(record, index, indentSize, expanded);
  var extraDom = (0, import_react8.useMemo)(function() {
    if (!actions || cardActionProps === "actions") {
      return void 0;
    }
    return [(0, import_jsx_runtime12.jsx)("div", {
      onClick: function onClick(e) {
        return e.stopPropagation();
      },
      children: actions
    }, "action")];
  }, [actions, cardActionProps]);
  var actionsDom = (0, import_react8.useMemo)(function() {
    if (!actions || !cardActionProps || cardActionProps === "extra") {
      return void 0;
    }
    return [(0, import_jsx_runtime12.jsx)("div", {
      className: "".concat(defaultClassName, "-actions ").concat(hashId).trim(),
      onClick: function onClick(e) {
        return e.stopPropagation();
      },
      children: actions
    }, "action")];
  }, [actions, cardActionProps, defaultClassName, hashId]);
  var titleDom = title || subTitle ? (0, import_jsx_runtime13.jsxs)("div", {
    className: "".concat(defaultClassName, "-header-container ").concat(hashId).trim(),
    children: [title && (0, import_jsx_runtime12.jsx)("div", {
      className: (0, import_classnames.default)("".concat(defaultClassName, "-title"), hashId, _defineProperty({}, "".concat(defaultClassName, "-title-editable"), isEditable)),
      children: title
    }), subTitle && (0, import_jsx_runtime12.jsx)("div", {
      className: (0, import_classnames.default)("".concat(defaultClassName, "-subTitle"), hashId, _defineProperty({}, "".concat(defaultClassName, "-subTitle-editable"), isEditable)),
      children: subTitle
    })]
  }) : null;
  var metaTitle = (_ref3 = itemTitleRender && (itemTitleRender === null || itemTitleRender === void 0 ? void 0 : itemTitleRender(record, index, titleDom))) !== null && _ref3 !== void 0 ? _ref3 : titleDom;
  var metaDom = metaTitle || avatar || subTitle || description ? (0, import_jsx_runtime12.jsx)(list_default.Item.Meta, {
    avatar,
    title: metaTitle,
    description: description && needExpanded && (0, import_jsx_runtime12.jsx)("div", {
      className: "".concat(className, "-description ").concat(hashId).trim(),
      children: description
    })
  }) : null;
  var rowClassName = (0, import_classnames.default)(hashId, _defineProperty(_defineProperty(_defineProperty({}, "".concat(defaultClassName, "-item-has-checkbox"), checkbox), "".concat(defaultClassName, "-item-has-avatar"), avatar), className, className));
  var cardTitleDom = (0, import_react8.useMemo)(function() {
    if (avatar || title) {
      return (0, import_jsx_runtime13.jsxs)(import_jsx_runtime14.Fragment, {
        children: [avatar, (0, import_jsx_runtime12.jsx)("span", {
          className: "".concat(getPrefixCls("list-item-meta-title"), " ").concat(hashId).trim(),
          children: title
        })]
      });
    }
    return null;
  }, [avatar, getPrefixCls, hashId, title]);
  var itemProps = onItem === null || onItem === void 0 ? void 0 : onItem(record, index);
  var defaultDom = !cardProps ? (0, import_jsx_runtime12.jsx)(list_default.Item, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({
    className: (0, import_classnames.default)(rowClassName, hashId, _defineProperty({}, propsClassName, propsClassName !== defaultClassName))
  }, rest), {}, {
    actions: extraDom,
    extra: !!extra && (0, import_jsx_runtime12.jsx)("div", {
      className: extraClassName,
      children: extra
    })
  }, onRow === null || onRow === void 0 ? void 0 : onRow(record, index)), itemProps), {}, {
    onClick: function onClick(e) {
      var _onRow, _onRow$onClick, _onItem, _onItem$onClick;
      onRow === null || onRow === void 0 || (_onRow = onRow(record, index)) === null || _onRow === void 0 || (_onRow$onClick = _onRow.onClick) === null || _onRow$onClick === void 0 || _onRow$onClick.call(_onRow, e);
      onItem === null || onItem === void 0 || (_onItem = onItem(record, index)) === null || _onItem === void 0 || (_onItem$onClick = _onItem.onClick) === null || _onItem$onClick === void 0 || _onItem$onClick.call(_onItem, e);
      if (expandRowByClick) {
        onExpand(!expanded);
      }
    },
    children: (0, import_jsx_runtime13.jsxs)(skeleton_default, {
      avatar: true,
      title: false,
      loading,
      active: true,
      children: [(0, import_jsx_runtime13.jsxs)("div", {
        className: "".concat(className, "-header ").concat(hashId).trim(),
        children: [(0, import_jsx_runtime13.jsxs)("div", {
          className: "".concat(className, "-header-option ").concat(hashId).trim(),
          children: [!!checkbox && (0, import_jsx_runtime12.jsx)("div", {
            className: "".concat(className, "-checkbox ").concat(hashId).trim(),
            children: checkbox
          }), Object.values(expandableConfig || {}).length > 0 && rowSupportExpand && renderExpandIcon({
            prefixCls,
            hashId,
            expandIcon,
            onExpand,
            expanded,
            record
          })]
        }), (_ref4 = itemHeaderRender && (itemHeaderRender === null || itemHeaderRender === void 0 ? void 0 : itemHeaderRender(record, index, metaDom))) !== null && _ref4 !== void 0 ? _ref4 : metaDom]
      }), needExpanded && (content || expandedRowDom) && (0, import_jsx_runtime13.jsxs)("div", {
        className: "".concat(className, "-content ").concat(hashId).trim(),
        children: [content, expandedRowRender && rowSupportExpand && (0, import_jsx_runtime12.jsx)("div", {
          className: expandedRowClassName && typeof expandedRowClassName !== "string" ? expandedRowClassName(record, index, indentSize) : expandedRowClassName,
          children: expandedRowDom
        })]
      })]
    })
  })) : (0, import_jsx_runtime12.jsx)(CheckCard_default, _objectSpread2(_objectSpread2(_objectSpread2({
    bordered: true,
    style: {
      width: "100%"
    }
  }, cardProps), {}, {
    title: cardTitleDom,
    subTitle,
    extra: extraDom,
    actions: actionsDom,
    bodyStyle: _objectSpread2({
      padding: 24
    }, cardProps.bodyStyle)
  }, itemProps), {}, {
    onClick: function onClick(e) {
      var _cardProps$onClick, _itemProps$onClick;
      cardProps === null || cardProps === void 0 || (_cardProps$onClick = cardProps.onClick) === null || _cardProps$onClick === void 0 || _cardProps$onClick.call(cardProps, e);
      itemProps === null || itemProps === void 0 || (_itemProps$onClick = itemProps.onClick) === null || _itemProps$onClick === void 0 || _itemProps$onClick.call(itemProps, e);
    },
    children: (0, import_jsx_runtime12.jsx)(skeleton_default, {
      avatar: true,
      title: false,
      loading,
      active: true,
      children: (0, import_jsx_runtime13.jsxs)("div", {
        className: "".concat(className, "-header ").concat(hashId).trim(),
        children: [itemTitleRender && (itemTitleRender === null || itemTitleRender === void 0 ? void 0 : itemTitleRender(record, index, titleDom)), content]
      })
    })
  }));
  if (!cardProps) {
    return defaultDom;
  }
  return (0, import_jsx_runtime12.jsx)("div", {
    className: (0, import_classnames.default)(hashId, _defineProperty(_defineProperty({}, "".concat(className, "-card"), cardProps), propsClassName, propsClassName !== defaultClassName)),
    style,
    children: defaultDom
  });
}
var Item_default = ProListItem;

// node_modules/@ant-design/pro-list/es/constants.js
var PRO_LIST_KEYS = ["title", "subTitle", "avatar", "description", "extra", "content", "actions", "type"];
var PRO_LIST_KEYS_MAP = PRO_LIST_KEYS.reduce(function(pre, next) {
  pre.set(next, true);
  return pre;
}, /* @__PURE__ */ new Map());

// node_modules/@ant-design/pro-list/es/ListView.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var _excluded4 = ["dataSource", "columns", "rowKey", "showActions", "showExtra", "prefixCls", "actionRef", "itemTitleRender", "renderItem", "itemCardProps", "itemHeaderRender", "expandable", "rowSelection", "pagination", "onRow", "onItem", "rowClassName"];
function ListView(props) {
  var dataSource = props.dataSource, columns = props.columns, rowKey = props.rowKey, showActions = props.showActions, showExtra = props.showExtra, customizePrefixCls = props.prefixCls, actionRef = props.actionRef, itemTitleRender = props.itemTitleRender, _renderItem = props.renderItem, itemCardProps = props.itemCardProps, itemHeaderRender = props.itemHeaderRender, expandableConfig = props.expandable, rowSelection = props.rowSelection, pagination = props.pagination, onRow = props.onRow, onItem = props.onItem, rowClassName = props.rowClassName, rest = _objectWithoutProperties(props, _excluded4);
  var _useContext = (0, import_react9.useContext)(ProProvider), hashId = _useContext.hashId;
  var _useContext2 = (0, import_react9.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext2.getPrefixCls;
  var getRowKey = import_react9.default.useMemo(function() {
    if (typeof rowKey === "function") {
      return rowKey;
    }
    return function(record, index) {
      return record[rowKey] || index;
    };
  }, [rowKey]);
  var _useLazyKVMap = useLazyKVMap_default(dataSource, "children", getRowKey), _useLazyKVMap2 = _slicedToArray(_useLazyKVMap, 1), getRecordByKey = _useLazyKVMap2[0];
  var usePaginationArgs = [function() {
  }, pagination];
  if (compareVersions(version_default, "5.3.0") < 0)
    usePaginationArgs.reverse();
  var _usePagination = usePagination_default(dataSource.length, usePaginationArgs[0], usePaginationArgs[1]), _usePagination2 = _slicedToArray(_usePagination, 1), mergedPagination = _usePagination2[0];
  var pageData = import_react9.default.useMemo(function() {
    if (pagination === false || !mergedPagination.pageSize || dataSource.length < mergedPagination.total) {
      return dataSource;
    }
    var _mergedPagination$cur = mergedPagination.current, current = _mergedPagination$cur === void 0 ? 1 : _mergedPagination$cur, _mergedPagination$pag = mergedPagination.pageSize, pageSize = _mergedPagination$pag === void 0 ? 10 : _mergedPagination$pag;
    var currentPageData = dataSource.slice((current - 1) * pageSize, current * pageSize);
    return currentPageData;
  }, [dataSource, mergedPagination, pagination]);
  var prefixCls = getPrefixCls("pro-list", customizePrefixCls);
  var useSelectionArgs = [
    {
      getRowKey,
      getRecordByKey,
      prefixCls,
      data: dataSource,
      pageData,
      expandType: "row",
      childrenColumnName: "children",
      locale: {}
    },
    rowSelection
    // 这个 API 用的不好，先 any 一下
  ];
  if (compareVersions(version_default, "5.3.0") < 0)
    useSelectionArgs.reverse();
  var _useSelection = useSelection_default.apply(void 0, useSelectionArgs), _useSelection2 = _slicedToArray(_useSelection, 2), selectItemRender = _useSelection2[0], selectedKeySet = _useSelection2[1];
  var _ref = expandableConfig || {}, expandedRowKeys = _ref.expandedRowKeys, defaultExpandedRowKeys = _ref.defaultExpandedRowKeys, _ref$defaultExpandAll = _ref.defaultExpandAllRows, defaultExpandAllRows = _ref$defaultExpandAll === void 0 ? true : _ref$defaultExpandAll, onExpand = _ref.onExpand, onExpandedRowsChange = _ref.onExpandedRowsChange, rowExpandable = _ref.rowExpandable;
  var _React$useState = import_react9.default.useState(function() {
    if (defaultExpandedRowKeys) {
      return defaultExpandedRowKeys;
    }
    if (defaultExpandAllRows !== false) {
      return dataSource.map(getRowKey);
    }
    return [];
  }), _React$useState2 = _slicedToArray(_React$useState, 2), innerExpandedKeys = _React$useState2[0], setInnerExpandedKeys = _React$useState2[1];
  var mergedExpandedKeys = import_react9.default.useMemo(function() {
    return new Set(expandedRowKeys || innerExpandedKeys || []);
  }, [expandedRowKeys, innerExpandedKeys]);
  var onTriggerExpand = import_react9.default.useCallback(function(record) {
    var key = getRowKey(record, dataSource.indexOf(record));
    var newExpandedKeys;
    var hasKey = mergedExpandedKeys.has(key);
    if (hasKey) {
      mergedExpandedKeys.delete(key);
      newExpandedKeys = _toConsumableArray(mergedExpandedKeys);
    } else {
      newExpandedKeys = [].concat(_toConsumableArray(mergedExpandedKeys), [key]);
    }
    setInnerExpandedKeys(newExpandedKeys);
    if (onExpand) {
      onExpand(!hasKey, record);
    }
    if (onExpandedRowsChange) {
      onExpandedRowsChange(newExpandedKeys);
    }
  }, [getRowKey, mergedExpandedKeys, dataSource, onExpand, onExpandedRowsChange]);
  var selectItemDom = selectItemRender([])[0];
  return (0, import_jsx_runtime15.jsx)(list_default, _objectSpread2(_objectSpread2({}, rest), {}, {
    className: (0, import_classnames2.default)(getPrefixCls("pro-list-container", customizePrefixCls), hashId, rest.className),
    dataSource: pageData,
    pagination: pagination && mergedPagination,
    renderItem: function renderItem(item, index) {
      var _actionRef$current;
      var listItemProps = {
        className: typeof rowClassName === "function" ? rowClassName(item, index) : rowClassName
      };
      columns === null || columns === void 0 || columns.forEach(function(column) {
        var listKey = column.listKey, cardActionProps = column.cardActionProps;
        if (!PRO_LIST_KEYS_MAP.has(listKey)) {
          return;
        }
        var dataIndex = column.dataIndex || listKey || column.key;
        var rawData = Array.isArray(dataIndex) ? get2(item, dataIndex) : item[dataIndex];
        if (cardActionProps === "actions" && listKey === "actions") {
          listItemProps.cardActionProps = cardActionProps;
        }
        var data = column.render ? column.render(rawData, item, index) : rawData;
        if (data !== "-")
          listItemProps[column.listKey] = data;
      });
      var checkboxDom;
      if (selectItemDom && selectItemDom.render) {
        checkboxDom = selectItemDom.render(item, item, index);
      }
      var _ref2 = ((_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 ? void 0 : _actionRef$current.isEditable(_objectSpread2(_objectSpread2({}, item), {}, {
        index
      }))) || {}, isEditable = _ref2.isEditable, recordKey = _ref2.recordKey;
      var isChecked = selectedKeySet.has(recordKey || index);
      var defaultDom = (0, import_jsx_runtime15.jsx)(Item_default, _objectSpread2(_objectSpread2({
        cardProps: rest.grid ? _objectSpread2(_objectSpread2(_objectSpread2({}, itemCardProps), rest.grid), {}, {
          checked: isChecked,
          onChange: import_react9.default.isValidElement(checkboxDom) ? function(changeChecked) {
            var _checkboxDom;
            return (_checkboxDom = checkboxDom) === null || _checkboxDom === void 0 || (_checkboxDom = _checkboxDom.props) === null || _checkboxDom === void 0 ? void 0 : _checkboxDom.onChange({
              nativeEvent: {},
              changeChecked
            });
          } : void 0
        }) : void 0
      }, listItemProps), {}, {
        recordKey,
        isEditable: isEditable || false,
        expandable: expandableConfig,
        expand: mergedExpandedKeys.has(getRowKey(item, index)),
        onExpand: function onExpand2() {
          onTriggerExpand(item);
        },
        index,
        record: item,
        item,
        showActions,
        showExtra,
        itemTitleRender,
        itemHeaderRender,
        rowSupportExpand: !rowExpandable || rowExpandable && rowExpandable(item),
        selected: selectedKeySet.has(getRowKey(item, index)),
        checkbox: checkboxDom,
        onRow,
        onItem
      }), recordKey);
      if (_renderItem) {
        return _renderItem(item, index, defaultDom);
      }
      return defaultDom;
    }
  }));
}
var ListView_default = ListView;

// node_modules/@ant-design/pro-list/es/style/index.js
init_defineProperty();
var techUiListActive = new Keyframes_default("techUiListActive", {
  "0%": {
    backgroundColor: "unset"
  },
  "30%": {
    background: "#fefbe6"
  },
  "100%": {
    backgroundColor: "unset"
  }
});
var genProListStyle = function genProListStyle2(token) {
  var _row;
  return _defineProperty({}, token.componentCls, _defineProperty(_defineProperty({
    backgroundColor: "transparent"
  }, "".concat(token.proComponentsCls, "-table-alert"), {
    marginBlockEnd: "16px"
  }), "&-row", (_row = {
    borderBlockEnd: "1px solid ".concat(token.colorSplit)
  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_row, "".concat(token.antCls, "-list-item-meta-title"), {
    borderBlockEnd: "none",
    margin: 0
  }), "&:last-child", _defineProperty({
    borderBlockEnd: "none"
  }, "".concat(token.antCls, "-list-item"), {
    borderBlockEnd: "none"
  })), "&:hover", _defineProperty(_defineProperty(_defineProperty(_defineProperty({
    backgroundColor: "rgba(0, 0, 0, 0.02)",
    transition: "background-color 0.3s"
  }, "".concat(token.antCls, "-list-item-action"), {
    display: "block"
  }), "".concat(token.antCls, "-list-item-extra"), {
    display: "flex"
  }), "".concat(token.componentCls, "-row-extra"), {
    display: "block"
  }), "".concat(token.componentCls, "-row-subheader-actions"), {
    display: "block"
  })), "&-card", _defineProperty({
    marginBlock: 8,
    marginInline: 0,
    paddingBlock: 0,
    paddingInline: 8,
    "&:hover": {
      backgroundColor: "transparent"
    }
  }, "".concat(token.antCls, "-list-item-meta-title"), {
    flexShrink: 9,
    marginBlock: 0,
    marginInline: 0,
    lineHeight: "22px"
  })), "&".concat(token.componentCls, "-row-editable"), _defineProperty({}, "".concat(token.componentCls, "-list-item"), {
    "&-meta": {
      "&-avatar,&-description,&-title": {
        paddingBlock: 6,
        paddingInline: 0,
        "&-editable": {
          paddingBlock: 0
        }
      }
    },
    "&-action": {
      display: "block"
    }
  })), "&".concat(token.componentCls, "-row-selected"), {
    backgroundColor: token.colorPrimaryBgHover,
    "&:hover": {
      backgroundColor: token.colorPrimaryBgHover
    }
  }), "&".concat(token.componentCls, "-row-type-new"), {
    animationName: techUiListActive,
    animationDuration: "3s"
  }), "&".concat(token.componentCls, "-row-type-inline"), _defineProperty({}, "".concat(token.componentCls, "-row-title"), {
    fontWeight: "normal"
  })), "&".concat(token.componentCls, "-row-type-top"), {
    backgroundImage: "url('https://gw.alipayobjects.com/zos/antfincdn/DehQfMbOJb/icon.svg')",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "left top",
    backgroundSize: "12px 12px"
  }), "&-show-action-hover", _defineProperty({}, "".concat(token.antCls, "-list-item-action,\n            ").concat(token.proComponentsCls, "-card-extra,\n            ").concat(token.proComponentsCls, "-card-actions"), {
    display: "flex"
  })), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_row, "&-show-extra-hover", _defineProperty({}, "".concat(token.antCls, "-list-item-extra"), {
    display: "none"
  })), "&-extra", {
    display: "none"
  }), "&-subheader", {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    height: "44px",
    paddingInline: 24,
    paddingBlock: 0,
    color: token.colorTextSecondary,
    lineHeight: "44px",
    background: "rgba(0, 0, 0, 0.02)",
    "&-actions": {
      display: "none"
    },
    "&-actions *": {
      marginInlineEnd: 8,
      "&:last-child": {
        marginInlineEnd: 0
      }
    }
  }), "&-expand-icon", {
    marginInlineEnd: 8,
    display: "flex",
    fontSize: 12,
    cursor: "pointer",
    height: "24px",
    marginRight: 4,
    color: token.colorTextSecondary,
    "> .anticon > svg": {
      transition: "0.3s"
    }
  }), "&-expanded", {
    " > .anticon > svg": {
      transform: "rotate(90deg)"
    }
  }), "&-title", {
    marginInlineEnd: "16px",
    wordBreak: "break-all",
    cursor: "pointer",
    "&-editable": {
      paddingBlock: 8
    },
    "&:hover": {
      color: token.colorPrimary
    }
  }), "&-content", {
    position: "relative",
    display: "flex",
    flex: "1",
    flexDirection: "column",
    marginBlock: 0,
    marginInline: 32
  }), "&-subTitle", {
    color: "rgba(0, 0, 0, 0.45)",
    "&-editable": {
      paddingBlock: 8
    }
  }), "&-description", {
    marginBlockStart: "4px",
    wordBreak: "break-all"
  }), "&-avatar", {
    display: "flex"
  }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_row, "&-header", {
    display: "flex",
    flex: "1",
    justifyContent: "flex-start",
    h4: {
      margin: 0,
      padding: 0
    }
  }), "&-header-container", {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-start"
  }), "&-header-option", {
    display: "flex"
  }), "&-checkbox", {
    width: "16px",
    marginInlineEnd: "12px"
  }), "&-no-split", _defineProperty(_defineProperty({}, "".concat(token.componentCls, "-row"), {
    borderBlockEnd: "none"
  }), "".concat(token.antCls, "-list ").concat(token.antCls, "-list-item"), {
    borderBlockEnd: "none"
  })), "&-bordered", _defineProperty({}, "".concat(token.componentCls, "-toolbar"), {
    borderBlockEnd: "1px solid ".concat(token.colorSplit)
  })), "".concat(token.antCls, "-list-vertical"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(token.componentCls, "-row"), {
    borderBlockEnd: "12px 18px 12px 24px"
  }), "&-header-title", {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    justifyContent: "center"
  }), "&-content", {
    marginBlock: 0,
    marginInline: 0
  }), "&-subTitle", {
    marginBlockStart: 8
  }), "".concat(token.antCls, "-list-item-extra"), _defineProperty({
    display: "flex",
    alignItems: "center",
    marginInlineStart: "32px"
  }, "".concat(token.componentCls, "-row-description"), {
    marginBlockStart: 16
  })), "".concat(token.antCls, "-list-bordered ").concat(token.antCls, "-list-item"), {
    paddingInline: 0
  }), "".concat(token.componentCls, "-row-show-extra-hover"), _defineProperty({}, "".concat(token.antCls, "-list-item-extra "), {
    display: "none"
  }))), "".concat(token.antCls, "-list-pagination"), {
    marginBlockStart: token.margin,
    marginBlockEnd: token.margin
  }), "".concat(token.antCls, "-list-list"), {
    "&-item": {
      cursor: "pointer",
      paddingBlock: 12,
      paddingInline: 12
    }
  }), "".concat(token.antCls, "-list-vertical ").concat(token.proComponentsCls, "-list-row"), _defineProperty({
    "&-header": {
      paddingBlock: 0,
      paddingInline: 0,
      borderBlockEnd: "none"
    }
  }, "".concat(token.antCls, "-list-item"), _defineProperty(_defineProperty(_defineProperty({
    width: "100%",
    paddingBlock: 12,
    paddingInlineStart: 24,
    paddingInlineEnd: 18
  }, "".concat(token.antCls, "-list-item-meta-avatar"), {
    display: "flex",
    alignItems: "center",
    marginInlineEnd: 8
  }), "".concat(token.antCls, "-list-item-action-split"), {
    display: "none"
  }), "".concat(token.antCls, "-list-item-meta-title"), {
    marginBlock: 0,
    marginInline: 0
  }))))));
};
function useStyle2(prefixCls) {
  return useStyle("ProList", function(token) {
    var proListToken = _objectSpread2(_objectSpread2({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProListStyle(proListToken)];
  });
}

// node_modules/@ant-design/pro-list/es/index.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var _excluded5 = ["metas", "split", "footer", "rowKey", "tooltip", "className", "options", "search", "expandable", "showActions", "showExtra", "rowSelection", "pagination", "itemLayout", "renderItem", "grid", "itemCardProps", "onRow", "onItem", "rowClassName", "locale", "itemHeaderRender", "itemTitleRender"];
function NoProVideProList(props) {
  var metals = props.metas, split = props.split, footer = props.footer, rowKey = props.rowKey, tooltip = props.tooltip, className = props.className, _props$options = props.options, options = _props$options === void 0 ? false : _props$options, _props$search = props.search, search = _props$search === void 0 ? false : _props$search, expandable = props.expandable, showActions = props.showActions, showExtra = props.showExtra, _props$rowSelection = props.rowSelection, propRowSelection = _props$rowSelection === void 0 ? false : _props$rowSelection, _props$pagination = props.pagination, propsPagination = _props$pagination === void 0 ? false : _props$pagination, itemLayout = props.itemLayout, renderItem = props.renderItem, grid = props.grid, itemCardProps = props.itemCardProps, onRow = props.onRow, onItem = props.onItem, rowClassName = props.rowClassName, locale = props.locale, itemHeaderRender = props.itemHeaderRender, itemTitleRender = props.itemTitleRender, rest = _objectWithoutProperties(props, _excluded5);
  var actionRef = (0, import_react10.useRef)();
  (0, import_react10.useImperativeHandle)(rest.actionRef, function() {
    return actionRef.current;
  }, [actionRef.current]);
  var _useContext = (0, import_react10.useContext)(config_provider_default.ConfigContext), getPrefixCls = _useContext.getPrefixCls;
  var proTableColumns = (0, import_react10.useMemo)(function() {
    var columns = [];
    Object.keys(metals || {}).forEach(function(key) {
      var meta = metals[key] || {};
      var valueType = meta.valueType;
      if (!valueType) {
        if (key === "avatar") {
          valueType = "avatar";
        }
        if (key === "actions") {
          valueType = "option";
        }
        if (key === "description") {
          valueType = "textarea";
        }
      }
      columns.push(_objectSpread2(_objectSpread2({
        listKey: key,
        dataIndex: (meta === null || meta === void 0 ? void 0 : meta.dataIndex) || key
      }, meta), {}, {
        valueType
      }));
    });
    return columns;
  }, [metals]);
  var prefixCls = getPrefixCls("pro-list", props.prefixCls);
  var _useStyle = useStyle2(prefixCls), wrapSSR = _useStyle.wrapSSR, hashId = _useStyle.hashId;
  var listClassName = (0, import_classnames3.default)(prefixCls, hashId, _defineProperty({}, "".concat(prefixCls, "-no-split"), !split));
  return wrapSSR((0, import_jsx_runtime16.jsx)(es_default2, _objectSpread2(_objectSpread2({
    tooltip
  }, rest), {}, {
    actionRef,
    pagination: propsPagination,
    type: "list",
    rowSelection: propRowSelection,
    search,
    options,
    className: (0, import_classnames3.default)(prefixCls, className, listClassName),
    columns: proTableColumns,
    rowKey,
    tableViewRender: function tableViewRender(_ref) {
      var columns = _ref.columns, size = _ref.size, pagination = _ref.pagination, rowSelection = _ref.rowSelection, dataSource = _ref.dataSource, loading = _ref.loading;
      return (0, import_jsx_runtime16.jsx)(ListView_default, {
        grid,
        itemCardProps,
        itemTitleRender,
        prefixCls: props.prefixCls,
        columns,
        renderItem,
        actionRef,
        dataSource: dataSource || [],
        size,
        footer,
        split,
        rowKey,
        expandable,
        rowSelection: propRowSelection === false ? void 0 : rowSelection,
        showActions,
        showExtra,
        pagination,
        itemLayout,
        loading,
        itemHeaderRender,
        onRow,
        onItem,
        rowClassName,
        locale
      });
    }
  })));
}
function BaseProList(props) {
  return (0, import_jsx_runtime16.jsx)(ProConfigProvider, {
    needDeps: true,
    children: (0, import_jsx_runtime16.jsx)(NoProVideProList, _objectSpread2({
      cardProps: false,
      search: false,
      toolBarRender: false
    }, props))
  });
}
function ProList(props) {
  return (0, import_jsx_runtime16.jsx)(ProConfigProvider, {
    needDeps: true,
    children: (0, import_jsx_runtime16.jsx)(NoProVideProList, _objectSpread2({}, props))
  });
}

// node_modules/@ant-design/pro-components/es/version.js
var version = {
  "@ant-design/pro-card": "2.9.6",
  "@ant-design/pro-components": "2.8.6",
  "@ant-design/pro-descriptions": "2.6.6",
  "@ant-design/pro-field": "3.0.3",
  "@ant-design/pro-form": "2.31.6",
  "@ant-design/pro-layout": "7.22.3",
  "@ant-design/pro-list": "2.6.6",
  "@ant-design/pro-provider": "2.15.3",
  "@ant-design/pro-skeleton": "2.2.1",
  "@ant-design/pro-table": "3.18.6",
  "@ant-design/pro-utils": "2.16.4"
};
export {
  BaseProList,
  SchemaForm_default as BetaSchemaForm,
  CellEditorTable,
  CheckCard_default as CheckCard,
  ConfigConsumer,
  DefaultFooter,
  DefaultHeader,
  DescriptionsSkeleton,
  DragSortTable_default as DragSortTable,
  DrawerForm,
  DropdownFooter,
  EditableTable_default as EditableProTable,
  ErrorBoundary,
  Code_default as FieldCode,
  FieldContext,
  DatePicker_default as FieldDatePicker,
  IndexColumn_default as FieldIndexColumn,
  FieldLabel,
  Money_default as FieldMoney,
  Percent_default as FieldPercent,
  Progress_default as FieldProgress,
  RangePicker_default as FieldRangePicker,
  FieldRender,
  Select_default as FieldSelect,
  Status_default as FieldStatus,
  Text_default as FieldText,
  TimePicker_default as FieldTimePicker,
  FilterDropdown,
  FooterToolbar,
  FormControlRender,
  FormItemProvide,
  FormItemRender,
  FormListContext,
  GridContent,
  GridContext,
  Group_default as Group,
  IndexColumn_default as IndexColumn,
  InlineErrorFormItem,
  ConfigConsumer as IntlConsumer,
  LabelIconTip,
  LightFilter,
  List_default as ListPageSkeleton,
  ListSkeleton,
  ListSkeletonItem,
  ListToolBar_default as ListToolBar,
  ListToolbarSkeleton,
  LoginForm,
  LoginFormPage,
  ModalForm,
  PageContainer,
  PageHeader,
  PageHeaderSkeleton,
  PageLoading,
  ProBreadcrumb,
  ProCard_default as ProCard,
  ProConfigProvider,
  ProDescriptions,
  ProField,
  ProForm,
  Captcha_default as ProFormCaptcha,
  Cascader_default as ProFormCascader,
  Checkbox_default as ProFormCheckbox,
  ColorPicker_default as ProFormColorPicker,
  ProFormContext,
  DateMonthRangePicker_default as ProFormDateMonthRangePicker,
  DatePicker_default2 as ProFormDatePicker,
  DateQuarterRangePicker_default as ProFormDateQuarterRangePicker,
  DateRangePicker_default as ProFormDateRangePicker,
  DateTimePicker_default as ProFormDateTimePicker,
  DateTimeRangePicker_default as ProFormDateTimeRangePicker,
  DateWeekRangePicker_default as ProFormDateWeekRangePicker,
  DateYearRangePicker_default as ProFormDateYearRangePicker,
  Dependency_default as ProFormDependency,
  Digit_default as ProFormDigit,
  DigitRange_default as ProFormDigitRange,
  Field_default as ProFormField,
  FieldSet_default as ProFormFieldSet,
  ProFormGroup,
  FormItem_default as ProFormItem,
  ProFormItemRender,
  ProFormList,
  Money_default2 as ProFormMoney,
  Radio_default as ProFormRadio,
  Rate_default as ProFormRate,
  Segmented_default as ProFormSegmented,
  Select_default2 as ProFormSelect,
  Slider_default as ProFormSlider,
  Switch_default as ProFormSwitch,
  Text_default2 as ProFormText,
  TextArea_default as ProFormTextArea,
  TimePicker_default2 as ProFormTimePicker,
  TreeSelect_default as ProFormTreeSelect,
  UploadButton_default as ProFormUploadButton,
  UploadDragger_default as ProFormUploadDragger,
  ProHelp,
  ProHelpContentPanel,
  ProHelpDrawer,
  ProHelpModal,
  ProHelpPanel,
  ProHelpPopover,
  ProHelpProvide,
  ProHelpSelect,
  ProLayout,
  ProList,
  ProPageHeader,
  ProProvider,
  ProSkeleton,
  Table_default as ProTable,
  QueryFilter,
  RenderContentPanel,
  RouteContext,
  RowEditorTable,
  Form_default as Search,
  SelectKeyProvide,
  SettingDrawer,
  Statistic_default as Statistic,
  StatisticCard_default as StatisticCard,
  StepsFormWarp as StepsForm,
  Submitter_default as Submitter,
  Dropdown_default as TableDropdown,
  TableItemSkeleton,
  TableSkeleton,
  Status_default as TableStatus,
  TopNavHeader,
  WaterMark,
  arEGIntl,
  caESIntl,
  compareVersions,
  compatibleBorder,
  conversionMomentValue,
  conversionMomentValue as conversionSubmitValue,
  convertMoment,
  coverToNewToken,
  createIntl,
  csCZIntl,
  dateArrayFormatter,
  dateFormatterMap,
  deDEIntl,
  defaultRenderText,
  editableRowByKey,
  enGBIntl,
  enUSIntl,
  esESIntl,
  faIRIntl,
  findIntlKeyByAntdLocaleKey,
  frFRIntl,
  genCopyable,
  getFieldPropsOrFormItemProps,
  getMenuData,
  getPageTitle,
  heILIntl,
  hrHRIntl,
  idIDIntl,
  intlMap,
  intlMapKeys,
  isBrowser,
  isDeepEqualReact,
  isDropdownValueType,
  isImg,
  isNeedOpenHash,
  isNil,
  isUrl,
  itITIntl,
  jaJPIntl,
  koKRIntl,
  lighten,
  menuOverlayCompatible,
  merge,
  mnMNIntl,
  msMYIntl,
  nanoid,
  nlNLIntl,
  objectToMap,
  omitBoolean,
  omitUndefined,
  omitUndefinedAndEmptyArr,
  openVisibleCompatible,
  operationUnit,
  parseValueToDay,
  pickControlProps,
  pickControlPropsWithId,
  pickProFormItemProps,
  pickProProps,
  plPLIntl,
  proFieldParsingText,
  proFieldParsingValueEnumToArray,
  proTheme,
  ptBRIntl,
  recordKeyToString,
  resetComponent,
  roROIntl,
  ruRUIntl,
  runFunction,
  setAlpha,
  skSKIntl,
  srRSIntl,
  stringify_default as stringify,
  svSEIntl,
  thTHIntl,
  trTRIntl,
  transformKeySubmitValue,
  ukUAIntl,
  useBreakpoint,
  useControlModel,
  useDebounceFn,
  useDebounceValue,
  useDeepCompareEffect,
  useDeepCompareEffectDebounce,
  useDeepCompareMemo_default as useDeepCompareMemo,
  useDocumentTitle,
  useEditableArray,
  useEditableMap,
  useFetchData,
  useIntl,
  useLatest,
  useMergedState as useMountMergeState,
  usePrevious,
  useReactiveRef,
  useRefCallback,
  useRefFunction,
  useStyle,
  useToken,
  uzUZIntl,
  version,
  viVNIntl,
  zhCNIntl,
  zhTWIntl
};
//# sourceMappingURL=@ant-design_pro-components.js.map
